{"root": true, "ignorePatterns": ["projects/**/*"], "overrides": [{"files": ["*.ts"], "extends": ["eslint:recommended", "@typescript-eslint/recommended", "@angular-eslint/recommended", "@angular-eslint/template/process-inline-templates"], "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "app", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "app", "style": "kebab-case"}], "@typescript-eslint/naming-convention": ["error", {"selector": "default", "format": ["camelCase"]}, {"selector": "variable", "format": ["camelCase", "UPPER_CASE"]}, {"selector": "parameter", "format": ["camelCase"], "leadingUnderscore": "allow"}, {"selector": "memberLike", "modifiers": ["private"], "format": ["camelCase"], "leadingUnderscore": "require"}, {"selector": "typeLike", "format": ["PascalCase"]}, {"selector": "property", "modifiers": ["readonly"], "format": ["camelCase", "UPPER_CASE"]}, {"selector": "enumMember", "format": ["UPPER_CASE"]}]}}, {"files": ["*.html"], "extends": ["@angular-eslint/template/recommended"], "rules": {}}]}