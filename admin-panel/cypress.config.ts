const {defineConfig} = require('cypress');
const {addCucumberPreprocessorPlugin} = require('@badeball/cypress-cucumber-preprocessor');
const browserify = require('@badeball/cypress-cucumber-preprocessor/browserify');
const fs = require('fs');

module.exports = defineConfig({
    video: true,
    videosFolder: 'cypress/videos',
    screenshotsFolder: 'cypress/screenshots',
    fixturesFolder: 'cypress/fixtures',
    downloadsFolder: 'cypress/downloads',
    viewportWidth: 1024,
    viewportHeight: 768,
    defaultCommandTimeout: 10000,
    projectId: 'y76w3w',

    e2e: {
        baseUrl: 'http://localhost:4200',
        specPattern: '**/*.feature',
        supportFile: false,
        async setupNodeEvents(on, config) {
            await addCucumberPreprocessorPlugin(on, config);

            on(
                'file:preprocessor',
                browserify.preprocessor(config, {
                    typescript: require.resolve('typescript'),
                })
            );

            on('task', {
                log(message) {
                    console.log(message)

                    return null
                },
                readMatchingFile({ directory, regex }) {
                    regex = new RegExp(regex);
                    const files = fs.readdirSync(directory);
                    const matchingFile = files.find(file => regex.test(file));

                    if (matchingFile) {
                        const filePath = `${directory}/${matchingFile}`;
                        return fs.readFileSync(filePath, 'utf8');
                    } else {
                        throw new Error(`No file matching regex ${regex} found in directory ${directory}`);
                    }
                }
            });

            on('before:browser:launch', (browser, launchOptions) => {
                if (browser.name === 'chrome' && browser.isHeadless) {
                    launchOptions.args.push('--window-size=1600,1000')
                    launchOptions.args.push('--force-device-scale-factor=1')
                }
                if (browser.name === 'electron' && browser.isHeadless) {
                    launchOptions.preferences.width = 1600
                    launchOptions.preferences.height = 1000
                }

                return launchOptions;
            });

            return config;
        },
        // experimentalOriginDependencies: true,
        experimentalRunAllSpecs: true
    }
});
