{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "esModuleInterop": true, "experimentalDecorators": true, "module": "es2022", "moduleResolution": "node", "importHelpers": true, "target": "es2022", "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom"], "paths": {"@test/*": ["test/*"], "@shared/*": ["src/app/shared/*"], "@mocks/*": ["src/app/shared/mocks/*"], "@pipes/*": ["src/app/shared/pipes/*"], "@enums/*": ["src/app/shared/enums/*"], "@models/*": ["src/app/shared/models/*"], "@modules/*": ["src/app/shared/modules/*"], "@services/*": ["src/app/shared/services/*"], "@decorators/*": ["src/app/shared/decorators/*"], "@directives/*": ["src/app/shared/directives/*"], "@core/*": ["src/app/core/*"], "@http": ["src/app/core/http"], "@constants/*": ["src/app/core/constants/*"], "@dataServices/*": ["src/app/core/data-services/*"], "@layouts/*": ["src/app/layouts/*"], "@config": ["src/app/config/config"], "@homeModels/*": ["src/app/feature/home/<USER>/models/*"]}}, "angularCompilerOptions": {"fullTemplateTypeCheck": true, "strictInjectionParameters": true}, "exclude": ["node_modules", "cypress", "cypress.config.ts"]}