{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"admin-panel": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": {"base": "dist/admin-panel"}, "index": "src/index.html", "polyfills": ["src/polyfills.ts"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/bootstrap/dist/css/bootstrap.min.css", "src/styles.scss"], "scripts": ["./node_modules/reflect-metadata/Reflect.js"], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "src/main.ts"}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "noAuthentication": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.noAuth.ts"}]}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"buildTarget": "admin-panel:build"}, "configurations": {"production": {"buildTarget": "admin-panel:build:production"}, "noAuthentication": {"buildTarget": "admin-panel:build:noAuthentication"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "admin-panel:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/bootstrap/dist/css/bootstrap.min.css", "src/styles.scss"], "scripts": [], "codeCoverage": true, "codeCoverageExclude": ["./**/*.harness.ts"]}}, "e2e": {"builder": "@cypress/schematic:cypress", "options": {"devServerTarget": "admin-panel:serve:noAuthentication", "watch": true, "headless": false}}, "cypress-run": {"builder": "@cypress/schematic:cypress", "options": {"devServerTarget": "admin-panel:serve:noAuthentication"}}, "cypress-open": {"builder": "@cypress/schematic:cypress", "options": {"watch": true, "headless": false}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"analytics": "3ce1f8ef-fa78-4838-8629-2e6790492ec5"}}