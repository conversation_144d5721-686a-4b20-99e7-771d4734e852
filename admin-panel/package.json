{"name": "admin-panel", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "cypress:open": "cypress open", "cypress:run": "cypress run"}, "cypress-cucumber-preprocessor": {"step_definitions": "./cypress/steps"}, "resolutions": {"webpack": "^5.0.0"}, "private": true, "dependencies": {"@angular/animations": "^19.2.14", "@angular/cdk": "^19.2.19", "@angular/common": "^19.2.14", "@angular/compiler": "^19.2.14", "@angular/core": "^19.2.14", "@angular/forms": "^19.2.14", "@angular/material": "^19.2.19", "@angular/platform-browser": "^19.2.14", "@angular/platform-browser-dynamic": "^19.2.14", "@angular/router": "^19.2.14", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "angular-imask": "^6.0.5", "base64-js": "^1.5.1", "bootstrap": "^4.5.0", "mb-date": "1.0.15", "mb-keycode": "^1.0.3", "ngx-bootstrap": "^19.0.2", "reflect-metadata": "^0.1.13", "rxjs": "~6.5.4", "tslib": "^2.0.0", "zone.js": "~0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.15", "@angular/cli": "^19.2.15", "@angular/compiler-cli": "^19.2.14", "@angular/language-service": "^19.2.14", "@badeball/cypress-cucumber-preprocessor": "^20.0.1", "@bahmutov/cypress-esbuild-preprocessor": "^2.1.5", "@cypress/browserify-preprocessor": "^3.0.2", "@cypress/schematic": "^1.5.1", "@types/base64-js": "^1.3.0", "@types/cucumber": "^6.0.1", "@types/jasmine": "^4.3.1", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.12.50", "chai": "^4.2.0", "@angular-eslint/builder": "^18.0.0", "@angular-eslint/eslint-plugin": "^18.0.0", "@angular-eslint/eslint-plugin-template": "^18.0.0", "@angular-eslint/schematics": "^18.0.0", "@angular-eslint/template-parser": "^18.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^9.0.0", "cypress": "^13.16.0", "jasmine-core": "^4.5.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.3.9", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^1.5.0", "karma-junit-reporter": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "ts-node": "^8.3.0", "typescript": "~5.5.4"}}