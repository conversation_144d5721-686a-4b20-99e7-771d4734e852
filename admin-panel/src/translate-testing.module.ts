import {TranslateLoader, TranslateModule, TranslateService} from '@ngx-translate/core';
import {TranslateCustomLoader} from './translate-custom-loader';
import {NgModule} from '@angular/core';
import {LanguageLayout} from '@enums/language-layout';

@NgModule({
    imports: [
        TranslateModule.forRoot({
            loader: {
                provide: TranslateLoader,
                useClass: TranslateCustomLoader
            }
        })
    ],
    exports: [TranslateModule]
})
export class TranslateTestingModule {
    constructor(translateService: TranslateService) {
        translateService.use(LanguageLayout.en);
    }
}
