{"signIn": "ورود", "username": "نام کاربری", "password": "گذرواژه", "usernameIsRequired": "وارد کردن نام کاربری اجباری است", "passwordIsRequired": "وارد کردن گذرواژه اجباری است", "forgotPassword": "گذرواژه خود را فراموش کرده‌اید؟", "userOrPassIsIncorrect": "نام کاربری یا گذرواژه اشتباه است", "collapseMenu": "بستن منو", "cancel": "لغو", "ok": "تأیید", "search": "جستجو", "searchEntity": "انتخاب موجودیت", "commands": "فرمان‌ها", "group": "گروه", "trader": "معامله‌گر", "traders": "معامله‌گرها", "traderCalendar": "تقویم روزهای معامله", "variableTickTable": "جدول قیمت پایه", "authorizationMatrix": "تنظیم دسترسی‌ها", "more": "بیشتر", "profile": "حساب کاربری", "changePassword": "تغییر گذرواژه", "history": "تاریخچه", "commandHistory": "تاریخچه فرمان‌ها", "marketEventHistory": "تاریخچه رویدادهای بازار", "settings": "تنظیمات", "marketEventFilter": "فیلتر رویدادهای بازار", "fileEditor": "ویرایشگر فایل", "logout": "خروج", "publicErrorWithCode": "خطای {{errorCode}} رخ داده است، با پشتیبانی تماس بگیرید", "language": "زبان", "english": "انگلیسی", "persian": "فار<PERSON>ی", "frozenInstruments": "نمادهای بسته‌شده", "menu": "منو", "both": "هر دو", "buy": "<PERSON><PERSON><PERSON><PERSON>", "sell": "فروش", "crossOrder": "سفارش دوطرفه", "broker": "کارگزار", "buyBroker": "کارگزا<PERSON> خرید", "buyShareholder": "سهامدار", "sellBroker": "کارگزار فروش", "brokers": "کارگزارها", "isRequired": "وارد کردن {{value}} اجباری است", "price": "قیمت", "buyPrice": "قی<PERSON><PERSON> خرید", "sellPrice": "قیمت فروش", "quantity": "حج<PERSON>", "displayedQuantity": "حجم نمایشی", "buyDisplayedQuantity": "حجم نمایشی", "totalQuantity": "حجم کل", "buyTotalQuantity": "حجم کل", "disclosedQuantity": "حجم آشکار", "orderType": "نوع سفارش", "limit": "م<PERSON><PERSON><PERSON><PERSON>", "market": "بازار", "validityQualifierType": "زمان اعتبار", "fillAndKill": "انجام و حذف", "fill_and_kill": "انجام و حذف", "goodTillCancel": "معتبر تا لغو", "good_till_cancel": "معتبر تا لغو", "good_till_date": "معتبر تا تاریخ", "goodTillDate": "معتبر تا تاریخ", "session": "نشست", "day": "روز", "minimumQuantity": "حد<PERSON><PERSON><PERSON> حجم", "shareholder": "سهامدار", "shareholders": "سهامدارها", "entryDate": "تاریخ سفارش", "brokerOrderEntryDateTime": "زمان ثبت سفارش", "validityDate": "تاریخ اعتبار", "submit": "تأیید", "orderSuccessMessage": "دستور ایجاد سفارش ارسال شد", "today": "امروز", "gregorian": "می<PERSON><PERSON>ی", "jalali": "شم<PERSON>ی", "invalidValue": "مقدار نامعتبر است", "orderSuccessfullySent": "سفارش با موفقیت ارسال شد", "date": "تاریخ", "time": "زمان", "tradeId": "شناسه معامله", "trades": "معاملات", "brokerId": "شناسه کارگزار", "total": "مجموع", "noDataToDisplay": "داده‌ای برای نمایش وجود ندارد", "selected": "انتخاب‌شده", "orderBook": "صف سفارش‌ها", "orderQueueType": "نوع صف سفارش", "buyQueue": "ص<PERSON>", "sellQueue": "صف فروش", "orderSide": "سمت سفارش", "tradedQuantity": "حجم معامله‌شده", "status": "وضعیت", "orderId": "شناسه سفارش", "buyOrderId": "شناسه سفارش", "origin": "م<PERSON><PERSON><PERSON>", "technicalOrigin": "مبد<PERSON> فنی", "buyOrigin": "م<PERSON><PERSON><PERSON>", "insertOrder": "ارسال سفارش", "loading": "در حال بارگذاری", "loadingErr": "خطا در دریافت اطلاعات", "instrument": "نماد", "closingPrice": "قیمت پایانی", "lastTradedPrice": "قیمت آخرین معامله", "baseVolume": "حجم مبنا", "iop": "قیمت گشایش", "totalTradeCount": "تعداد معاملات", "totalShareCount": "حجم معاملات", "maxOwnershipByShareholder": "حد بالای مالکیت سهامدار خرد", "vwap": "قیمت میانگین حجمی", "yesterdayClosingPrice": "قیمت دیروز", "totalTradedWorth": "ارزش معاملات", "priceTick": "وا<PERSON><PERSON> تغییر قیمت", "referencePrice": "قیمت مرجع", "orderPreference": "الگوریتم اولویت‌بندی صف", "lotSize": "وا<PERSON><PERSON> تغییر حجم", "totalShares": "تعداد کل سهم‌ها", "orderErrorMessage": "ارسال سفارش با خطا مواجه شد", "currentState": "وضعیت جاری", "suspended": "مجاز-متوقف", "reserved": "مجاز-محفوظ", "opened": "باز", "frozen": "فریز‌شده", "forbidden": "بسته", "halted": "ممنوع-متوقف", "surveillance": "نظارت", "authorized": "مجاز", "groupAuction": "حر<PERSON>ج", "changeInstrumentStateDesc": "برای تغییر وضعیت نماد، یکی از وضعیت های زیر را انتخاب کنید", "changeInstrumentState": "تغییر وضعیت نماد", "submissionFailed": "درخواست با خطا مواجه شد", "successfullySubmitted": "با موفقیت ثبت شد", "changeInstrumentMsg": "از وضعیت <strong>{{currentState}}</strong> به وضعیت <strong>{{selectedState}}</strong> تغییر دادید", "areYouSureToSubmit": "آیا از ذخیره تغییرات مطمعن هستید؟", "or": "یا", "browseFile": "انتخاب فایل", "dragAndDropFileHere": "فایل را بکشید و اینجا رها کنید", "uploadFile": "بارگذاری فایل", "filename": "نام فایل", "message": "پیام", "sendMessageCount": "تعداد پیام های ارسالی", "totalMessageCount": "مجموع پیام‌ها", "file's content was sent to the dispatcher": "محتوای فایل‌ها برای تولید سفارش ارسال شد", "COMPLETED": "تکمیل شد", "IN_PROGRESS": "در حال ارسال", "processing": "در حال پردازش", "INITIALIZED": "مقدار دهی اولیه", "uploadInfo": "جزییات بارگذاری", "uploadOrderFile": "بارگذاری فایل سفارش‌ها", "instrumentCommands": "عملیات نماد", "home": "خانه", "uploadOrderFileError": "بارگذاری فایل انجام نشد، لطفاً دوباره تلاش کنید", "instrumentInfo": "اطلاعات نماد", "reload": "بارگذاری مجدد‌", "apiFailError": "خطا در ای پی آی", "notifications": "اعلان‌ها", "changeInstrumentStateRequest": "درخواست تغییر وضعیت نماد {{symbol}} ارسال شد", "changeInstrumentStateDone": "تغییر وضعیت نماد {{symbol}} با موفقیت انجام شد", "changeInstrumentStateError": "درخواست تغییر وضعیت نماد {{symbol}} با خطا مواجه شد", "notFound": "موردی یافت نشد", "unauthorizedError": "خطای غیرمجاز", "unauthorizedErrorMsg": "نشست کاربری منقضی شده است، لطفاً دوباره وارد شوید", "close": "بستن", "failureError": "خطای دریافت اطلاعات", "failureErrorMsg": "خطایی در دریافت اطلاعات به وجود آمده است، لطفاً شبکه خود را بررسی کنید و در صورت حل نشدن مشکل با پشتیبانی تماس بگیرید", "groupInstruments": "فهرست نمادهای گروه", "removeOrderStateSent": "درخواست حذف سفارش {{orderId}} ارسال شد", "removeOrderStateSuccess": "سفارش {{orderId}} با موفقیت حذف شد", "removeOrderStateFailed": "درخواست حذف سفارش {{orderId}} با خطا مواجه شد", "confirm": "تأیید", "areYouSureAboutTheChanges": "آیا از تغییرات خود مطمئن هستید؟", "removeOrderConfirm": "آیا از حذف سفارش {{orderId}} مطمئن هستید؟", "editOrderStateSent": "درخواست ویرایش سفارش {{orderId}} ارسال شد", "editOrderStateSuccess": "سفارش {{orderId}} با موفقیت ویرایش شد", "editOrderStateFailed": "درخواست ویرایش سفارش {{orderId}} با خطا مواجه شد", "editOrderConfirm": "آیا از ویرایش سفارش {{orderId}} مطمئن هستید؟", "edit": "ویرایش", "editInstrument": "ویرایش نماد", "remove": "<PERSON><PERSON><PERSON>", "deleteInstrument": "<PERSON><PERSON><PERSON> نماد", "scheduleTemplate": "الگوی برنامه زمانی", "tableName": "نام جدول", "description": "توضیحات", "targetState": "وضعیت نهایی", "addSystemStateScheduleTemplate": "ایجاد الگوی زمانی وضعیت سامانه", "addGroupStateScheduleTemplate": "ایجاد الگوی زمانی گروه", "instrumentsState": "وضعیت نمادها", "exceptExcluded": "فقط نمادهای شامل", "allInstruments": "همه نمادها", "changeGroupStateRequest": "درخواست تغییر وضعیت گروه {{groupName}} به {{state}} ارسال شد", "changeGroupStateDone": "تغییر وضعیت گروه {{groupName}} به {{state}} با موفقیت انجام شد", "changeGroupStateError": "تغییر وضعیت گروه {{groupName}} به {{state}} با خطا مواجه شد", "removeGroup": "حذ<PERSON> گروه", "removeGroupRequest": "درخواست حذف گروه {{groupName}} ارسال شد", "removeGroupDone": "گروه {{groupName}} با موفقیت حذف شد", "removeGroupError": "درخواست حذف گروه {{groupName}} با خطا مواجه شد", "groups": "گروه‌ها", "select": "انتخاب", "newGroup": "گروه جدید", "editGroup": "ویرایش گروه", "deleteGroup": "حذ<PERSON> گروه", "editInstruments": "ویرایش نمادها", "delete": "<PERSON><PERSON><PERSON>", "included": "شامل", "excluded": "مستثنی", "groupName": "نام گروه", "state": "وضعیت", "oldState": "وضعیت پیشین", "newState": "وضعیت جدید", "filter": "فیلتر کردن", "reset": "پاک کردن", "addInstrument": "افزودن نماد", "requestFailed": "درخواست شما با خطا مواجه شد", "tryAgain": "تلاش مجدد", "uploadTime": "زمان بارگذاری", "fileProcessTime": "زمان پردازش فایل", "stateOfCommands": "وضعیت فرمان‌ها", "type": "نوع", "command": "فرمان", "orderByLimit": "عمق بازار", "volume": "حج<PERSON>", "basicInfo": "اطلاعات پایه", "trading": "معاملات", "unit": "وا<PERSON><PERSON> تغییرات", "matchingAlgo": "الگوریتم تطبیق سفارش", "instrumentState": "وضعیت نماد", "new": "ج<PERSON><PERSON><PERSON>", "canceled": "لغو شد", "reject": "ر<PERSON> ش<PERSON>", "rejected": "ر<PERSON> ش<PERSON>", "expired": "منق<PERSON>ی شد", "next": "بعدی", "back": "قب<PERSON>ی", "skip": "بستن", "done": "پایان", "staticPriceBandLowerBound": "ح<PERSON><PERSON><PERSON><PERSON> قیمت مجاز", "staticPriceBandUpperBound": "حداکثر قیمت مجاز", "defaultSearchDesc": "اطلاعاتی جهت نمایش وجود ندارد", "defaultSearchHint": "می‌توانید موارد زیر را جستجو کنید", "shareholderSearchDesc": "جهت جستجوی سهامدار مورد نظر می‌توانید از کد سهامداری استفاده کنید.", "shareholderSearchHint": "ج<PERSON>ت جستجو حداقل پنج حرف وارد کنید.", "investorSearchDesc": "جهت جستجوی سرمایه‌گذار مورد نظر می‌توانید از شناسه سرمایه‌گذار استفاده کنید.", "investorSearchHint": "ج<PERSON>ت جستجو حداقل پنج حرف وارد کنید.", "mnemonic": "نام اختصاری", "enMnemonic": "نام اختصاری انگلیسی", "faMnemonic": "نام اختصاری فارسی", "companyName": "نام شرکت", "instrumentIsin": "کد ۱۲رقمی شرکت", "getM1File": "دریافت فایل M1", "createM1Messages": "ایجاد پیام‌های M1", "getAramisFiles": "دریافت فایل‌های آرامیس", "deleteAllM1Files": "حذف تمام فایل‌های M1", "listOfM1FilesForDownload": "فهرست بارگیری فایل‌های M1", "downloadFile": "بارگیری فایل", "templateNameFa": "نام فارسی الگو", "templateNameEn": "نام انگلیسی الگو", "runAt": "شروع از", "code": "شناسه", "enable": "فعال", "disable": "غیرفعال", "name": "نام", "templateName": "نام الگو", "templateCode": "شناسه الگو", "apply": "اعمال", "applyOn": "اعمال از تاریخ", "applyTo": "اعمال به", "applyJustToday": "اعمال فقط برای روز جاری", "associateTemplate": "اختصاص الگوی زمانی", "systemSchedules": "برنامه‌های زمانی سیستم", "groupSchedules": "برنامه‌های زمانی گروه", "associateDeferredTemplate": "اختصاص الگوی زمانی معوق", "associatedTemplates": "الگوهای اختصاص‌داده‌شده", "unscheduled": "اختصاص‌داده‌نشده", "unscheduledEntities": "موجودیت‌های فاقد الگوی زمانی", "sessionSchedules": "برنامه زمانی روز / نشست", "createTrade": "ایجاد معامله", "buyer": "خر<PERSON>د<PERSON>ر", "buyOrder": "سفا<PERSON><PERSON> خرید", "seller": "فروشنده", "sellOrder": "سفارش فروش", "traderOrderNumber": "شماره سفارش", "giveUpBroker": "کارگزار دوم", "freeText": "متن", "bankCode": "کد-بانک", "towns": "شهرها", "townCode": "کد-شهر", "createTown": "افزودن شهر", "updateTown": "ویرایش شهر", "traderTownCodes": "کد-شهرهای معامله‌گر", "traderTowns": "شهرهای معامله‌گر", "branchCode": "کد-شعبه", "countryCode": "کد-کشور", "client": "مشتری", "house": "کارگزاری", "others": "سایر", "marketMaker": "بازارگردان", "manual": "دستی", "automatic": "<PERSON>و<PERSON><PERSON>ار", "other": "سایر", "scheduleTemplates": "الگو‌های برنامه زمانی", "file": "فایل", "visibleQuantity": "حجم قابل مشاهده", "initialQuantity": "حجم اولیه", "updateSuccessMessage": "زمان‌بندی با موفقیت تغییر یافت", "apiRequestPending": "در حال ارسال درخواست", "updateReferencePrice": "تغییر قیمت مرجع", "updateAbsolutePriceBand": "تغییر بازه قیمتی مطلق", "updatePercentagePriceBand": "تغییر نوسانات مجاز قیمت", "lowerBound": "ح<PERSON><PERSON><PERSON><PERSON> قیمت مجاز", "upperBound": "حداکثر قیمت مجاز", "changePriceBand": "تغییر بازه قیمتی", "upperPriceBoundPercentage": "حداکثر نوسان قیمت مجاز", "lowerPriceBoundPercentage": "حداقل نوسان قیمت مجاز", "priceBandPercentage": "نوسانات مجاز قیمت", "createTradeFailed": "دستور ایجاد معامله با خطا مواجه شد", "createTradeSent": "دستور ایجاد معامله ارسال شد", "keepOpenDialog": "باز نگه داشتن پنجره", "uploadCsdFile": "بارگذاری فایل CSD", "changeState": "تغییر وضعیت", "deleteGroupConfirmMsg": "آیا از حذف گروه {{code}} مطمئن هستید؟", "deleteGroupInGroupsPage": "برای حذف گروه به صفحه گروه‌ها مراجعه کنید", "thereIsNoScheduleTemplate": "الگوی زمانی‌ای جهت نمایش وجود ندارد.", "hasToBePositive": "مقدار واردشده باید عددی بزرگتر یا مساوی صفر باشد.", "hasToBeInRange": "باید بین {{min}} تا {{max}} باشد.", "includeToGroup": "پیروی از الگوی زمانی گروه", "excludeFromGroup": "عدم پیروی از الگوی زمانی گروه", "doInclude": "پیروی کردن", "doExclude": "خروج از گروه", "includeInstrument": "افزودن نماد", "excludeConfirmMsg": "با خروج از گروه، از این پس نماد از الگوی زمانی گروه خود پیروی نخواهد کرد، آیا از خروج نماد {{instrument}} از گروه مطمئن هستید؟", "includeConfirmMsg": "با خروج از گروه، تمامی الگوهای اختصاص‌داده‌شده برای نماد حذف خواهند شد. آیا از این عملیات مطمئن هستید؟", "groupCodeMask": "فقط اعداد و حروف انگلیسی بزرگ مجاز است", "editGroupInstrument": "ویرایش نمادهای گروه", "postSession": "پس از معاملات", "post_session": "پس از معاملات", "pre_session": "پیش از معاملات", "trading_session": "معاملات", "nextTradingSessionDate": "تاریخ نشست معاملات بعدی", "active": "فعال", "yes": "بله", "no": "<PERSON><PERSON><PERSON>", "selectDate": "انتخاب تاریخ", "keepDialogOpened": "باز نگه داشتن پنجره", "sourceId": "سهامدار مبدا", "destinationId": "سهامدار مقصد", "ignoreBlockedStatus": "نادیده گرفتن وضعیت مسدودی", "blockShareholderBuySide": "مسدود کردن سمت خرید سهامدار", "blockShareholderSellSide": "مسدود کردن سمت فروش سهامدار", "blockBrokerBuySide": "مسدود کردن سمت خرید کارگزار", "blockBrokerSellSide": "مسدود کردن سمت فروش کارگزار", "blockedOwnership": "دارایی مسدود‌شده", "blockShareholder": "تغییر وضعیت مسدودی سهامدار", "transferShare": "انتقال سهام", "shareholderPositions": "موقعیت‌های سهامدار", "pendingSell": "حجم در صف فروش", "pendingBuy": "حجم در صف خرید", "ownership": "دارایی تحت مالکیت", "unblock": "رفع مسدودی", "buySideIsBlocked": "سمت خرید مسدود شده است", "investorBuySideIsBlocked": "سمت خرید سرمایه‌گذار مسدود شده است", "sellSideIsBlocked": "سمت فروش مسدود شده است", "investorSellSideIsBlocked": "سمت فروش سرمایه‌گذار مسدود شده است", "blockedBuySide": "مسدودی سمت خرید", "blockedSellSide": "مسدودی سمت فروش", "all": "همه", "Shareholder_not_found": "سهامدار پیدا نشد", "quantity_must_be_greater_than_0": "تعداد باید بزرگتر از صفر باشد", "block_shareholder": "تغییر وضعیت مسدود کردن سهامدار", "cancel_order": "لغو سفارش", "create_trade": "ایجاد معامله", "shutdown": "خاموش کردن", "transfer_share": "انتقال سهام", "forbid_security": "ممنوع کردن نماد", "open_security": "باز کردن نماد", "reserve_security": "رزرو کردن نماد", "suspend_security": "تعلیق نماد", "thaw_security": "به جریان انداختن نماد", "awaitingResponse": "منتظر پاسخ هسته", "partialSuccess": "نیمه‌موفق", "failure": "ناموفق", "success": "موفق", "true": "بله", "false": "<PERSON><PERSON><PERSON>", "title": "عنوان", "systemState": "وضعیت سامانه", "system": "سامانه", "activationOfChangeSystemStateGuards": "فعال کردن بررسی محدودیت‌های تغییر وضعیت سامانه", "toggleActiveSystemState": "فعال / غیرفعال کردن محدویت‌های تغییر وضعیت سامانه", "preSession": "پیش از معاملات", "tradingSession": "معاملات", "preSessionDesc": "آماده‌سازی سامانه برای نشست معاملاتی", "tradingSessionDesc": "باز کردن سامانه برای معاملات", "postSessionDesc": "مدیریت نماد و عملیات مربوط به سامانه‌های جانبی", "Operation is not permitted in TRADING_SESSION and can only be executed in [POST_SESSION]": "این عملیات در طی نشست معاملات مجاز نیست و فقط در نشست پس از معاملات مجاز است", "Conflict in adding new resource": "مشکل تطابق در اضافه کردن", "Operation is not permitted in TRADING_SESSION.": "این عملیات در طی نشست معاملات مجاز نیست.", "userManagement": "مدیریت کاربران", "userManagementDesc": "حذف و اضافه کردن کاربر، مسدود کردن کاربران و تغییر نقش آن‌ها", "userRolesManagement": "مدیریت نقش‌های کاربران", "userRolesManagementDesc": "حذف و اضافه کردن نقش کاربران و تغییر دسترسی‌های آن‌ها", "permissionManagement": "مدیریت دسترسی‌ها", "permissionManagementDesc": "حذف و اضافه دسترسی‌ها و تغییر آن‌ها", "blocked": "مسدود ‌شده", "notBlocked": "مسدود نشده", "block": "مسدو<PERSON> کردن", "noChange": "بدون تغییر", "followGroup": "پیروی از گروه", "followedGroup": "پیرو گروه", "independentOfGroup": "مستقل از گروه", "addUser": "افزودن کاربر", "editUser": "ویرایش کاربر", "roles": "نقش‌ها", "availableOptions": "گزینه‌های موجود", "selectedOptions": "گزینه‌های انتخاب‌شده", "permissions": "دسترسی‌ها", "addUserRole": "افزودن نقش کاربر", "editUserRole": "ویرایش نقش کاربر", "shareholderId": "شناسه سهامدار", "method": "متد", "url": "آدرس", "addPermission": "افزودن دسترسی", "editPermission": "ویرایش دسترسی", "ruleId": "شناسه قانون", "ruleEngine": "قوانین دریافت سفارش", "createRule": "افزودن قانون", "insert": "ورود", "replace": "جایگزین", "modify": "ویرایش", "actionType": "نوع عملیات", "dropRequest": "صرف نظر از سفارش بدون پیام خطا", "rejectRequest": "رد سفارش با پیام خطا", "requestTypes": "نوع درخواست‌ها", "oneOf": "یک<PERSON> از", "nonOf": "هیچ‌<PERSON><PERSON> از", "orderTypes": "نوع سفارش‌ها", "iceberg": "دوبخشی", "market_on_opening": "قیمت گشایش", "accounts": "شناسه درگاه ورود", "origins": "مبدأها", "numberOfFilteredRequests": "تعداد سفارش‌های فیلترشده", "send": "ارسال", "resend": "ارسال مجدد", "deleteAll": "<PERSON><PERSON><PERSON> همه", "permissionDenied": "عدم اجازه دسترسی", "permissionDeniedForLoginMsg": "شما حداقل مجوزهای لازم برای ورود به سامانه را ندارید، لطفاً با مدیر خود تماس بگیرید.", "permissionDeniedMsg": "شما اجازه انجام این عملیات را ندارید. اگر فکر می‌کنید اشتباهی پیش آمده، لطفاً با مدیر خود تماس بگیرید.", "orderDetails": "جزئیات سفارش", "tradeDetails": "جزئیات معامله", "editOrder": "ویرایش سفارش", "deleteOrder": "لغو سفارش", "requestType": "نوع درخواست", "stateNotFound": "وضعیت یافت نشد", "oldPassword": "گذرواژه قبلی", "newPassword": "گذرواژه جدید", "confirmPassword": "تکرار گذرواژه جدید", "passwordsAreNotSame": "کلمه عبور با تکرار آن یکسان نیست", "passwordRegex": "گذرواژه باید حداقل ۸ کاراکتر و شامل حروف لاتین بزرگ و کوچک و اعداد باشد", "operationIsNotPermittedInCurrentState": "این عملیات در وضعیت فعلی سامانه مجاز نیست", "marketEntity": "نماد یا گروه", "purgeOrders": "پاک کردن سفارش‌ها", "nextPage": "ص<PERSON><PERSON>ه بعدی", "previousPage": "ص<PERSON><PERSON><PERSON> قبلی", "firstPage": "صفحه اول", "lastPage": "صف<PERSON>ه آخر", "of": "از", "corporateAction": "اقدامات شرکتی", "corporateActions": "اقدامات شرکتی", "appliedCorporateActions": "تاریخچه اقدامات شرکتی", "bonusOfferedShares": "سهام جدید عرضه‌شده بخش سهام جایزه", "bonusHeldShares": "سهام شرکت پیش از اعمال اقدام شرکتی بخش سهام جایزه", "rightOfferedShares": "سهام جدید عرضه‌شده بخش حق تقدم", "rightHeldShares": "سهام شرکت پیش از اعمال اقدام شرکتی بخش حق تقدم", "rightDiscountedPrice": "ارزش اسمی نماد بخش حق تقدم", "rights": "افزایش سرمایه از نوع حق تقدم", "dividends": "تقسیم سود نقدی", "bonus": "افزایش سرمایه از نوع سهام جایزه", "bonusRights": "افزایش سرمایه از نوع حق تقدم و سهام جایزه", "corporateActionType": "نوع اقدام شرکتی", "newTotalShareNumbers": "سهام شرکت پس از اعمال اقدام شرکتی", "offeredShares": "سهام جدید عرضه‌شده", "heldShares": "سهام شرکت پیش از اعمال اقدام شرکتی", "discountedPrice": "ارزش اسمی نماد", "amount": "مقدار", "percentage": "در<PERSON>د", "basicSpec": "مشخصات پایه", "instrumentSpec": "مشخصات نماد", "orderSpec": "مشخصات سفارش", "securityId": "شناسه نماد", "companyFaName": "نام فارسی شرکت", "companyEnName": "نام انگلیسی شرکت", "settlementDelay": "تأخیر در تسویه‌حساب", "instrumentType": "نوع نماد (RLC)", "shortInstrumentType": "نوع نماد", "minQuantity": "حدا<PERSON><PERSON> حجم سفارش", "maxBuyQuantity": "حداکثر حجم خرید", "maxSellQuantity": "حداکثر حجم فروش", "commandQueue": "صف فرمان‌ها", "majorPriority": "اولویت", "minorPriority": "اولویت فرعی", "investor": "سرمایه‌گذار", "investors": "سرمایه‌گذارها", "investorId": "شناسه سرمایه‌گذار", "changeBrokerCredit": "تغییر اعتبار کارگزار", "brokerIsBlocked": "کارگزار مسدود شده است", "changeCredit": "تغییر اعتبار", "credit": "اعتبار", "highestTradePrice": "بیشترین قیمت معامله", "lowestTradePrice": "کمترین قیمت معامله", "detail": "جزئیات", "scheduleIsNotSet": "الگویی ثبت نشده است", "custom": "سفار<PERSON>ی", "newClosingPrice": "قیمت پایانی پس از اعمال اقدام شرکتی", "value": "ارزش", "eventReports": "گزارش رویداد‌ها", "inQueue": "در صف", "sent": "ارسال‌شده", "applyCsdRecord": "اعمال ثبت CSD", "cancelTrade": "لغو معامله", "changeStaticPriceBand": "تغییر بازه قیمت ایستا", "updateSecurityReferencePrice": "تغییر قیمت مرجع نماد", "changeSecurityStaticPriceBandPercentage": "تغییر بازه قیمت درصد ایستای نماد", "updateStaticPriceBand": "به‌روز‌رسانی بازه قیمت ایستا", "changeGroupStaticPriceBand": "تغییر بازه قیمت ایستا گروه", "endSession": "پایان نشست", "removeInvalidatedSessionOrders": "حذف سفارش‌های نامعتبر نشست", "removeTodayInvalidatedOrders": "حذف سفارش‌های نامعتبر امروز", "declareRemainingOrders": "انتشار سفارش‌های باقی‌مانده", "save": "ذخیره", "saveAll": "ذخ<PERSON><PERSON>ه همه", "saveOrderBook": "ذخیره صف سفارش‌ها", "saveSecurity": "ذخیره نماد", "saveShareholder": "ذخیره سهامدار", "saveInvestor": "ذخیره سرمایه‌گذار", "saveProduct": "ذخ<PERSON>ره محصول", "startSession": "آغاز نشست", "changeSecurityState": "تغییر وضعیت نماد", "changeSecurityGroup": "تغییر گروه نماد", "tradingDayTimeTables": "انتشار جدول زمانی MMTP", "scheduledCommand": "فرمان برنامه‌ریزی‌شده", "bonusCorporateAction": "اعمال اقدام شرکتی - افزایش سرمایه از نوع سهام جایزه", "rightsCorporateAction": "اعمال اقدام شرکتی - افزایش سرمایه از نوع حق تقدم", "dividendsCorporateAction": "اعمال اقدام شرکتی - تقسیم سود نقدی", "bonusRightsCorporateAction": "اعمال اقدام شرکتی - افزایش سرمایه از نوع حق تقدم و سهام جایزه", "customCorporateAction": "اعمال اقدام شرکتی - سفارشی", "raisePostSessionMmtpMessages": "انتشار پیام‌های MMTP مرحله پس از معاملات", "revertCorporateActions": "بازنشانی همه اقدامات شرکتی", "updateSecuritySpecification": "ویرایش نماد", "purgeSecurity": "<PERSON><PERSON><PERSON> نماد", "addSecurity": "ایجاد نماد", "changeSystemState": "تغییر وضعیت سامانه", "addRule": "افزودن قوانین دریافت سفارش از کارگزاری", "editRule": "ویرایش قوانین دریافت سفارش از کارگزاری", "addOrUpdateRule": "افزودن / ویرایش قوانین دریافت سفارش از کارگزاری", "clearRule": "پاک کردن قوانین دریافت سفارش از کارگزاری", "clearAllRules": "پاک کردن همه قوانین دریافت سفارش از کارگزاری", "createProduct": "افزودن محصول", "blockInvestor": "تغییر وضعیت مسدودی سرمایه‌گذار", "setBrokerCredit": "تغییر اعتبار کارگزاری", "addSchedule": "افزودن برنامه زمانی", "addNewItem": "افزودن مورد جدید", "cancelSchedule": "حذف کردن برنامه زمانی", "updateInvestor": "به‌روز‌رسانی سرمایه‌گذار", "updateShareholderInvestorLink": "به‌روز‌رسانی ارتباط سرمایه‌گذار و سهامدار", "updateShareholderPosition": "تغییر دارایی سهامدار", "passwordSuccessfullyChanged": "گذرواژه با موفقیت تغییر پیدا کرد", "prepareToShutdown": "پایان کار", "prepareToShutdownDesc": "آماده‌سازی سامانه برای پایان کار", "unknownError": "خطای نامشخص", "daily": "روزانه", "deferred_opening": "گشایش معوق", "no_action": "بدون اقدام", "orderAdministration": "مدیریت سفارش", "currentStateInfo": "اطلاعات وضعیت جاری", "initialStateInfo": "اطلاعات مقادیر اولیه", "accountId": "شناسه درگاه", "internalSubscriberReference": "شماره پیگیری", "secondaryShareholderId": "سهامدار دوم", "expectedRemainingQuantity": "حجم باقی‌مانده مورد انتظار", "previousSequenceId": "شناسه پیشین سفارش", "previousEntryDate": "تاریخ پیشین سفارش", "secondarySequenceId": "شناسه دوم سفارش", "rejectionCause": "ع<PERSON><PERSON> خطا", "secondaryOrigin": "مبد<PERSON> دوم", "jobId": "شناسه اجرا", "jobIds": "شناسه‌های اجرا", "triggerTime": "زمان اجرا", "isDeferred": "گشایش معوق", "scheduledActionRequest": "عملیات", "address": "آدرس", "billingAddress": "آدرس صورت‌حساب", "contactName": "نام رابط", "changeSecurityStaticPriceBand": "تغییر بازه قیمت ایستای نماد", "calendar": "تقویم", "weekend": "<PERSON><PERSON><PERSON> هفته", "sunday": "یکشنبه", "monday": "دو‌شنبه", "tuesday": "سه‌شنبه", "wednesday": "چهارشنبه", "thursday": "پنج<PERSON>نبه", "friday": "جمعه", "saturday": "شنبه", "holiday": "تعطیلات", "sender": "ارسال‌کننده", "instrumentsWithoutGroup": "فهرست نمادهای بدون گروه", "role": "نقش", "myPermissions": "دسترسی‌های من", "myPermissionsDesc": "مشاهده دسترسی‌ها و نقش‌های من", "addOrUpdateHolidays": "افزودن یا ویرایش تعطیلات", "deleteHoliday": "حذ<PERSON> تعطیلات", "addOrUpdateWeekend": "افزودن یا ویرایش آخر هفته", "deleteWeekend": "حذ<PERSON> آ<PERSON>ر هفته", "adjustments": "مقاد<PERSON>ر تعدیل‌شده", "adjustedClosingPrice": "قیمت پایانی تعدیل‌شده", "superAdjustedClosingPrice": "قیمت پایانی تعدیل‌شده فوق‌العاده", "adjustedLastTradePrice": "قیمت آخرین معامله تعدیل‌شده", "adjustedTotalShareCount": "تعداد سهام تعدیل‌شده", "adjustedVwap": "قیمت میانگین حجمی تعدیل‌شده", "shouldClearingDataUploadedMsg": "برای تغییر وضعیت سامانه به 'پایان کار'، لطفاً ابتدا فایل CSD را بارگذاری کنید. وضعیت 'پایان کار' فقط پس از موفقیت‌آمیز بودن بارگذاری فایل CSD فعال خواهد شد", "productId": "شناسه محصول", "product": "محصول", "products": "محصول‌ها", "editProduct": "ویرایش محصول", "updateProduct": "ویرایش محصول", "deleteProduct": "<PERSON><PERSON><PERSON> محصول", "persianName": "نام فارسی", "englishName": "نام انگلیسی", "adjustedTotalShares": "تعداد تعدیل‌شده کل سهم‌ها", "boards": "تابلوها", "board": "تابلو", "boardCode": "ک<PERSON> تابلو", "createBoard": "افزودن تابلو", "updateBoard": "ویرایش تابلو", "deleteBoard": "حذ<PERSON> تابلو", "firstTradingDate": "اولین روز معاملاتی", "minBlockMarketQuantity": "حدا<PERSON><PERSON> حجم سفارش بازار بلوکی", "parValue": "ارزش اسمی سهام", "sectors": "بخش‌ها", "sector": "بخش", "sectorCode": "کد بخش", "createSector": "افزودن بخش", "updateSector": "ویرایش بخش", "deleteSector": "حذ<PERSON> بخش", "subSectors": "زیربخش‌ها", "subSector": "زیربخش", "subSectorCode": "ک<PERSON> زیربخش", "createSubSector": "افزودن  زیربخش", "updateSubSector": "ویرایش  زیربخش", "deleteSubSector": "حذ<PERSON>  زیر<PERSON><PERSON>ش", "cancelTrades": "لغو معاملات", "canceledTrades": "معاملات لغو شده", "cancelTradesReport": "گزارش لغو معاملات", "error_required": "وارد کردن {{label}} اجباری است", "error_min": "حداقل مقدار {{label}} عدد {{value.min}} است", "error_max": "حداقل مقدار {{label}} عدد {{value.max}} است", "error_minlength": "حداق<PERSON> باید شامل {{value.requiredLength}} حرف باشد {{label}}", "error_maxlength": "حداکثر باید شامل {{value.requiredLength}} حرف باشد {{label}}", "error_email": "ایمیل نامعتبر است", "error_pattern": "الگو به‌درستی رعایت نشده است", "error_length": "طول {{label}} باید {{value.length}} حرف باشد", "error_greaterThan": "مفدار {{label}} باید بزرگتر از {{value.greaterThan}} باشد", "error_range": "مفدار {{label}} باید بین {{value.range.min}} و {{value.range.max}} باشد", "lessThanAnother": "مفدار {{label}} باید کوچکتر از {{value.lessThanAnother}} باشد", "system_state": "سامانه", "security": "نماد", "sessionScheduleIsEmpty": "برنامه زمان‌بندی برای {{schedulable}} ثبت نشده است", "createScheduleAction": "افزودن برنامه زمانی", "excludeFromGroupMsg": "این نماد عضو گروه {{groupCode}} است و جهت اختصاص الگوی زمانی برای این نماد ابتدا باید از گروه خود خارج شود", "clearingData": "اطلاعات تسویه‌وجوه", "secondaryClearingData": "اطلاعات تسویه‌وجوه دوم", "example": "مثال: {{example}}", "normalMarket": "بازار معاملات عادی", "oddLotMarket": "بازار خرده‌فروشی ", "buyingInMarket": "بازا<PERSON> <PERSON><PERSON><PERSON>د جبرانی", "blockMarket": "بازار معاملات عمده", "indexMarket": "بازار شاخص", "unlistedInstrumentsMarket": "بازار شرکت‌های پذیرفته‌نشده", "killed": "باطل‌شده", "deleted": "حذ<PERSON>‌<PERSON><PERSON>ه", "purged": "پاکسازی‌شده", "invalidated": "ابطال‌شده", "updated": "بروزرسانی شده", "admin_canceled": "لغو‌شده توسط مدیر سامانه", "from": "از", "to": "تا", "changeGroupOfSecurity": "تغییر نماد گروه", "thereAreNoDetails": "جزئیات وجود ندارد", "isPercentage": "از نوع درصد", "condition": "شرط", "systemStateDeactivationDangerMsg": "از عواقب خطرناک غیر‌فعال کردن وضعیت سامانه و تغییر خارج از ترتیب آن آگاه هستم.", "clearingReport": "گزارش تسویه‌وجوه", "totalStatus": "وضعیت کلی", "failedCsdRecords": "رکوردهای CSD ناموفق", "originalRecord": "ر<PERSON><PERSON><PERSON><PERSON> اصلی", "isNotWorkingDay": "هشدار: ‌امروز روز کاری نیست.", "securityNotFound": "نمادی یافت نشد", "prepareToShutDownToPreSessionChangeWarning": "از تغییر وضعیت سامانه به پس از معاملات آگاه هستم و عواقب خطرناک تغییر خارج از ترتیب وضعیت سامانه را می‌پذیرم.", "startMarketExplanationMessage": "مطلع باشید که بازار هنوز آغاز نشده است!", "startMarketGuidanceMessage": "این به این معناست که هنوز پیام‌های ابتدایی روز برای بازار همانند پیام‌های RLC-53, RLC-5E, RLC-5F, RLC-5G ,RLC-07 منتشر نشده‌اند. در ابتدا شما باید بازار را آغاز کنید تا بتوانید این پیام‌ها را ارسال کنید و در نتیجه به درستی با سامانه و بازار کار کنید.", "start": "آغ<PERSON>ز", "startMarket": "آغاز بازار", "initializeMarket": "آغاز بازار", "notifyMarket": "ارسال پیام ناظر", "notifyMarketTemplates": "الگوهای پیام ناظر", "addNotifyMarketTemplate": "افزودن الگوی پیام ناظر", "editNotifyMarketTemplate": "ویرایش الگوی پیام ناظر", "messageNature": "ماهیت پیام", "addressType": "نوع آدرس", "isUrgent": "اضطراری", "notNow": "فعلاً خیر", "immediateEdit": "ویرایش فوری", "purgeGroup": "حذ<PERSON> گروه", "immediateUpdateSecuritySpecification": "ویرایش فوری مشخصات نماد", "startOfSecurityCharacteristics": "آغاز انتشار مشخصات نماد", "endOfSecurityCharacteristics": "اتمام انتشار مشخصات نماد", "dividendPercentage": "درصد سود از ارزش اسمی نماد به ازای هر سهم", "dividendAmount": "مقدار ریالی سود به ازای هر سهم", "addDevice": "افزو<PERSON><PERSON> کلید", "firmCode": "کد مؤسسه مالی", "counterCode": "کد ایستگاه", "traderId": "شناسه معامله‌گر", "addTrader": "ایجاد معامله‌گر", "deleteTrader": "حذ<PERSON> معامله‌گر", "company": "شرکت‌", "companies": "شرکت‌ها", "startDate": "تاریخ آغاز", "updateDate": "تاریخ آخرین ویرایش", "shortName": "نام خلاصه", "fullName": "نام کامل", "createCompany": "افزودن شرکت", "editCompany": "ویرایش شرکت", "updateCompany": "ویرایش شرکت", "deleteCompany": "حذ<PERSON> شر<PERSON>ت", "shortPersianName": "نام فارسی خلاصه", "shortEnglishName": "نام انگلیسی خلاصه", "fullPersianName": "نام فارسی کامل", "fullEnglishName": "نام انگلیسی کامل", "issuePrice": "قیمت صدور سهام", "tradingStartDate": "تاریخ آغاز معامله", "tradingEndDate": "تاریخ پایان معامله", "maturityDate": "تاریخ سررسید", "normalBlockSize": "حجم عادی سفارش عمده", "companyCode": "کد شرکت", "productCode": "<PERSON><PERSON> محصول", "productTypeCode": "کد نوع محصول", "productSubTypeCode": "کد زیرنوع محصول", "marketFlowCode": "کد نحوه انتشار اطلاعات", "productTypes": "نوع‌های محصول", "productType": "نوع محصول", "productSubTypes": "زیرنوع‌های محصول", "productSubType": "زیرنوع محصول", "marketFlow": "نحوه انتشار اطلاعات", "productName": "نام محصول", "creationDate": "تاریخ ایجاد", "warrants": "اوراق", "strikePrice": "قیمت اعمال", "underlyingProductId": "شناسه محصول پایه", "productSpec": "مشخصات محصول", "marketSec": "قسمت بازار", "derivativeSpec": "مشخصات مشتقه", "purgeOrdersWithBlockingBroker": "پاک کردن سفارش‌ها با مسدود کردن کارگزار", "priorityDateTime": "اولویت زمانی", "instruments": "نمادها", "commandDetail": "جزئیات فرمان", "id": "شناسه", "orderHistory": "تاریخچه سفارش‌ها", "queuedCommandEffectedByDeletingCompanyWarning": "آگاه باشید که حذف این شرکت منجر به رد فرمان‌های منتظر در صف برای ایجاد/ویرایش محصول‌های زیر می‌شود:", "queuedCommandEffectedByDeletingGroupWarning": "آگاه باشید که حذف این گروه منجر به رد فرمان‌های منتظر در صف برای ایجاد/ویرایش نماد‌های زیر می‌شود:", "fifoOrigin": "FIFO Origin", "fifo": "FIFO", "bestLimits": "بهترین مظنه‌ها", "orderCount": "تعداد", "buyOrderCount": "تعداد", "buyQuantity": "حج<PERSON>", "messageMarket": "بازار پیام", "changeGroupState": "تغییر وضعیت گروه", "createGroup": "افزودن گروه", "updateGroup": "ویرایش گروه", "applyPermanently": "عدم پیروی بازه قیمتی نماد از گروه خود.", "groupStaticPriceBand": "تغییر بازه قیمت ایستای گروه", "deferSecurityOpening": "تعویق گشایش نماد", "cancelSecurityDeferredOpening": "لغو گشایش معوق نماد", "editBroker": "ویرایش کارگزاری", "addBroker": "افزودن کارگزاری", "updateSchedule": "ویرایش الگوی زمانی", "blockBroker": "تغییر وضعیت مسدودی کارگزار", "productCreation": "ایج<PERSON> محصول", "securityCreation": "ایجاد نماد", "productUpdate": "ویرایش محصول", "securityUpdate": "ویرایش نماد", "securityDeletion": "<PERSON><PERSON><PERSON> نماد", "productDeletion": "<PERSON><PERSON><PERSON> محصول", "orderWorkflow": "مدیریت سفارش‌گیری", "created": "ایج<PERSON> شده", "persisted": "بدون تغییر", "futureChanges": "تغییرات آینده", "pending": "در انتظار", "trace": "پیگردی", "traceId": "شناسه پیگردی", "orderTrace": "پیگردی سفارش", "orderRequestsReport": "گزارش سفارش‌های درخواستی", "alreadyInQueue": "در صف", "rejectedByRequestViolation": "ردشده به دلیل نقض درخواست", "rejectedByMatchingEngineCondition": "ردشده به دلیل شرایط موتور تطبیق", "frozenInQueue": "فریز‌شده در صف", "canceledByBroker": "لغوشده توسط کارگزار", "canceledByAdmin": "لغوشده توسط مدیر سامانه", "partialTraded": "معامله جزئی", "fullyTraded": "معامله کامل", "eliminatedByAdmin": "ردشده توسط مدیر سامانه", "eliminatedBySessionEnding": "ردشده به دلیل پایان جلسه", "eliminatedByDateEnding": "ردشده به دلیل پایان تاریخ", "eliminatedByAuctionEnding": "ردشده به دلیل پایان حراج", "eliminatedByShareholderBlocking": "ردشده به دلیل مسدودیت سهامدار", "eliminatedByInvestorBlocking": "ردشده به دلیل مسدودیت سرمایه‌گذار", "eliminatedByBrokerBlocking": "ردشده به دلیل مسدودیت کارگزار", "eliminatedByPositionUpdating": "ردشده به دلیل به‌روزرسانی دارایی", "eliminatedByCorporateAction": "ردشده به دلیل اقدام شرکتی", "eliminatedByProductDeleting": "ردشده به دلیل حذف محصول", "eliminatedBySecurityDeleting": "ردشده به دلیل حذف نماد", "eliminatedBySecurityUpdating": "ردشده به دلیل به‌روزرسانی نماد", "droppedByBrokerRequestRule": "حدف‌شده به دلیل قانون دریافت سفارش", "droppedByInputRoutingTree": "حدف‌شده به دلیل درخت مسیریابی", "cross": "دو‌طرفه", "isShortSellAllowed": "مجاز بودن فروش تعهدی", "changeSecurityShortSellState": "تغییر وضعیت فروش تعهدی نماد", "accessMatrix": "ماتریس دسترسی", "traderGroupAccessMatrix": "ماتریس دسترسی معامله‌گر-گروه", "brokerGroupAccessMatrix": "ماتریس دسترسی کارگزار-گروه", "addGroupBasedBlockEntry": "افزودن ورودی مبتنی بر گروه", "removeGroupBasedBlockEntry": "حذف ورودی مبتنی بر گروه", "availableForSell": "موجود برای فروش", "currentSessionBlockedOwnership": "دارایی مسدود‌شده برای امروز", "instrumentStateIsNotTheSameAsItsGroup": "وضعیت نماد با وضعیت گروه متفاوت است", "excludedInstruments": "نمادهای خارج گروه", "unselectAll": "پاک کردن همه", "selectAll": "انتخاب همه", "itemSelected": "مورد انتخاب شده", "itemsSelected": "مورد انتخاب شده", "creditCheckingStatus": "وضعیت بررسی اعتبار", "updateCreditCheckingStatus": "به‌روز‌رسانی وضعیت بررسی اعتبار", "creditCheckingIdDisabled": "بررسی اعتبار غیر فعال است", "changeGroupCreditCheckingStatus": "تغییر وضعیت بررسی اعتبار گروه", "changeBrokerCreditCheckingStatus": "تغییر وضعیت بررسی اعتبار کارگزار", "changeSystemCreditCheckingStatus": "تغییر وضعیت بررسی اعتبار سیستم", "addPosition": "افزودن موقعیت", "excludeSecurityGroup": "خارج کردن نماد گروه", "includeSecurityGroup": "وارد کردن نماد گروه", "staticPriceBand": "بازه قیمت ایستا", "groupLowerPriceBoundPercentage": "حداقل نوسان قیمت مجاز گروه", "groupUpperPriceBoundPercentage": "حداکثر نوسان قیمت مجاز گروه", "buyQueuePrices": "قیمت‌های صف خرید (کمترین - بیشترین)", "sellQueuePrices": "قیمت‌های صف فروش (کمترین - بیشترین)", "scheduledStateChange": "تغییر وضعیت برنامه‌ریزی‌شده", "automaticSecurityStateChange": "تغییر وضعیت خودکار نماد", "uploadCancelTradesFile": "بارگذاری فایل لغو معاملات", "dividendValue": "سود نقدی تقسیم‌شده", "failed": "ناموفق", "followedGroupStaticPriceBand": "نماد‌های پیروی‌کننده از بازه قیمتی گروه", "notFollowedGroupStaticPriceBand": "نمادهای مستثنی از بازه قیمتی گروه", "followedPriceBand": "پیروی‌کننده از بازه قیمتی", "notFollowedPriceBand": "مستثنی از بازه قیمتی", "shouldOpen": "باز شود", "preOpening": "پیش‌گشایش", "continuousTrading": "حراج پیوسته", "uploadImmediateUpdate": "ویرایش دسته‌ای محدودیت حجم نماد", "uploadImmediateState": "ویرایش دسته‌ای وضعیت نماد", "uploadReferencePrices": "ویرایش دسته‌ای قیمت مرجع نماد", "uploadGroupStaticThreshold": "ویرایش دسته‌ای بازه نوسان گروه", "uploadPriceBand": "ویرایش دسته‌ای بازه قیمت نماد", "uploadDeferredInstrumentSchedule": "ویرایش دسته‌ای الگوی زمانی معوق", "uploadStaticThresholdPercentage": "ویرایش دسته‌ای بازه نوسان نماد", "fileHeaderStructure": "ساختار فایل", "numberOfSentEntries": "تعداد سطرهای ارسال شده", "noAvailableOption": "هیچ موردی وجود ندارد", "securityStateChangeFailure": "تغییر ناموفق وضعیت نماد", "progress": "پیشرفت", "checkProgress": "مشاهده پیشرفت", "auction": "حر<PERSON>ج", "createProductType": "افزودن نوع محصول", "updateProductType": "ویرایش نوع محصول", "deleteProductType": "حذف نوع محصول", "createProductSubType": "افزودن زیرنوع محصول", "updateProductSubType": "ویرایش زیرنوع محصول", "deleteProductSubType": "حذف زیرنوع محصول", "queuedCommandEffectedByDeletingBoardWarning": "آگاه باشید که حذف این تابلو منجر به رد فرمان‌های منتظر در صف برای ایجاد/ویرایش محصول‌های زیر می‌شود:", "queuedCommandEffectedByDeletingProductTypeWarning": "آگاه باشید که حذف این نوع محصول منجر به رد فرمان‌های منتظر در صف برای ایجاد/ویرایش محصول‌های زیر می‌شود:", "queuedCommandEffectedByDeletingProductSubTypeWarning": "آگاه باشید که حذف این زیرنوع محصول منجر به رد فرمان‌های منتظر در صف برای ایجاد/ویرایش محصول‌های زیر می‌شود:", "buyState": "وضعیت خرید", "sellState": "وضعیت فروش", "waiting": "منتظر", "default": "پیش‌<PERSON><PERSON>ش", "changeGroup": "تغییر گروه", "routTreePosition": "جایگاه", "length": "طول", "predicateAppliedOnChildren": "قید اعمال‌شده بر گره‌های فرزند", "routeTree": "درخت مسیریابی", "bindCapToRouteNode": "انتساب مسیریابی به درگاه‌های پیام", "capGroups": "گروه‌های درگاه پیام", "routeNodeCode": "ک<PERSON> گره‌", "routeNodeCodes": "کد گره‌های مسیریابی", "caps": "درگاه‌های پیام", "entryPort": "پورت ورودی", "publicPort": "پورت عمومی", "privatePort": "پورت خصوصی", "xdiffPort": "X-diff پورت", "addCapToGroup": "افزودن درگاه پیام به گروه", "massageRouting": "مسیریابی پیغام", "capGroupNames": "نام گروه‌های درگاه پیام", "inherited": "ارث‌برده شده", "notInherited": "الصاق شده", "withoutImpact": "عدم تأثیر در بازار", "addTree": "افزودن درخت", "saveTrees": "ذخیره درخت‌ها", "applyConfiguration": "اعمال تنظیمات", "initializeRouteTree": "مقداردهی اولیه درخت مسیریابی", "hasImpact": "در بازار تأثیر دارد", "marketToLimit": "بازار به محدود", "hasGlobalPrivateMessages": "دارای تمام پیام‌های خصوصی", "createCap": "افزودن درگاه پیام", "updateCap": "ویرایش درگاه پیام", "deleteCap": "حذف درگاه پیام", "capName": "نام درگاه پیام", "successfulZ2Count": "موفق Z2", "successfulU2Count": "موف<PERSON>", "successfulW2Count": "موفق W2", "successfulX2Count": "موفق X2", "failedZ2Count": "ناموفق Z2", "failedU2Count": "ناموفق U2", "failedW2Count": "ناموفق W2", "failedX2Count": "ناموفق X2", "isPreOpeningOrder": "عدم اجرا در حراج پیوسته", "percentagePrice": "قیمت درصدی", "groupPriceBandUpdate": "تغییر بازه قیمت گروه", "mmtpMessageCode": "شناسه پیام MMTP", "saveToDisk": "ذخیره در حافظه", "successfullySavedToDisk": "با موفقیت در حافظه ذخیره شد", "failedOnSavingToDisk": "خطا در زمان ذخیره در حافظه", "successfullyOpenOrdersDownload": "فایل OPEN_ORDERS با موفقیت دانلود شد", "failedOnDownloadingOpenOrders": "خطا در زمان دانلود فایل OPEN_ORDERS", "priority": "اولویت", "originId": "شناسه مبدأ", "updateOriginPriority": "ویرایش اولویت مبدأ", "isUpdatingAfterOpening": "به‌روز‌رسانی بازه قیمت ایستا پس از گشایش", "changeOpeningUpdatePriceBandStatus": "به‌روز‌رسانی بازه قیمت ایستا پس از گشایش", "availableShare": "دارایی قابل‌معامله", "potentialShare": "دارایی بالقوه", "nationality": "ملیت", "investorType": "نوع سرمایه‌گذار", "national": "داخلی", "foreigner": "<PERSON>ا<PERSON><PERSON>ی", "individual": "شخص حقیقی", "nonIndividual": "شخص حقوقی", "individualBoardDirector": "عضو هیأت مدیره", "updateProductSellAvailability": "ویرایش موجودی فروش محصول", "shouldUpdateClosingPrice": "قیمت پایانی ویرایش شود.", "rateLimit": "محدودیت نرخ تبادل (میکرو ثانیه)", "individualMaxOwnership": "حد بالای مالکیت شخص حقیقی", "nonIndividualMaxOwnership": "حد بالای مالکیت شخص حقوقی", "individualBoardDirectorMaxOwnership": "حد بالای مالکیت عضو هیأت مدیره", "individualCurrentOwnership": "مالکیت جاری شخص حقیقی", "nonIndividualCurrentOwnership": "مالکیت جاری شخص حقوقی", "individualBoardDirectorCurrentOwnership": "مالکیت جاری عضو هیأت مدیره", "nationalMaxOwnership": "حد بالای مالکیت شخص داخلی", "foreignerMaxOwnership": "حد بالای مالکیت شخص خارجی", "nationalCurrentOwnership": "مالکیت جاری شخص داخلی", "foreignerCurrentOwnership": "مالکیت جاری شخص خارجی", "isInherited": "گرفته شده از گروه‌های بالاتر", "position": "موقعیت مکانی", "routePredicates": "قید‌های راهبری", "createRoutePredicate": "افزودن قید‌های راهبری", "updateRoutePredicate": "ویرایش قید‌های راهبری", "items": "موا<PERSON>د", "isDefault": "گره پیش‌فرض", "newRouteNode": "ایج<PERSON> گره جدید", "actions": "عملیات", "action": "<PERSON><PERSON><PERSON>", "schedule": "برنامه زمانی", "noAction": "بدون عملیات", "sourceGroupCode": "کد نماد مبدأ", "targetGroupCode": "کد نماد مقصد", "entryType": "نوع ورودی", "copy": "رونویسی", "copyAccessMatrix": "رونویسی ماتریس دسترسی", "copyGroupBasedEntries": "رونویسی ماتریس دسترسی", "isDisabled": "غیرفعال", "addGroupsToCap": "افزودن گروه", "editRouteTreeNode": "ویرایش گره درخت مسیریابی", "groupSessionSchedules": "برنامه زمانی روز / نشست گروه", "instrumentSessionSchedules": "برنامه زمانی روز / نشست نماد", "systemSessionSchedules": "برنامه زمانی روز / نشست سامانه", "addChildNode": "افزودن گره فرزند", "incomingPredicate": "قید ورودی", "outgoingPredicate": "<PERSON>ی<PERSON> خروجی", "uploadProductSellAvailability": "ویرایش دسته‌ای موجودی فروش محصول", "uploadInstrumentAllowShortSell": "ویرایش دسته‌ای مجاز بودن فروش تعهدی", "orderEntryRuleId": "شناسه قانون دریافت سفارش", "editModeIsOn": "حالت ویرایش فعال است", "deleteShareholder": "حذ<PERSON> سهامدار", "deleteInvestor": "حذف سرمایه‌گذار"}