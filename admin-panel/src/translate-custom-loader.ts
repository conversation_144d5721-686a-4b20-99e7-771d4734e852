import {TranslateLoader} from '@ngx-translate/core';
import {Observable, of} from 'rxjs';
import {readJSON} from '@test/read-json';

export class TranslateCustomLoader implements TranslateLoader {
    getTranslation(lang: string): Observable<any> {
        switch (lang) {
            case 'en':
                const en = readJSON('assets/i18n/en.json');
                return of(en);

            case 'fa':
                return of(readJSON('assets/i18n/fa.json'));
        }
    }
}
