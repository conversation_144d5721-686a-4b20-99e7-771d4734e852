import 'reflect-metadata';
import {Type} from '@angular/core';
import {EXCLUDED_EXCEL_COLUMNS_KEY} from './exclude-from-excel';

export const EXCEL_FILE_NAME_KEY = '$excelFileName';
export const EXCEL_COLUMNS_KEY = '$excelColumns';

export function Excel({fileName}: { fileName: string }) {
    return function Excel(target: Type<any>): void {
        const targetName = getExcelColumnsPropName(target);

        target.prototype[EXCEL_FILE_NAME_KEY] ||= fileName;
        target.prototype[targetName] = getAncestorColumns(target);

        const instanceOfTargetClass = new target();
        for (let prop in instanceOfTargetClass) {
            if (instanceOfTargetClass[prop] === undefined && !isExcludedExcelColumn(target, prop)) {
                target.prototype[targetName].push(prop);
            }
        }
    }
}

function isExcludedExcelColumn(target: Type<any>, column: string): boolean {
    return target[EXCLUDED_EXCEL_COLUMNS_KEY] &&
        target[EXCLUDED_EXCEL_COLUMNS_KEY][column];
}

export function getExcelColumnsPropName(target: any): string {
    return typeof target === 'function'
        ? target.name
        : target?.constructor.name;
}

export function getAncestorColumns(target: any): any[] {
    const columns = [];

    let proto = Object.getPrototypeOf(target.prototype);

    while (proto && proto.constructor && proto.constructor.name !== 'Object') {
        const name = proto.constructor.name;
        columns.push(...(target.prototype.constructor[name] || []));
        proto = Object.getPrototypeOf(proto);
    }

    return columns;
}
