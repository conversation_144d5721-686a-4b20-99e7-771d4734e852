import {TableGroupData} from '@models/table-representation/table-group-data';
import {TranslateKey} from '@shared/enums/translate-key';
import {Name} from '@shared/models/name';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {Company} from '@models/company';
import {TableDataProvider} from '@models/table-representation/table-representable';
import {SimpleTableData} from '@models/table-representation/simple-table-representation';
import {Getter, Type} from '@shared/plain-to-instance';

export namespace CompanyAdminCommand {

    @Getter
    export class Create extends TableDataProvider {
        readonly code: string;

        @Type(Name)
        readonly shortName: Name;

        @Type(Name)
        readonly fullName: Name;

        readonly sectorCode: string;

        readonly subSectorCode: string;

        getTableData(): SimpleTableData[] {
            const data: TableGroupData[] = [
                {
                    title: TranslateKey.code,
                    value: this.code
                },
                {
                    title: TranslateKey.shortName,
                    value: this.shortName,
                    pipeToken: SelectByLanguagePipe
                },
                {
                    title: TranslateKey.fullName,
                    value: this.fullName,
                    pipeToken: SelectByLanguagePipe
                },
                {
                    title: TranslateKey.sectorCode,
                    value: this.sectorCode
                },
                {
                    title: TranslateKey.subSectorCode,
                    value: this.subSectorCode
                }
            ];
            return [{data}];
        }
    }

    @Getter
    export class Update extends TableDataProvider {
        readonly companyId: string;

        readonly spec: Company.Spec;

        getTableData(): SimpleTableData[] {
            const data: TableGroupData[] = [
                {
                    title: TranslateKey.code,
                    value: this.companyId
                },
                {
                    title: TranslateKey.shortName,
                    value: this.spec.shortName,
                    pipeToken: SelectByLanguagePipe
                },
                {
                    title: TranslateKey.fullName,
                    value: this.spec.fullName,
                    pipeToken: SelectByLanguagePipe
                },
                {
                    title: TranslateKey.sectorCode,
                    value: this.spec.sectorCode
                },
                {
                    title: TranslateKey.subSectorCode,
                    value: this.spec.subSectorCode
                }
            ];
            return [{data}];
        }
    }

    @Getter
    export class Delete extends TableDataProvider {
        readonly companyId: string;

        getTableData(): SimpleTableData[] {
            const data: TableGroupData[] = [
                {
                    title: TranslateKey.code,
                    value: this.companyId
                }
            ];
            return [{data}];
        }
    }
}
