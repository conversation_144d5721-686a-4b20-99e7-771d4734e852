import {FormGroupRawValue, MutableAny} from '@models/form-group-raw-value';
import {FormControl} from '@angular/forms';
import {DayOfWeek} from '@models/day-of-week';
import {toEndpointDateFormat} from '@core/utils';

export class Weekend {
    private _applyDate: string;
    get applyDate(): string { return this._applyDate; }

    private _daysOfWeek: DayOfWeek[];
    get daysOfWeek(): DayOfWeek[] { return this._daysOfWeek; }
}

export namespace Weekend {
    export class Form implements MutableAny<Weekend> {
        applyDate: FormControl<Date>;
        daysOfWeek: FormControl<DayOfWeek[]>;
    }

    export class Create implements MutableAny<Weekend> {
        applyDate: string;
        daysOfWeek: DayOfWeek[];

        constructor(weekend: FormGroupRawValue<Form>) {
            this.applyDate = toEndpointDateFormat(weekend.applyDate);
            this.daysOfWeek = weekend.daysOfWeek;
        }
    }
}
