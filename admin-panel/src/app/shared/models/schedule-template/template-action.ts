import {Mutable} from '@models/form-group-raw-value';
import {Getter} from '../../plain-to-instance/getter';
import {Setter} from '../../plain-to-instance/setter';
import {FormControl} from "@angular/forms";
import {Group} from "@models/group";

@Getter
@Setter
export class TemplateAction<T = any> {
    readonly runAt: string;

    readonly targetState: T;

    readonly shouldOpen?: boolean;
}

export namespace TemplateAction {
    export class CreateForm<T> {
        runAt: FormControl<string>;
        targetState: FormControl<T>;
        shouldOpen?: FormControl<boolean>;

    }

    export class Form<T = any> implements Mutable<TemplateAction<T>> {
        runAt = '00:00:00';
        targetState: T = null;
        shouldOpen?: boolean = false;
    }
}
