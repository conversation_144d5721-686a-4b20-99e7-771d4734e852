import {Instrument} from '@homeModels/instrument';
import {TranslateKey} from '@shared/enums/translate-key';
import {TranslatePipe} from '@ngx-translate/core';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {TableGroupData} from '@models/table-representation/table-group-data';
import {Order} from '@models/order/order';
import {Shareholder} from '@models/shareholder';
import {CustomDecimalPipe} from '@shared/pipes/custom-decimal.pipe';
import {TableDataProvider} from '@models/table-representation/table-representable';
import {SimpleTableData} from '@models/table-representation/simple-table-representation';
import {StoreService} from '@shared/services/store.service';
import {Getter, Transform, Type} from '@shared/plain-to-instance';

export namespace ShareholderAdminCommands {
    @Getter
    export class Block extends TableDataProvider {
        readonly shareholderId: string;

        @Type(Shareholder.BlockedSide)
        readonly shareholderBlockedSides: Shareholder.BlockedSide[];

        @Type(Shareholder.BlockedPosition)
        readonly blockedPositions: Shareholder.BlockedPosition[];

        override getTableData(): SimpleTableData[] {
            let blockedBuySideValue: string;
            let blockedSellSideValue: string;

            this.shareholderBlockedSides?.find(item => {
                const blockedSide = item.blocked.toString();

                item.side === Order.Side.BUY
                    ? (blockedBuySideValue = blockedSide)
                    : (blockedSellSideValue = blockedSide);
            });

            const basicData: TableGroupData[] = [
                {
                    title: TranslateKey.shareholder,
                    value: this.shareholderId
                },
                {
                    title: TranslateKey.blockedBuySide,
                    value: blockedBuySideValue,
                    pipeToken: TranslatePipe
                },
                {
                    title: TranslateKey.blockedSellSide,
                    value: blockedSellSideValue,
                    pipeToken: TranslatePipe
                }
            ];

            const positionsData: SimpleTableData[] = this.blockedPositions.map(position => ({
                data: [
                    {
                        title: TranslateKey.productId,
                        value: position.productId
                    },
                    {
                        title: TranslateKey.blockedBuySide,
                        value: position.symbolBuySideStatus?.blocked.toString(),
                        pipeToken: TranslatePipe
                    },
                    {
                        title: TranslateKey.blockedSellSide,
                        value: position.symbolSellSideStatus?.blocked.toString(),
                        pipeToken: TranslatePipe
                    },
                    {
                        title: TranslateKey.blockedOwnership,
                        value: position.blockedOwnership || 0
                    }
                ]
            }));

            return [
                {data: basicData},
                ...positionsData
            ];
        }
    }

    @Getter
    export class TransferShare extends TableDataProvider {
        readonly blockedStatusIgnored: boolean;

        @Transform(TransferShare._findInstrument, 'productId')
        readonly instrument: Instrument.Single;

        readonly destinationId: string;

        readonly quantity: number;

        readonly sourceId: string;

        private static _findInstrument(productId: string): any {
            return StoreService.instruments.find(instrument => instrument.productId === productId);
        }

        override getTableData(): SimpleTableData[] {
            const data = [
                {
                    title: TranslateKey.instrument,
                    value: this.instrument?.mnemonic,
                    pipeToken: SelectByLanguagePipe
                },
                {
                    title: TranslateKey.sourceId,
                    value: this.sourceId
                },
                {
                    title: TranslateKey.destinationId,
                    value: this.destinationId
                },
                {
                    title: TranslateKey.quantity,
                    value: this.quantity,
                    pipeToken: CustomDecimalPipe
                }
            ];

            return [{data}];
        }
    }
}
