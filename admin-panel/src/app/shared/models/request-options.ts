import {HttpHeaders} from '@angular/common/http';
import {UtilConstants} from '@constants/util-constants';
import {QueryParams} from '@models/query-params';

export interface RequestOptions {
    hasLocalErrorHandler?: boolean;
    params?: QueryParams;
    [prop: string]: any;
}

export class HttpCustomRequestOptions {
    headers = new HttpHeaders();

    constructor(options: RequestOptions) {
        if (!options) { return }

        if (options.hasLocalErrorHandler) {
            this.headers = this.headers
                .set(UtilConstants.HAS_LOCAL_ERROR_HANDLER, options.hasLocalErrorHandler.toString());
        }

        for (let prop in options) {
            this[prop] = options[prop];
        }
    }
}
