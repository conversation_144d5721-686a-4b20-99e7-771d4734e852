import {Getter} from '../plain-to-instance/getter';
import {Excel} from '../to-excel-columns/excel';

@Getter
@Excel({fileName: 'failedCSDRecords'})
export class FailedCsdRecord {
    readonly originalRecord: string;

    readonly message: string;
}

@Getter
@Excel({fileName: 'CSDFiles'})
export class CsdFile {
    readonly fileName: string;

    readonly totalStatus: string;

    readonly successfulZ2Count: number;

    readonly successfulU2Count: number;

    readonly successfulW2Count: number;

    readonly successfulX2Count: number;

    readonly failedZ2Count: number;

    readonly failedU2Count: number;

    readonly failedW2Count: number;

    readonly failedX2Count: number;

    readonly processingProgress: number;
}
