import {Type} from '@shared/plain-to-instance/type';
import {Order} from '@models/order/order';
import {Getter} from '@shared/plain-to-instance';
import {Setter} from '@shared/plain-to-instance';
import {FormArray, FormControl, FormGroup} from '@angular/forms';
import {Excel} from '@shared/to-excel-columns';

@Getter
export class Shareholder {
    readonly shareholderId: string;

    readonly investorId: string;

    readonly originId: string;

    @Setter
    isBuyBlocked: boolean;

    @Setter
    isSellBlocked: boolean;

    @Getter
    readonly deleted: boolean;
}

export namespace Shareholder {
    @Excel({fileName: 'shareholder positions'})
    @Getter
    export class Position {
        @Setter
        productId: string;

        readonly shareholderId: string;

        readonly ownership: number;

        readonly pendingBuy: number;

        readonly pendingSell: number;

        readonly currentSessionBlockedOwnership: number;

        isBuyBlocked: boolean;

        isSellBlocked: boolean;

        @Setter
        blockedOwnership: string;
    }

    @Getter
    export class BlockedSide {
        readonly side: Order.Side;

        @Setter
        blocked: boolean;
    }

    export class BlockedPosition extends Position {
        @Type(BlockedSide)
        private readonly _blockedSides: BlockedSide[] = [];

        get symbolBuySideStatus(): BlockedSide { return this._blockedSides[0]; }

        get symbolSellSideStatus(): BlockedSide { return this._blockedSides[1]; }
    }

    interface BlockedSideForm {
        side: FormControl<Order.Side>;
        blocked: FormControl<boolean>;
    }

    export interface BlockedPositionsForm {
        productId: FormControl<string>;
        blockedOwnership: FormControl<string>;
        blockedSides: FormArray<FormGroup<BlockedSideForm>>
    }

    export class BlockForm {
        blockedPositions: FormArray<FormGroup<BlockedPositionsForm>>;
        shareholderBlockedSides: FormArray<FormGroup<BlockedSideForm>>;
    }
}
