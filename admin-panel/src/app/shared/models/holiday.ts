import {FormGroupRawValue, MutableAny} from '@models/form-group-raw-value';
import {FormControl} from '@angular/forms';
import {toEndpointDateFormat} from '@core/utils';

export class Holiday {
    private _date: string;
    get date(): string { return this._date; }

    private _description: string;
    get description(): string { return this._description; }
}

export namespace Holiday {
    export class Form implements MutableAny<Holiday> {
        date: FormControl<Date>;
        description: FormControl<string>;
    }

    export class Create implements MutableAny<Holiday> {
        date: string;
        description: string;

        constructor(holiday: FormGroupRawValue<Form>) {
            this.date = toEndpointDateFormat(holiday.date);
            this.description = holiday.description;
        }
    }

    export class Update {
        description: string;

        constructor(description: string) {
            this.description = description;
        }

    }
}
