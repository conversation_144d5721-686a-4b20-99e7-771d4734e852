import {MutableAny} from '@models/form-group-raw-value';
import {FormControl} from '@angular/forms';
import {Getter} from '../plain-to-instance/getter';

@Getter
export class PriceBandPercentage {
    readonly lowerBoundPercentage: number;

    readonly upperBoundPercentage: number;
}

export namespace PriceBandPercentage {
    export class Form implements MutableAny<PriceBandPercentage> {
        lowerBoundPercentage: FormControl<number>;
        upperBoundPercentage: FormControl<number>;
    }
}
