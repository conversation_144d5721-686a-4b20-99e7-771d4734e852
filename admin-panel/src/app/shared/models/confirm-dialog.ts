import {ThemePalette} from '@angular/material/core';
import {Observable} from 'rxjs';

export class ConfirmDialog {
    onConfirm: () => void;
    onCancel: () => void;
    type: ThemePalette;
    message?: string | Observable<string>;

    constructor({onConfirm, onCancel, type, message}: ConfirmDialog) {
        this.onConfirm = onConfirm;
        this.onCancel = onCancel;
        this.message = message;
        this.type = type;
    }
}
