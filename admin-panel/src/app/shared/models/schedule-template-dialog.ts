import {Schedule} from '@models/schedule';
import {Schedulable} from '@models/schedule-template/schedulable';

export interface ScheduleTemplateDialog {
    isForSelect: boolean;
}

export class SetScheduleTemplateDialog {
    schedulable?: Schedulable;
    associatedTemplate?: Schedule;
    disableSearch?: boolean;
    isDeferred?: boolean;

    constructor(options: SetScheduleTemplateDialog) {
        this.associatedTemplate = options.associatedTemplate;
        this.disableSearch = options.disableSearch;
        this.schedulable = options.schedulable;
        this.isDeferred = options.isDeferred;
    }
}
