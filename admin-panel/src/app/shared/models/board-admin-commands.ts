import {TableDataProvider} from '@models/table-representation/table-representable';
import {SimpleTableData} from '@models/table-representation/simple-table-representation';
import {TableGroupData} from '@models/table-representation/table-group-data';
import {TranslateKey} from '@shared/enums/translate-key';
import {Getter} from '@shared/plain-to-instance';

export namespace BoardAdminCommands {
    @Getter
    export class Create extends TableDataProvider {
        readonly code: string;

        readonly name: string;

        getTableData(): SimpleTableData[] {
            const data: TableGroupData[] = [
                {
                    title: TranslateKey.code,
                    value: this.code
                },
                {
                    title: TranslateKey.name,
                    value: this.name
                }
            ];

            return [{data}];
        }
    }


    @Getter
    export class Delete extends TableDataProvider {
        readonly code: string;

        getTableData(): SimpleTableData[] {
            const data: TableGroupData[] = [
                {
                    title: TranslateKey.code,
                    value: this.code
                }
            ];

            return [{data}];
        }
    }
}
