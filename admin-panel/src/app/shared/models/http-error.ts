import {Translate<PERSON><PERSON>} from '@shared/enums/translate-key';
import {Getter} from '@shared/plain-to-instance';

@Getter
export class HttpError {
    readonly type: string;

    readonly title: string;

    readonly detail: string;

    readonly instance: string;

    readonly traceId: string;

    constructor(type: string, title: string, detail: string, instance: string) {
        this.type = type;
        this.title = title;
        this.detail = detail;
        this.instance = instance;
    }

    static createUnknownErrors(): HttpError {
        return new HttpError(TranslateKey.unknownError, null, null, null);
    }
}
