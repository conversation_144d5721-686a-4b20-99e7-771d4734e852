import {FormControl} from '@angular/forms';
import {Instrument} from '@homeModels/instrument';
import {Product} from '@homeModels/product';
import {Group} from '@models/group';
import {Shareholder} from '@models/shareholder';
import {Trader} from '@models/trader';
import {Getter} from '../plain-to-instance/getter';
import {TranslateKey} from '@shared/enums/translate-key';
import {Transform} from '@shared/plain-to-instance/transform';
import {Investor} from '@models/investor';
import {Order} from '@models/order/order';
import {TranslatePipe} from '@ngx-translate/core';
import {BilingualPipe} from '@shared/modules/shared-declarations/select-by-language/bilingual.pipe';
import {TableDataProvider} from '@models/table-representation/table-representable';
import {SimpleTableData} from '@models/table-representation/simple-table-representation';
import {mapToBrokers, mapToGroups, mapToInstruments} from '@core/utils';
import {Broker} from '@homeModels/broker';

@Getter
export class PurgeOrder {
    readonly securities: string[];

    readonly products: string[];

    readonly groups: string[];

    readonly brokers: string[];

    readonly shareholders: string[];
}

export namespace PurgeOrder {

    export interface RequestForm {
        sides: FormControl<number>;
        securities: FormControl<Instrument.Single[]>;
        products: FormControl<Product.Simple[]>;
        groups: FormControl<Group[]>;
        brokers: FormControl<Broker.Simple[]>;
        shareholders: FormControl<Shareholder[]>;
        investors: FormControl<Investor[]>;
        traders: FormControl<Trader[]>;
    }

    @Getter
    export class Request extends TableDataProvider {
        @Transform(mapToInstruments)
        readonly securities: Instrument.Single[];

        @Transform(mapToGroups)
        readonly groups: Group[];

        @Transform(mapToBrokers)
        readonly brokers: Broker.Simple[];

        readonly products: string[];

        readonly shareholders: string[];

        readonly investors: string[];

        readonly traders: string[];

        readonly sides: Order.Side[];

        getTableData(): SimpleTableData[] {
            const table = [];

            if (this.securities.length) {
                const securitiesTable: SimpleTableData = {
                    groupName: TranslateKey.instruments,
                    data: this.securities.map(security => ({
                        title: TranslateKey.mnemonic,
                        value: security?.mnemonic,
                        pipeToken: BilingualPipe,
                        pipeArgs: [true]
                    }))
                }

                table.push(securitiesTable);
            }

            if (this.groups.length) {
                const groupsTable: SimpleTableData = {
                    groupName: TranslateKey.groups,
                    data: this.groups.map(group => ({
                        title: TranslateKey.code,
                        value: group?.code
                    }))
                }

                table.push(groupsTable);
            }

            if (this.brokers.length) {
                const brokersTable: SimpleTableData = {
                    groupName: TranslateKey.brokers,
                    data: this.brokers.map(broker => ({
                        title: TranslateKey.brokerId,
                        value: broker.id
                    }))
                }

                table.push(brokersTable);
            }

            if (this.products.length) {
                const productsTable: SimpleTableData = {
                    groupName: TranslateKey.products,
                    data: this.products.map(product => ({
                        title: TranslateKey.productId,
                        value: product
                    }))
                }

                table.push(productsTable);
            }

            if (this.shareholders.length) {
                const shareholdersTable: SimpleTableData = {
                    groupName: TranslateKey.shareholders,
                    data: this.shareholders.map(trader => ({
                        title: TranslateKey.shareholderId,
                        value: trader
                    }))
                }

                table.push(shareholdersTable);
            }

            if (this.investors.length) {
                const investorsTable: SimpleTableData = {
                    groupName: TranslateKey.investors,
                    data: this.investors.map(trader => ({
                        title: TranslateKey.investorId,
                        value: trader
                    }))
                }

                table.push(investorsTable);
            }

            if (this.traders.length) {
                const tradersTable: SimpleTableData = {
                    groupName: TranslateKey.traders,
                    data: this.traders.map(trader => ({
                        title: TranslateKey.trader,
                        value: trader
                    }))
                }

                table.push(tradersTable);
            }

            if (this.sides.length) {
                const tradersTable: SimpleTableData = {
                    groupName: TranslateKey.orderSide,
                    data: this.sides.map(side => ({
                        title: TranslateKey[side.toLowerCase()],
                        pipeToken: TranslatePipe,
                        value: TranslateKey.true
                    }))
                }

                table.push(tradersTable);
            }

            return table;
        }
    }
}
