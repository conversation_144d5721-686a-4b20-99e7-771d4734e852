import {Type} from '@shared/plain-to-instance/type';
import {ValidityQualifierType} from '@enums/validity-qualifier-type';
import {OrderType} from '@enums/order-type';
import {Getter} from '../../plain-to-instance/getter';
import {ClearingData} from '@models/clearing-data';

export abstract class Order {
    abstract side;
    abstract type;
    abstract price;
    abstract quantity;
    abstract clearingData;
    abstract brokerId: string;
    abstract disclosedQuantity: number;
    abstract validityQualifierType: ValidityQualifierType;
    abstract shareholderId: string;
}

export namespace Order {
    export enum Side {
        BUY = 'BUY',
        SELL = 'SELL'
    }

    export enum Status {
        NEW = 'NEW',
        CANCELED = 'CANCELED',
        REJECTED = 'REJECTED',
        EXPIRED = 'EXPIRED',
        ADMIN_CANCELED = 'ADMIN_CANCELED',
        UPDATED = 'UPDATED',
        INVALIDATED = 'INVALIDATED',
        FROZEN = 'FROZEN',
        PURGED = 'PURGED',
        DELETED = 'DELETED',
        KILLED = 'KILLED',
    }

    @Getter
    export class New extends Order {
        readonly price: number;

        readonly side: Side;

        readonly shareholderId: string;

        readonly brokerId: string;

        readonly validityQualifierType: ValidityQualifierType;

        readonly minimumQuantity: number;

        @Type(ClearingData)
        readonly clearingData: ClearingData;

        readonly disclosedQuantity: number;

        readonly quantity: number;

        readonly type: OrderType;

        readonly validityDate: string;

        readonly isPreOpeningOrder: boolean;
    }

    @Getter
    export class Update {
        readonly price: number;

        readonly validityQualifierType: ValidityQualifierType;

        readonly quantity: number;

        readonly validityDate: string;
    }
}
