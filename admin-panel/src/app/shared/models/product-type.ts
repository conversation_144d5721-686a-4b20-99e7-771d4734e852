import {FormControl} from '@angular/forms';
import {Excel} from '@shared/to-excel-columns';
import {Getter} from '@shared/plain-to-instance';

@Excel({fileName: 'productTypes'})
@Getter
export class ProductType {
    readonly code: string;

    readonly name: string;
}

export namespace ProductType {
    export interface Create {
        code: FormControl<string>;

        name: FormControl<string>;
    }
}
