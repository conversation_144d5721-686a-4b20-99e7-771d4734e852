import {DataTable} from '@models/data-table';
import {DatatableConfig} from '@constants/data-table-config';
import {takeUntil} from 'rxjs/operators';
import {MatPaginator} from '@angular/material/paginator';
import {Page} from '@models/page';
import {AfterViewInit, ChangeDetectorRef, Directive, inject, OnDestroy} from '@angular/core';

@Directive() // Ensures Angular recognizes the lifecycle hooks
export abstract class PaginatedDataTable<T> extends DataTable<T> implements OnDestroy, AfterViewInit {


    protected abstract _paginator: MatPaginator;

    protected _changeDetectorRef = inject(ChangeDetectorRef);

    private _pageDataValue = new Page<T>([]);

    protected get _pageData(): Page<T> {
        return this._pageDataValue;
    }

    protected set _pageData(value: Page<T>) {
        this.isPageDataLoaded.set(true);
        this._pageDataValue = value;
    }

    get dataSource(): T[] {
        return this._pageData.content;
    }

    get dataSourceLength(): number {
        return this._pageData.element.total;
    }

    get dataSourcePageSize(): number {
        return this._pageData.page.size;
    }

    get paginatorClass(): string {
        return this.dataSourceLength > this._pageData.page.size
            ? ''
            : 'invisible';
    }

    protected _calcPageSize(): void {
        const tableContainerHeight = this._tableContainer.nativeElement.getBoundingClientRect().height;
        const viewHeight = tableContainerHeight - DatatableConfig.HEADER_HEIGHT;
        // Calculate the number of rows that fit within viewport
        this.filterProperty.filterParams.size = Math.floor(viewHeight / DatatableConfig.ROW_HEIGHT).toString();
    }

    ngAfterViewInit(): void {
        this._changeDetectorRef.detectChanges();
        this._refreshPageData();
        this._subscribeOnPageChange();
    }

    ngOnDestroy(): void {
        this._onDestroy.next();
        this._onDestroy.complete();
    }

    protected _subscribeOnPageChange(): void {
        this._paginator.page
            .pipe(takeUntil(this._onDestroy))
            .subscribe(page => {
                this.filterProperty.filterParams.page = page.pageIndex.toString();
                this._refreshPageData(true);
            });
    }

    protected _refreshPageData(_ = false): void {
        this.isPageDataLoaded.set(false);
        this._calcPageSize();
        this._fetchPageData();
    }

    protected _filterPageData(): void {
        // Reset filter params page index.
        this.filterProperty.filterParams.page = '0';
        // Reset angular paginator page index.
        this._paginator.pageIndex = 0;

        this._refreshPageData();
    }

    protected _forceDataTableRefresh = (): void => {
        // To trigger the data table to update its view,
        // temporarily clear and restore the data source reference.
        const backupData = this._pageData.content;
        this._pageData.content = [];
        this._changeDetectorRef.detectChanges();
        this._pageData.content = backupData;
        this._changeDetectorRef.detectChanges();
    }

    exportToExcel(): void {
        this.filterProperty.filterParams.page = null;
        this.filterProperty.filterParams.size = null;
        super.exportToExcel();
        this._calcPageSize();
    }
}
