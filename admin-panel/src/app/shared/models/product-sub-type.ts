import {FormControl} from '@angular/forms';
import {Excel} from '@shared/to-excel-columns';
import {Getter} from '@shared/plain-to-instance';

@Excel({fileName: 'productSubTypes'})
@Getter
export class ProductSubType {

    readonly code: string;

    readonly name: string;

    readonly instrumentType: string;

    readonly shortInstrumentType: string;

    readonly productTypeCode: string;
}

export namespace ProductSubType {
    export interface Create {
        code: FormControl<string>;

        name: FormControl<string>;

        instrumentType: FormControl<string>;

        shortInstrumentType: FormControl<string>;

        productTypeCode: FormControl<string>;
    }
}
