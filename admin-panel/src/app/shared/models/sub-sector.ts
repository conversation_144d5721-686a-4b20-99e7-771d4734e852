import {FormControl} from '@angular/forms';
import {Excel} from '@shared/to-excel-columns';
import {Getter} from '@shared/plain-to-instance';

@Excel({fileName: 'subSectors'})
@Getter
export class SubSector {
    code: string;

    name: string;

    sectorCode: string;
}

export namespace SubSector {
    export interface Create {
        code: FormControl<string>,
        name: FormControl<string>,
        sectorCode: FormControl<string>
    }

    export interface Update {
        code: FormControl<string>,
        name: FormControl<string>
    }
}
