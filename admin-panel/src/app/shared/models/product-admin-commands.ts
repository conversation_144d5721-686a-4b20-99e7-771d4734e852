import {Getter} from '../plain-to-instance/getter';
import {TableGroupData} from '@models/table-representation/table-group-data';
import {TranslateKey} from '@shared/enums/translate-key';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {MbDatePipe} from '@modules/datepicker/mb-date.pipe';
import {UtilConstants} from '@constants/util-constants';
import {Product} from '@homeModels/product';
import {CustomDecimalPipe} from '@shared/pipes/custom-decimal.pipe';
import {BilingualPipe} from '@shared/modules/shared-declarations/select-by-language/bilingual.pipe';
import {TableDataProvider} from '@models/table-representation/table-representable';
import {SimpleTableData} from '@models/table-representation/simple-table-representation';
import {TranslatePipe} from '@ngx-translate/core';

export namespace ProductAdminCommands {
    @Getter
    export class Create extends TableDataProvider {
        readonly productId: string;

        readonly spec: Product.Spec;


        getTableData(): SimpleTableData[] {
            const data: TableGroupData[] = [
                {
                    title: TranslateKey.productId,
                    value: this.productId
                },
                {
                    title: TranslateKey.productCode,
                    value: this.spec.productCode
                },
                {
                    title: TranslateKey.name,
                    value: this.spec.name,
                    pipeToken: SelectByLanguagePipe
                },
                {
                    title: TranslateKey.mnemonic,
                    value: this.spec.mnemonic,
                    pipeToken: BilingualPipe,
                    pipeArgs: [true]
                },
                {
                    title: TranslateKey.boardCode,
                    value: this.spec.boardCode
                },
                {
                    title: TranslateKey.productTypeCode,
                    value: this.spec.productTypeCode
                },
                {
                    title: TranslateKey.productSubTypeCode,
                    value: this.spec.productSubTypeCode
                },
                {
                    title: TranslateKey.marketFlowCode,
                    value: this.spec.marketFlowCode
                },
                {
                    title: TranslateKey.tradingStartDate,
                    value: this.spec.tradingStartDate,
                    pipeToken: MbDatePipe,
                    pipeArgs: [UtilConstants.DATE_FORMAT]
                },
                {
                    title: TranslateKey.tradingEndDate,
                    value: this.spec.tradingEndDate,
                    pipeToken: MbDatePipe,
                    pipeArgs: [UtilConstants.DATE_FORMAT]
                },
                {
                    title: TranslateKey.parValue,
                    value: this.spec.parValue
                },
                {
                    title: TranslateKey.referencePrice,
                    value: this.spec.referencePrice
                },
                {
                    title: TranslateKey.minBlockMarketQuantity,
                    value: this.spec.normalBlockSize
                },
                {
                    title: TranslateKey.totalShares,
                    value: this.spec.totalShares,
                    pipeToken: CustomDecimalPipe
                },
                {
                    title: TranslateKey.underlyingProductId,
                    value: this.spec.underlyingProductId
                },
                {
                    title: TranslateKey.strikePrice,
                    value: this.spec.strikePrice
                },
                {
                    title: TranslateKey.issuePrice,
                    value: this.spec.issuePrice
                },
                {
                    title: TranslateKey.percentagePrice,
                    value: this.spec.isPricePercentage.toString(),
                    pipeToken: TranslatePipe
                }
            ];
            return [{data}];
        }

    }

    @Getter
    export class Delete extends TableDataProvider {

        readonly productId: string;

        getTableData(): SimpleTableData[] {
            const data: TableGroupData[] = [
                {
                    title: TranslateKey.productId,
                    value: this.productId
                }
            ];
            return [{data}];
        }

    }

    @Getter
    export class UpdateSellAvailability extends TableDataProvider {

        readonly productId: string;

        readonly availableForSell: boolean;

        getTableData(): SimpleTableData[] {
            const data: TableGroupData[] = [
                {
                    title: TranslateKey.productId,
                    value: this.productId
                },
                {
                    title: TranslateKey.availableForSell,
                    value: this.availableForSell.toString(),
                    pipeToken: TranslatePipe
                }
            ];
            return [{data}];
        }

    }

    export class Update extends Create {}
}
