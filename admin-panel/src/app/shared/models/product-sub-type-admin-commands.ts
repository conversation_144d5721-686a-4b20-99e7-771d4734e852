import {Getter} from '../plain-to-instance/getter';
import {TableDataProvider} from '@models/table-representation/table-representable';
import {SimpleTableData} from '@models/table-representation/simple-table-representation';
import {TableGroupData} from '@models/table-representation/table-group-data';
import {TranslateKey} from '@shared/enums/translate-key';

export namespace ProductSubTypeAdminCommands {
    @Getter
    export class Create extends TableDataProvider {
        readonly code: string;

        readonly name: string;

        readonly instrumentType: string;

        readonly shortInstrumentType: string;

        readonly productTypeCode: string;

        getTableData(): SimpleTableData[] {
            const data: TableGroupData[] = [
                {
                    title: TranslateKey.code,
                    value: this.code
                },
                {
                    title: TranslateKey.name,
                    value: this.name
                },
                {
                    title: TranslateKey.instrumentType,
                    value: this.instrumentType
                },
                {
                    title: TranslateKey.shortInstrumentType,
                    value: this.shortInstrumentType
                },
                {
                    title: TranslateKey.productTypeCode,
                    value: this.productTypeCode
                }
            ];

            return [{data}];
        }
    }

    @Getter
    export class Update extends TableDataProvider {
        readonly code: string;

        readonly name: string;

        readonly instrumentType: string;

        readonly shortInstrumentType: string;

        getTableData(): SimpleTableData[] {
            const data: TableGroupData[] = [
                {
                    title: TranslateKey.code,
                    value: this.code
                },
                {
                    title: TranslateKey.name,
                    value: this.name
                },
                {
                    title: TranslateKey.instrumentType,
                    value: this.instrumentType
                },
                {
                    title: TranslateKey.shortInstrumentType,
                    value: this.shortInstrumentType
                }
            ];

            return [{data}];
        }
    }

    @Getter
    export class Delete extends TableDataProvider {
        readonly code: string;

        getTableData(): SimpleTableData[] {
            const data: TableGroupData[] = [
                {
                    title: TranslateKey.code,
                    value: this.code
                }
            ];

            return [{data}];
        }
    }
}
