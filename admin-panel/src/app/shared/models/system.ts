import {Name} from '@shared/models/name';
import {SystemState} from '@models/system-state';
import {Schedulable} from '@models/schedule-template/schedulable';
import {Getter} from '../plain-to-instance/getter';

export class System extends Schedulable {
    constructor(state?: SystemState.State) {
        super();
        this._state = state;
    }

    @Getter
    readonly type = Schedulable.Type.SYSTEM_STATE;

    private _state: SystemState.State;
    get state(): SystemState.State { return this._state; }

    get name(): Name {
        const name = new Name();
        name.fa = 'سامانه';
        name.en = 'System';
        return name;
    }

    override get schedulableName(): Name {
        return this.name;
    }
}
