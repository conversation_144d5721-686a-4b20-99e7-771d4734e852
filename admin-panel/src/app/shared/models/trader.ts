import {FormControl} from '@angular/forms';
import {Transform} from '@shared/plain-to-instance/transform';
import {Town} from '@models/town';
import {AutoCompleteFormField} from '@modules/dynamic-form/form-fields/auto-complete-field/auto-complete-form-field';
import {mapToBroker} from '@core/utils';
import {Broker} from '@homeModels/broker';
import {Getter, Setter} from '@shared/plain-to-instance';
import {Excel, ExcelColumns} from '@shared/to-excel-columns';

@Getter
@Excel({fileName: 'traders'})
export class Trader {
    @Setter
    traderId: string;

    @Transform(mapToBroker, 'brokerId')
    @ExcelColumns({title: 'brokerId', valuePath: 'broker.id'})
    readonly broker: Broker.Simple;

    readonly firmCode: string;

    readonly townCode: string;

    readonly counterCode: string;
}

export namespace Trader {
    export class Form {
        brokerCode: FormControl<string>;
        firmCode: FormControl<string>;
        townCode: FormControl<AutoCompleteFormField<Town>>;
        counterCode: FormControl<string>;
    }
}
