import {InstrumentAdminCommands} from '@homeModels/instrument/instrument-admin-commands';
import {CsdFileAdminCommands} from '@models/csd-file-admin-commands';
import {OrderAdminCommands} from '@models/order/order-admin-commands';
import {ShareholderAdminCommands} from '@models/shareholder-admin-commands';
import {TradeAdminCommands} from '@models/trade-admin-commands';
import {SystemStateAdminCommands} from '@models/system-state-admin-commands';
import {ScheduleAdminCommands} from '@models/schedule-admin-commands';
import {InvestorAdminCommands} from '@models/investor-admin-commands';
import {BrokerAdminCommands} from '@models/broker-admin-commands';
import {WeekendAdminCommands} from '@models/weekend-admin-commands';
import {RuleAdminCommands} from '@models/rule-admin-commands';
import {HolidayAdminCommands} from '@models/holiday-admin-commands';
import {ProductAdminCommands} from '@models/product-admin-commands';
import {SaveAdminCommands} from '@models/save-admin-commands';
import {CorporateActionAdminCommands} from '@models/corporate-action-admin-commands';
import {TraderAdminCommands} from '@models/trader-admin-commands';
import {CompanyAdminCommand} from '@models/company-admin-command';
import {PurgeOrder} from '@models/purge-order';
import {GroupAdminCommands} from '@models/group-admin-commands';
import {Trade} from '@models/trade';
import {TableDataProvider} from '@models/table-representation/table-representable';
import {GroupBaseBlockEntryAdminCommand} from '@homeModels/group-base-block-entry-admin-command';
import {NotifyMarketAdminCommand} from '@homeModels/notify-market-admin-command';
import {SystemAdminCommands} from '@models/system-admin-commands';
import {TownAdminCommands} from '@models/town-admin-commands';
import {SectorAdminCommands} from '@models/sector-admin-commands';
import {SubSectorAdminCommands} from '@models/sub-sector-admin-commands';
import {BoardAdminCommands} from '@models/board-admin-commands';
import {ProductTypeAdminCommands} from '@models/product-type-admin-commands';
import {ProductSubTypeAdminCommands} from '@models/product-sub-type-admin-commands';
import {Group} from '@models/group';
import {OriginAdminCommands} from '@homeModels/origin-admin-command';
import {mapToGroupByCode} from '@core/utils';
import {Excel, ExcelColumns, ExcludeFromExcel} from '@shared/to-excel-columns';
import {Getter, plainToInstance, Transform} from '@shared/plain-to-instance';


type AdminCommandClasses = {
    [K in AdminCommand.Type]: any
}

@Getter
@Excel({fileName: 'adminCommands'})
export class AdminCommand<T = TableDataProvider> {
    @ExcludeFromExcel
    @Transform(AdminCommand.transformCommandRequest)
    readonly request: T;

    readonly type: AdminCommand.Type;

    readonly sender: string;

    readonly status: AdminCommand.Status;

    readonly timestamp: string;

    readonly commandId: number;

    @Transform(AdminCommand.transformRejectionCause)
    readonly rejectionCause: string[];

    readonly securityId: string;

    @Transform(mapToGroupByCode, 'groupCode')
    @ExcelColumns({title: 'Group Code', valuePath: 'group.code'})
    readonly group: Group;

    get isProgress(): boolean {
        return this._isState(AdminCommand.Status.AWAITING_RESPONSE);
    }

    get isFailed(): boolean {
        return this._isState(AdminCommand.Status.FAILURE);
    }

    get isSuccess(): boolean {
        return this._isState(AdminCommand.Status.SUCCESS);
    }

    get isPartialSuccess(): boolean {
        return this._isState(AdminCommand.Status.PARTIAL_SUCCESS);
    }

    get isAwaitingResponse(): boolean {
        return this._isState(AdminCommand.Status.AWAITING_RESPONSE);
    }

    private static transformRejectionCause(rejectionCause: string): string[] {
        return (rejectionCause || '')
            .replace(/[\[\]]/g, ``)
            .split(', ')
            .filter(item => item);

    }

    private static transformCommandRequest(request: any, {type}: { type: AdminCommand.Type }) {
        const adminCommandClasses: AdminCommandClasses = {
            CHANGE_SECURITY_STATE: InstrumentAdminCommands.ChangeState,
            CHANGE_SECURITY_GROUP: InstrumentAdminCommands.ChangeGroup,
            APPLY_CSD_RECORD: CsdFileAdminCommands.ApplyRecordAdminCommand,
            BLOCK_SHAREHOLDER: ShareholderAdminCommands.Block,
            CREATE_TRADE: TradeAdminCommands.CreateTrade,
            TRANSFER_SHARE: ShareholderAdminCommands.TransferShare,
            CHANGE_SYSTEM_STATE: SystemStateAdminCommands.Change,
            ORDER_ADMINISTRATION: (
                (request as OrderAdminCommands.Insert).request
                    ? (request as OrderAdminCommands.Insert).request.side
                        ? OrderAdminCommands.Insert
                        : OrderAdminCommands.Update
                    : OrderAdminCommands.Cancel
            ),
            ADD_SCHEDULE: ScheduleAdminCommands.Add,
            CANCEL_SCHEDULE: ScheduleAdminCommands.Cancel,
            BLOCK_INVESTOR: InvestorAdminCommands.Block,
            BLOCK_BROKER: BrokerAdminCommands.Block,
            SET_BROKER_CREDIT: BrokerAdminCommands.SetCredit,
            ADD_OR_UPDATE_WEEKEND: WeekendAdminCommands.Update,
            ADD_OR_UPDATE_HOLIDAYS: (request as HolidayAdminCommands.Add).holidays
                ? HolidayAdminCommands.Add
                : HolidayAdminCommands.Update,
            CREATE_PRODUCT: ProductAdminCommands.Create,
            UPDATE_PRODUCT: ProductAdminCommands.Update,
            UPDATE_PRODUCT_SELL_AVAILABILITY: ProductAdminCommands.UpdateSellAvailability,
            PURGE_SECURITY: InstrumentAdminCommands.Purge,
            UPDATE_SECURITY_SPECIFICATION: InstrumentAdminCommands.Update,
            IMMEDIATE_UPDATE_SECURITY_SPECIFICATION: InstrumentAdminCommands.ImmediateEditInstrument,
            UPDATE_SECURITY_REFERENCE_PRICE: InstrumentAdminCommands.UpdateReferencePrice,
            CHANGE_SECURITY_STATIC_PRICE_BAND_PERCENTAGE: InstrumentAdminCommands.ChangePriceBandPercentage,
            UPDATE_STATIC_PRICE_BAND: InstrumentAdminCommands.UpdateStaticPriceBand,
            ADD_OR_UPDATE_RULE: RuleAdminCommands.Add,
            CLEAR_RULE: RuleAdminCommands.Add,
            SAVE_ALL: SaveAdminCommands.Save,
            DIVIDENDS_CORPORATE_ACTION: CorporateActionAdminCommands.Dividends,
            CUSTOM_CORPORATE_ACTION: CorporateActionAdminCommands.Custom,
            BONUS_RIGHTS_CORPORATE_ACTION: CorporateActionAdminCommands.BonusRights,
            BONUS_CORPORATE_ACTION: CorporateActionAdminCommands.Bonus,
            RIGHTS_CORPORATE_ACTION: CorporateActionAdminCommands.Rights,
            NOTIFY_MARKET: NotifyMarketAdminCommand.Notify,
            ADD_TRADER: TraderAdminCommands.Add,
            DELETE_TRADER: TraderAdminCommands.Delete,
            CREATE_COMPANY: CompanyAdminCommand.Create,
            UPDATE_COMPANY: CompanyAdminCommand.Update,
            DELETE_COMPANY: CompanyAdminCommand.Delete,
            PURGE_ORDERS: PurgeOrder.Request,
            ADD_SECURITY: InstrumentAdminCommands.Add,
            ADD_BROKER: BrokerAdminCommands.CreateUpdate,
            EDIT_BROKER: BrokerAdminCommands.CreateUpdate,
            DELETE_PRODUCT: ProductAdminCommands.Delete,
            DELETE_HOLIDAY: HolidayAdminCommands.Delete,
            DELETE_WEEKEND: WeekendAdminCommands.Delete,
            DELETE_GROUP: GroupAdminCommands.Delete,
            CREATE_GROUP: GroupAdminCommands.Create,
            UPDATE_GROUP: GroupAdminCommands.Update,
            CHANGE_GROUP_STATE: GroupAdminCommands.ChangeState,
            CHANGE_GROUP_STATIC_PRICE_BAND: GroupAdminCommands.ChangePriceBand,
            CANCEL_SECURITY_DEFERRED_OPENING: InstrumentAdminCommands.CancelDefer,
            DEFER_SECURITY_OPENING: InstrumentAdminCommands.Defer,
            CHANGE_SECURITY_SHORT_SELL_STATE: InstrumentAdminCommands.ChangeShortSellState,
            CANCEL_TRADE: Trade.CancelTrade,
            ADD_GROUP_BASED_BLOCK_ENTRY: GroupBaseBlockEntryAdminCommand.Create,
            REMOVE_GROUP_BASED_BLOCK_ENTRY: GroupBaseBlockEntryAdminCommand.Delete,
            CHANGE_GROUP_CREDIT_CHECKING_STATUS: GroupAdminCommands.CreditChecking,
            CHANGE_BROKER_CREDIT_CHECKING_STATUS: BrokerAdminCommands.CreditChecking,
            CHANGE_SYSTEM_CREDIT_CHECKING_STATUS: SystemAdminCommands.CreditChecking,
            CREATE_SECTOR: SectorAdminCommands.Create,
            UPDATE_SECTOR: SectorAdminCommands.Create,
            DELETE_SECTOR: SectorAdminCommands.Delete,
            CREATE_SUB_SECTOR: SubSectorAdminCommands.Create,
            UPDATE_SUB_SECTOR: SubSectorAdminCommands.Update,
            DELETE_SUB_SECTOR: SubSectorAdminCommands.Delete,
            CREATE_BOARD: BoardAdminCommands.Create,
            UPDATE_BOARD: BoardAdminCommands.Create,
            DELETE_BOARD: BoardAdminCommands.Delete,
            CREATE_TOWN: TownAdminCommands.Create,
            UPDATE_TOWN: TownAdminCommands.Create,
            CREATE_PRODUCT_TYPE: ProductTypeAdminCommands.Create,
            UPDATE_PRODUCT_TYPE: ProductTypeAdminCommands.Create,
            DELETE_PRODUCT_TYPE: ProductTypeAdminCommands.Delete,
            CREATE_PRODUCT_SUB_TYPE: ProductSubTypeAdminCommands.Create,
            UPDATE_PRODUCT_SUB_TYPE: ProductSubTypeAdminCommands.Update,
            DELETE_PRODUCT_SUB_TYPE: ProductSubTypeAdminCommands.Delete,
            UPDATE_ORIGIN_PRIORITY: OriginAdminCommands.UpdatePriority,
            CHANGE_OPENING_UPDATE_PRICE_BAND_STATUS: GroupAdminCommands.UpdatePriceBandOnOpening,
            COPY_GROUP_BASED_ENTRIES: GroupAdminCommands.CopyAccessMatrix,


            INITIALIZE_MARKET: undefined,
            TRADING_DAY_TIME_TABLES: undefined,
            RAISE_POST_SESSION_MMTP_MESSAGES: undefined,
            REMOVE_INVALIDATED_SESSION_ORDERS: undefined,
            REMOVE_TODAY_INVALIDATED_ORDERS: undefined,
            DECLARE_REMAINING_ORDERS: undefined,
            CLEAR_ALL_RULES: undefined,
            UPDATE_SHAREHOLDER_INVESTOR_LINK: undefined,
            UPDATE_SHAREHOLDER_POSITION: undefined,
            DELETE_SHAREHOLDER: undefined,
            UPDATE_INVESTOR: undefined,
            DELETE_INVESTOR: undefined,
            SHUTDOWN: undefined
        };

        return adminCommandClasses[type]
            ? plainToInstance(adminCommandClasses[type], request)
            : request;
    }

    private _isState(state: AdminCommand.Status): boolean {
        return this.status === state;
    }
}

export namespace AdminCommand {
    export enum Type {
        INITIALIZE_MARKET = 'INITIALIZE_MARKET',
        NOTIFY_MARKET = 'NOTIFY_MARKET',
        APPLY_CSD_RECORD = 'APPLY_CSD_RECORD',
        BLOCK_SHAREHOLDER = 'BLOCK_SHAREHOLDER',
        ORDER_ADMINISTRATION = 'ORDER_ADMINISTRATION',
        CANCEL_TRADE = 'CANCEL_TRADE',
        CREATE_TRADE = 'CREATE_TRADE',
        REMOVE_INVALIDATED_SESSION_ORDERS = 'REMOVE_INVALIDATED_SESSION_ORDERS',
        REMOVE_TODAY_INVALIDATED_ORDERS = 'REMOVE_TODAY_INVALIDATED_ORDERS',
        DECLARE_REMAINING_ORDERS = 'DECLARE_REMAINING_ORDERS',
        PURGE_ORDERS = 'PURGE_ORDERS',
        SAVE_ALL = 'SAVE_ALL',
        SHUTDOWN = 'SHUTDOWN',
        TRANSFER_SHARE = 'TRANSFER_SHARE',
        CHANGE_SECURITY_STATE = 'CHANGE_SECURITY_STATE',
        TRADING_DAY_TIME_TABLES = 'TRADING_DAY_TIME_TABLES',
        BONUS_CORPORATE_ACTION = 'BONUS_CORPORATE_ACTION',
        CUSTOM_CORPORATE_ACTION = 'CUSTOM_CORPORATE_ACTION',
        RIGHTS_CORPORATE_ACTION = 'RIGHTS_CORPORATE_ACTION',
        DIVIDENDS_CORPORATE_ACTION = 'DIVIDENDS_CORPORATE_ACTION',
        BONUS_RIGHTS_CORPORATE_ACTION = 'BONUS_RIGHTS_CORPORATE_ACTION',
        RAISE_POST_SESSION_MMTP_MESSAGES = 'RAISE_POST_SESSION_MMTP_MESSAGES',
        UPDATE_SECURITY_SPECIFICATION = 'UPDATE_SECURITY_SPECIFICATION',
        IMMEDIATE_UPDATE_SECURITY_SPECIFICATION = 'IMMEDIATE_UPDATE_SECURITY_SPECIFICATION',
        PURGE_SECURITY = 'PURGE_SECURITY',
        ADD_SECURITY = 'ADD_SECURITY',
        CHANGE_SYSTEM_STATE = 'CHANGE_SYSTEM_STATE',
        ADD_OR_UPDATE_RULE = 'ADD_OR_UPDATE_RULE',
        CLEAR_RULE = 'CLEAR_RULE',
        CLEAR_ALL_RULES = 'CLEAR_ALL_RULES',
        CREATE_PRODUCT = 'CREATE_PRODUCT',
        CREATE_COMPANY = 'CREATE_COMPANY',
        UPDATE_PRODUCT = 'UPDATE_PRODUCT',
        UPDATE_COMPANY = 'UPDATE_COMPANY',
        DELETE_PRODUCT = 'DELETE_PRODUCT',
        DELETE_COMPANY = 'DELETE_COMPANY',
        CREATE_SECTOR = 'CREATE_SECTOR',
        UPDATE_SECTOR = 'UPDATE_SECTOR',
        DELETE_SECTOR = 'DELETE_SECTOR',
        CREATE_SUB_SECTOR = 'CREATE_SUB_SECTOR',
        UPDATE_SUB_SECTOR = 'UPDATE_SUB_SECTOR',
        DELETE_SUB_SECTOR = 'DELETE_SUB_SECTOR',
        CREATE_BOARD = 'CREATE_BOARD',
        UPDATE_BOARD = 'UPDATE_BOARD',
        DELETE_BOARD = 'DELETE_BOARD',
        CREATE_TOWN = 'CREATE_TOWN',
        UPDATE_TOWN = 'UPDATE_TOWN',
        BLOCK_INVESTOR = 'BLOCK_INVESTOR',
        ADD_BROKER = 'ADD_BROKER',
        EDIT_BROKER = 'EDIT_BROKER',
        SET_BROKER_CREDIT = 'SET_BROKER_CREDIT',
        BLOCK_BROKER = 'BLOCK_BROKER',
        ADD_SCHEDULE = 'ADD_SCHEDULE',
        CANCEL_SCHEDULE = 'CANCEL_SCHEDULE',
        ADD_TRADER = 'ADD_TRADER',
        DELETE_TRADER = 'DELETE_TRADER',
        UPDATE_INVESTOR = 'UPDATE_INVESTOR',
        UPDATE_SHAREHOLDER_INVESTOR_LINK = 'UPDATE_SHAREHOLDER_INVESTOR_LINK',
        UPDATE_SHAREHOLDER_POSITION = 'UPDATE_SHAREHOLDER_POSITION',
        DELETE_GROUP = 'DELETE_GROUP',
        ADD_OR_UPDATE_HOLIDAYS = 'ADD_OR_UPDATE_HOLIDAYS',
        ADD_OR_UPDATE_WEEKEND = 'ADD_OR_UPDATE_WEEKEND',
        DELETE_WEEKEND = 'DELETE_WEEKEND',
        DELETE_HOLIDAY = 'DELETE_HOLIDAY',
        CREATE_GROUP = 'CREATE_GROUP',
        UPDATE_GROUP = 'UPDATE_GROUP',
        CHANGE_GROUP_STATE = 'CHANGE_GROUP_STATE',
        CHANGE_GROUP_STATIC_PRICE_BAND = 'CHANGE_GROUP_STATIC_PRICE_BAND',
        DEFER_SECURITY_OPENING = 'DEFER_SECURITY_OPENING',
        CANCEL_SECURITY_DEFERRED_OPENING = 'CANCEL_SECURITY_DEFERRED_OPENING',
        ADD_GROUP_BASED_BLOCK_ENTRY = 'ADD_GROUP_BASED_BLOCK_ENTRY',
        REMOVE_GROUP_BASED_BLOCK_ENTRY = 'REMOVE_GROUP_BASED_BLOCK_ENTRY',
        CHANGE_SECURITY_SHORT_SELL_STATE = 'CHANGE_SECURITY_SHORT_SELL_STATE',
        CHANGE_GROUP_CREDIT_CHECKING_STATUS = 'CHANGE_GROUP_CREDIT_CHECKING_STATUS',
        CHANGE_BROKER_CREDIT_CHECKING_STATUS = 'CHANGE_BROKER_CREDIT_CHECKING_STATUS',
        CHANGE_SYSTEM_CREDIT_CHECKING_STATUS = 'CHANGE_SYSTEM_CREDIT_CHECKING_STATUS',
        UPDATE_SECURITY_REFERENCE_PRICE = 'UPDATE_SECURITY_REFERENCE_PRICE',
        CHANGE_SECURITY_STATIC_PRICE_BAND_PERCENTAGE = 'CHANGE_SECURITY_STATIC_PRICE_BAND_PERCENTAGE',
        UPDATE_STATIC_PRICE_BAND = 'UPDATE_STATIC_PRICE_BAND',
        CREATE_PRODUCT_TYPE = 'CREATE_PRODUCT_TYPE',
        UPDATE_PRODUCT_TYPE = 'UPDATE_PRODUCT_TYPE',
        DELETE_PRODUCT_TYPE = 'DELETE_PRODUCT_TYPE',
        CREATE_PRODUCT_SUB_TYPE = 'CREATE_PRODUCT_SUB_TYPE',
        UPDATE_PRODUCT_SUB_TYPE = 'UPDATE_PRODUCT_SUB_TYPE',
        DELETE_PRODUCT_SUB_TYPE = 'DELETE_PRODUCT_SUB_TYPE',
        CHANGE_SECURITY_GROUP = 'CHANGE_SECURITY_GROUP',
        UPDATE_ORIGIN_PRIORITY = 'UPDATE_ORIGIN_PRIORITY',
        CHANGE_OPENING_UPDATE_PRICE_BAND_STATUS = 'CHANGE_OPENING_UPDATE_PRICE_BAND_STATUS',
        UPDATE_PRODUCT_SELL_AVAILABILITY = 'UPDATE_PRODUCT_SELL_AVAILABILITY',
        COPY_GROUP_BASED_ENTRIES = 'COPY_GROUP_BASED_ENTRIES',
        DELETE_SHAREHOLDER = 'DELETE_SHAREHOLDER',
        DELETE_INVESTOR = 'DELETE_INVESTOR'
    }

    export enum QueuedType {
        CREATE_PRODUCT = 'CREATE_PRODUCT',
        ADD_SECURITY = 'ADD_SECURITY',
        UPDATE_PRODUCT = 'UPDATE_PRODUCT',
        UPDATE_SECURITY_SPECIFICATION = 'UPDATE_SECURITY_SPECIFICATION',
        DIVIDENDS_CORPORATE_ACTION = 'DIVIDENDS_CORPORATE_ACTION',
        RIGHTS_CORPORATE_ACTION = 'RIGHTS_CORPORATE_ACTION',
        BONUS_CORPORATE_ACTION = 'BONUS_CORPORATE_ACTION',
        BONUS_RIGHTS_CORPORATE_ACTION = 'BONUS_RIGHTS_CORPORATE_ACTION',
        CUSTOM_CORPORATE_ACTION = 'CUSTOM_CORPORATE_ACTION',
        PURGE_SECURITY = 'PURGE_SECURITY',
        DELETE_PRODUCT = 'DELETE_PRODUCT',
        CHANGE_GROUP_STATIC_PRICE_BAND = 'CHANGE_GROUP_STATIC_PRICE_BAND'
    }

    export enum Status {
        AWAITING_RESPONSE = 'AWAITING_RESPONSE',
        SUCCESS = 'SUCCESS',
        FAILURE = 'FAILURE',
        PARTIAL_SUCCESS = 'PARTIAL_SUCCESS'
    }

    export enum QueuedCorporateActionType {
        DIVIDENDS_CORPORATE_ACTION = 'DIVIDENDS_CORPORATE_ACTION',
        RIGHTS_CORPORATE_ACTION = 'RIGHTS_CORPORATE_ACTION',
        BONUS_CORPORATE_ACTION = 'BONUS_CORPORATE_ACTION',
        BONUS_RIGHTS_CORPORATE_ACTION = 'BONUS_RIGHTS_CORPORATE_ACTION',
        CUSTOM_CORPORATE_ACTION = 'CUSTOM_CORPORATE_ACTION'
    }
}
