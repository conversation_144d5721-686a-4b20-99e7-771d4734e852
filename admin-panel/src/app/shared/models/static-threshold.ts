import {MutableAny} from '@models/form-group-raw-value';
import {FormControl, FormGroup} from '@angular/forms';
import {Type} from '@shared/plain-to-instance/type';
import {PriceBandPercentage} from '@models/price-band-percentage';
import {Getter} from '../plain-to-instance/getter';
import {staticPriceBand} from '@models/static-price-band';

@Getter
export class PercentageStaticThreshold {
    @Type(PriceBandPercentage)
    readonly priceBandPercentage: PriceBandPercentage;
}

@Getter
export class staticPriceBandThreshold {
    @Type(staticPriceBand)
    readonly staticPriceBand: staticPriceBand;
}


export namespace StaticThreshold {
    export class PercentageForm implements MutableAny<PercentageStaticThreshold> {
        priceBandPercentage: FormGroup<PriceBandPercentage.Form>;
    }

    export class staticPriceBandForm implements MutableAny<staticPriceBandThreshold> {
        staticPriceBand: FormGroup<staticPriceBand.Form>;
    }

    export class ReferencePriceForm {
        referencePrice: FormControl<string>;
        shouldUpdateClosingPrice: FormControl<boolean>;
    }
}
