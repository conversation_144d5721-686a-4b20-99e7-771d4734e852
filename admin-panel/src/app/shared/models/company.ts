import {Name} from '@shared/models/name';
import {Type} from '@shared/plain-to-instance/type';
import {FormControl, FormGroup} from '@angular/forms';
import {Sector} from '@models/sector';
import {SubSector} from '@models/sub-sector';
import {Excel, ExcelColumns, ExcludeFromExcel} from '@shared/to-excel-columns';
import {Getter, Setter} from '@shared/plain-to-instance';

@Getter
@Excel({fileName: 'companies'})
export class Company {
    readonly code: string;

    @Type(Name)
    @ExcelColumns({title: 'EN shortName', valuePath: 'shortName.en'}, {title: 'FA shortName', valuePath: 'shortName.fa'})
    readonly shortName: Name;

    @Type(Name)
    @ExcelColumns({title: 'EN fullName', valuePath: 'fullName.en'}, {title: 'FA fulName', valuePath: 'fullName.fa'})
    readonly fullName: Name;

    @Setter
    @ExcludeFromExcel
    sector: Sector;

    readonly sectorCode: string;

    @Setter
    @ExcludeFromExcel
    subSector: SubSector;

    readonly subSectorCode: string;

    readonly creationDate: string;

    readonly updateDate: string;

    @Setter
    @ExcludeFromExcel
    searchExpression: string;
}

export class CompanyDetails {
    readonly code: string;

    @Type(Name)
    readonly shortName: Name;

    @Type(Name)
    readonly fullName: Name;

    readonly sectorCode: string;

    readonly sectorName: string;

    readonly subSectorCode: string;

    readonly subSectorName: string;

    readonly creationDate: string;

    readonly updateDate: string;
}

export namespace Company {
    export class NewForm {
        code: FormControl<string>;
        shortName: FormGroup<Name.Form>;
        fullName: FormGroup<Name.Form>;
        sectorCode: FormControl<string>;
        subSectorCode: FormControl<string>;
    }

    export class UpdateForm {
        shortName: FormGroup<Name.Form>;
        fullName: FormGroup<Name.Form>;
        sectorCode: FormControl<string>;
        subSectorCode: FormControl<string>;
    }

    export class Spec {
        @Type(Name)
        readonly shortName: Name;

        @Type(Name)
        readonly fullName: Name;

        readonly sectorCode: string;

        readonly subSectorCode: string;
    }
}
