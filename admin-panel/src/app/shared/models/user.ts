import {Excel} from '../to-excel-columns/excel';
import {Getter} from '../plain-to-instance/getter';

@Getter
@Excel({fileName: 'users'})
export class User {
    readonly blocked: boolean;

    readonly roles: string[];

    readonly username: string;

    constructor(username?: string, roles?: string[], blocked?: boolean) {
        this.username = username;
        this.roles = roles;
        this.blocked = blocked;
    }
}
