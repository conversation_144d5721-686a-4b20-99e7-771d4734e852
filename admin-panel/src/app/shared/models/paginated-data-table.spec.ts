import {Subject} from 'rxjs';
import {PaginatedDataTable} from './paginated-data-table';
import {ChangeDetectorRef, ElementRef} from '@angular/core';
import {TestBed} from '@angular/core/testing';
import {mockChangeDetectorRef} from '@test/harness/fake/mock-change-detector-ref';
import {Page} from '@models/page';

class ConcretePaginatedDataTable extends PaginatedDataTable<any> {
    _paginator: any = {page: new Subject()};
    _tableContainer: ElementRef<HTMLDivElement> = {
        nativeElement: {
            getBoundingClientRect: () => ({height: 500})
        }
    } as ElementRef<HTMLDivElement>;

    _fetchPageData(): void {
        // Mock implementation for testing
        // Simulate data loading by setting _pageData
        const mockPage = new Page<any>([{id: 1}, {id: 2}]);
        mockPage.element.total = 10;
        mockPage.page.size = 5;
        this._pageData = mockPage;
    }
}

describe('PaginatedDataTable', () => {
    let table: ConcretePaginatedDataTable;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [{
                provide: ChangeDetectorRef, useValue: mockChangeDetectorRef
            }]
        }).compileComponents();

        TestBed.runInInjectionContext(() => {
            table = new ConcretePaginatedDataTable();
        });

        spyOn(table, '_fetchPageData').and.callThrough();
    });

    it('should subscribe to paginator page changes and update filter params', () => {
        const mockPageEvent = {pageIndex: 2, pageSize: 10};
        table['_subscribeOnPageChange']();

        // Emit a page event
        table._paginator.page.next(mockPageEvent as any);

        // Check if filter params are updated correctly
        expect(table['filterProperty'].filterParams.page).toBe('2');

        // Verify `_fetchPageData` is called
        expect(table._fetchPageData).toHaveBeenCalled();
    });

    describe('isPageDataLoaded signal', () => {
        it('should initialize with false value', () => {
            // Check initial value
            expect(table.isPageDataLoaded()).toBe(false);
        });

        it('should set to true when _pageData is set', () => {
            // Initially false
            expect(table.isPageDataLoaded()).toBe(false);

            // Set page data by calling _fetchPageData
            table['_fetchPageData']();

            // Should be set to true
            expect(table.isPageDataLoaded()).toBe(true);
        });
    });

    describe('page data operations', () => {
        it('should calculate page size based on container height', () => {
            // Call the method
            table['_calcPageSize']();

            // Check if size is calculated correctly
            // Using the mock height of 500 from the tableContainer
            expect(table.filterProperty.filterParams.size).toBeTruthy();
        });

        it('should handle data source operations correctly', () => {
            // Set page data
            table['_fetchPageData']();

            // Check data source getter
            expect(table.dataSource).toEqual([{id: 1}, {id: 2}]);
            expect(table.dataSourceLength).toBe(10);
            expect(table.dataSourcePageSize).toBe(5);
        });

        it('should determine paginator visibility correctly', () => {
            // Set page data
            table['_fetchPageData']();

            // When total > page size, paginator should be visible
            expect(table.paginatorClass).toBe('');

            // Create a new page with total <= page size
            const mockPage = new Page<any>([{id: 1}, {id: 2}]);
            mockPage.element.total = 5;
            mockPage.page.size = 5;
            table['_pageData'] = mockPage;

            // Paginator should be invisible
            expect(table.paginatorClass).toBe('invisible');
        });
    });
});
