import {Translate<PERSON><PERSON>} from '@shared/enums/translate-key';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {DecimalPipe} from '@angular/common';
import {Trade} from '@models/trade';
import {Getter} from '../plain-to-instance/getter';
import {Transform} from '@shared/plain-to-instance/transform';
import {CustomDecimalPipe} from '@shared/pipes/custom-decimal.pipe';
import {TableDataProvider} from '@models/table-representation/table-representable';
import {SimpleTableData} from '@models/table-representation/simple-table-representation';
import {TranslatePipe} from '@ngx-translate/core';
import {getInstrumentById} from '@core/utils';
import {Instrument} from '@homeModels/instrument';

export namespace TradeAdminCommands {
    @Getter
    export class CreateTrade extends TableDataProvider implements Trade.Create {
        readonly securityId: string;

        @Transform((price) => +price)
        readonly price: number;

        readonly quantity: number;

        readonly hasImpactOnMarketStatistics: boolean;

        readonly entryDate: Date;

        readonly seller: Trade.Side;

        readonly buyer: Trade.Side;

        get instrument(): Instrument.Single { return getInstrumentById(this.securityId); };

        override getTableData(): SimpleTableData[] {
            const basicInfoData = [
                {
                    title: TranslateKey.instrument,
                    value: this.instrument.mnemonic,
                    pipeToken: SelectByLanguagePipe
                },
                {
                    title: TranslateKey.totalQuantity,
                    value: this.quantity,
                    pipeToken: DecimalPipe
                },
                {
                    title: TranslateKey.price,
                    value: this.price,
                    pipeToken: CustomDecimalPipe
                },
                {
                    title: TranslateKey.withoutImpact,
                    value: (!this.hasImpactOnMarketStatistics).toString(),
                    pipeToken: TranslatePipe
                }
            ];
            const buyerData = [
                {
                    title: TranslateKey.broker,
                    value: this.buyer.brokerId
                },
                {
                    title: TranslateKey.shareholder,
                    value: this.buyer.shareholderId
                },
                {
                    title: TranslateKey.giveUpBroker,
                    value: this.buyer.clearingData.giveUpBrokerId
                },
                {
                    title: TranslateKey.trader,
                    value: this.buyer.clearingData.traderId
                },
                {
                    title: TranslateKey.orderId,
                    value: this.buyer.clearingData.traderOrderNumber
                },
                {
                    title: TranslateKey.freeText,
                    value: this.buyer.clearingData.freeText
                },
                {
                    title: TranslateKey.townCode,
                    value: this.buyer.clearingData.brokerBusinessIdentificationCode.townCode
                },
                {
                    title: TranslateKey.bankCode,
                    value: this.buyer.clearingData.brokerBusinessIdentificationCode.bankCode
                },
                {
                    title: TranslateKey.branchCode,
                    value: this.buyer.clearingData.brokerBusinessIdentificationCode.branchCode
                }
            ];
            const sellerData = [
                {
                    title: TranslateKey.broker,
                    value: this.seller.brokerId
                },
                {
                    title: TranslateKey.shareholder,
                    value: this.seller.shareholderId
                },
                {
                    title: TranslateKey.giveUpBroker,
                    value: this.seller.clearingData.giveUpBrokerId
                },
                {
                    title: TranslateKey.trader,
                    value: this.seller.clearingData.traderId
                },
                {
                    title: TranslateKey.orderId,
                    value: this.seller.clearingData.traderOrderNumber
                },
                {
                    title: TranslateKey.freeText,
                    value: this.seller.clearingData.freeText
                },
                {
                    title: TranslateKey.townCode,
                    value: this.seller.clearingData.brokerBusinessIdentificationCode.townCode
                },
                {
                    title: TranslateKey.bankCode,
                    value: this.seller.clearingData.brokerBusinessIdentificationCode.bankCode
                },
                {
                    title: TranslateKey.branchCode,
                    value: this.seller.clearingData.brokerBusinessIdentificationCode.branchCode
                }
            ];

            return [
                {
                    groupName: TranslateKey.basicInfo,
                    data: basicInfoData
                },
                {
                    groupName: TranslateKey.buyer,
                    data: buyerData
                },
                {
                    groupName: TranslateKey.seller,
                    data: sellerData
                }
            ];
        }
    }
}
