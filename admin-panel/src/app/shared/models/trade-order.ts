import {Order} from '@models/order/order';
import {TechnicalOrigin} from '@enums/technical-origins';
import {OrderType} from '@enums/order-type';
import {ValidityQualifierType} from '@enums/validity-qualifier-type';
import {Getter, Transform} from '@shared/plain-to-instance';
import {mapToInstrument, mapToOrigin} from '@core/utils';
import {Instrument} from '@homeModels/instrument';

@Getter
export class TradeOrder extends Order {
    readonly priorityDateTime: string;

    readonly initialQuantity: number;

    readonly internalSubscriberReference: string;

    readonly sequenceId: string;

    readonly displayedQuantity: number;

    readonly brokerId: string;

    readonly clearingData: any;

    readonly disclosedQuantity: number;

    readonly price: string;

    readonly quantity: number;

    readonly shareholderId: string;

    readonly side: Order.Side;

    readonly type: OrderType;

    readonly technicalOrigin: TechnicalOrigin;

    readonly validityQualifierType: ValidityQualifierType;

    @Transform(mapToOrigin, 'originId')
    readonly origin: string;

    @Transform(mapToInstrument, 'securityId')
    readonly instrument: Instrument.Single;
}
