import {Type} from '@angular/core';
import {DateRange} from './date-range';
import {GDate} from 'mb-date/dist/src/gdate';
import {JDate} from 'mb-date';
import {MbDatepickerComponent} from '@shared/modules/datepicker/mb-datepicker.component';

export class DateRangeAvailability {
    private readonly _composeDate: Type<GDate | JDate>;
    private readonly _dateRange: DateRange;

    constructor(composeDate: Type<GDate | JDate>, dateRange: DateRange) {
        this._composeDate = composeDate;
        this._dateRange = dateRange;
    }

    getAvailableYears(): number[] {
        const result: number[] = [];
        const date = this._resetYear(new this._composeDate());
        const START = date.getFullYear() - 25;
        const END = date.getFullYear() + 25;

        for (let year = START; year <= END; year++) {
            date.setFullYear(year);
            if (this._dateRange.isInclude(date.gDate)) {
                result.push(+year);
            }
        }

        return result;
    }

    getAvailableMonths(d: Date): number[] {
        const result: number[] = [];
        const date = this._resetYear(new this._composeDate(d));

        for (let month = 0; month < 12; month++) {
            date.setMonth(month + MbDatepickerComponent.firstMonthIndex(date));
            if (this._dateRange.isInclude(date.gDate)) {
                result.push(+month);
            }
        }

        return result;
    }

    private _resetYear(date: GDate | JDate): GDate | JDate {
        date.setMonth(MbDatepickerComponent.firstMonthIndex(date));
        date.setDate(1);

        return date;
    }
}
