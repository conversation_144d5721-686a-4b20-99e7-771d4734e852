import {Component, ElementRef, forwardRef, ViewChild} from '@angular/core';
import {ValueAccessor} from '@shared/value-accessor';
import {NG_VALUE_ACCESSOR} from '@angular/forms';
import {MbDatepickerComponent} from '@shared/modules/datepicker/mb-datepicker.component';
import {StoreService} from '@shared/services/store.service';

@Component({
    selector: 'app-datepicker',
    templateUrl: './datepicker.component.html',
    styleUrls: ['./datepicker.component.scss'],
    providers: [{
        provide: NG_VALUE_ACCESSOR,
        useExisting: forwardRef(() => DatepickerComponent),
        multi: true
    }],
    standalone: false
})
export class DatepickerComponent extends ValueAccessor<Date> {
    readonly systemDate = StoreService.systemDateTime;

    private _isOpen: boolean;
    get isOpen(): boolean { return this._isOpen; }

    private _selectedDate: Date;
    get selectedDate(): Date { return this._selectedDate; }

    set selectedDate(value: Date) { this._selectedDate = value; }

    @ViewChild(MbDatepickerComponent)
    datepicker: MbDatepickerComponent;

    @ViewChild('datePickerContainer')
    private _datePickerContainer: ElementRef<HTMLDivElement>;

    constructor(public elementRef: ElementRef) { super(); }

    override writeValue(date: Date) {
        this.onChange(date);
        if (this._isOpen) {
            this._isOpen = false;
        }
    }

    open() {
        this._isOpen = true;

        const container = this.elementRef.nativeElement;
        const containerRect: DOMRect = container.getBoundingClientRect();

        const content = this._datePickerContainer.nativeElement;
        content.style.display = 'block';
        content.style.left = '';
        content.style.right = '';
        content.style.top = '';
        content.style.bottom = '';

        if (window.innerWidth < containerRect.x + 300) {
            content.style.right = '0';
        } else {
            content.style.left = containerRect.x + 'px';
        }

        if (window.innerHeight < containerRect.y + 395) {
            content.style.bottom = '0';
        } else {
            content.style.top = containerRect.y + 'px';
        }


        document.body.appendChild(content);
    }

    close() {
        this._isOpen = false;
        const content = this._datePickerContainer.nativeElement;
        content.style.display = 'none';
    }
}
