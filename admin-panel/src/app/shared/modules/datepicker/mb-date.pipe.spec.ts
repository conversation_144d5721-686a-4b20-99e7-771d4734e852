import {MbDatePipe} from './mb-date.pipe';
import {JDate} from 'mb-date';
import {UtilConstants} from '@constants/util-constants';

describe('MbDatePipe', () => {
    let pipe: MbDatePipe;

    beforeEach(() => {
        pipe = new MbDatePipe();
    });

    it('should return an empty string for an invalid value', () => {
        expect(pipe.transform(null, 'YYYY-MM-DD')).toBe('');
        expect(pipe.transform(undefined, 'YYYY-MM-DD')).toBe('');
        expect(pipe.transform('invalid', 'YYYY-MM-DD')).toBe('');
    });

    it('should parse and format a valid YYYYMMDD string', () => {
        const value = '20240115'; // Represents 15th January 2024
        const format = 'YYYY-MM-DD';
        spyOn(JDate.prototype, 'format').and.returnValue('2024-01-15');

        const result = pipe.transform(value, format);
        expect(result).toBe('2024-01-15');
        expect(JDate.prototype.format).toHaveBeenCalledWith(format);
    });

    it('should format a Date object correctly', () => {
        const value = new Date(2024, 0, 15); // 15th January 2024
        const format = 'YYYY-MM-DD';
        spyOn(JDate.prototype, 'format').and.returnValue('2024-01-15');

        const result = pipe.transform(value, format);
        expect(result).toBe('2024-01-15');
        expect(JDate.prototype.format).toHaveBeenCalledWith(format);
    });

    it('should handle milliseconds if present in original value', () => {
        const value = '2024-01-15T12:34:56.789';
        const format = UtilConstants.EXACT_DATE_TIME_FORMAT;
        spyOn(JDate.prototype, 'format').and.returnValue('2024-01-15 12:34:56');

        const result = pipe.transform(value, format);
        expect(result).toBe('2024-01-15 12:34:56.789');
        expect(JDate.prototype.format).toHaveBeenCalledWith(format);
    });

    it('should not add milliseconds if not present in original value', () => {
        const value = '2024-01-15T12:34:56';
        const format = UtilConstants.EXACT_DATE_TIME_FORMAT;
        spyOn(JDate.prototype, 'format').and.returnValue('2024-01-15 12:34:56');

        const result = pipe.transform(value, format);
        expect(result).toBe('2024-01-15 12:34:56');
        expect(JDate.prototype.format).toHaveBeenCalledWith(format);
    });

    it('should correctly handle non-string and non-date values', () => {
        const value = 20240115;
        const format = 'YYYY-MM-DD';
        spyOn(JDate.prototype, 'format').and.returnValue('2024-01-15');

        const result = pipe.transform(value, format);
        expect(result).toBe('2024-01-15');
        expect(JDate.prototype.format).toHaveBeenCalledWith(format);
    });
});
