<div class="d-flex justify-content-between mb-3" (click)="$event.stopPropagation()">
    <button
        mat-icon-button
        (click)="previousMonth()">
        <mat-icon>keyboard_arrow_down</mat-icon>
    </button>


    <button
        mat-icon-button
        (click)="forwardMonth()">
        <mat-icon>keyboard_arrow_up</mat-icon>
    </button>

    <div class="d-flex">
        <select
            class="form-control"
            [(ngModel)]="displayedMonth">
            <option *ngFor="let month of availableMonths" [value]="month">{{monthNames[month]}}</option>
        </select>

        <select
            class="form-control"
            [(ngModel)]="displayedYear">
            <option *ngFor="let year of availableYears" [value]="year">{{year}}</option>
        </select>
    </div>
</div>
