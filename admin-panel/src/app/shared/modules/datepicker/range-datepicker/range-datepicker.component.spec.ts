import {ComponentFixture, TestBed} from '@angular/core/testing';

import {RangeDatepickerComponent} from './range-datepicker.component';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';

describe('RangeDatepickerComponent', () => {
    let component: RangeDatepickerComponent;
    let fixture: ComponentFixture<RangeDatepickerComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [RangeDatepickerComponent],
            schemas: [CUSTOM_ELEMENTS_SCHEMA]
        })
            .compileComponents();

        fixture = TestBed.createComponent(RangeDatepickerComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
