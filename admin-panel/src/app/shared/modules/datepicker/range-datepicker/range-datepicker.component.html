<div #datePickerContainer class="datepicker">
    <app-calendar-navigation-bar
        [class.d-none]="!!from"
        [datepicker]="datepicker"></app-calendar-navigation-bar>

    <app-calendar-navigation-bar
        [class.d-none]="!from"
        [datepicker]="datepicker2"></app-calendar-navigation-bar>

    <div
        (click)="$event.stopPropagation()">
        <mb-datepicker
            #datepicker
            [today]="systemDate"
            [(ngModel)]="from"
            [class.d-none]="!!from"
            (ngModelChange)="onSelectFrom($event)">
        </mb-datepicker>
    </div>

    <mb-datepicker
        #datepicker2
        [today]="systemDate"
        [(ngModel)]="to"
        (ngModelChange)="onSelectTo()"
        [class.d-none]="!from"
        [periodHover]="true"
    >
    </mb-datepicker>
</div>
