import {NgModule} from '@angular/core';
import {MbDatepickerComponent} from './mb-datepicker.component';
import {CommonModule} from '@angular/common';
import {DateTriggerForDirective} from './date-trigger-for.directive';
import {FormsModule} from '@angular/forms';
import {DatepickerFooterDirective} from './datepicker-footer.directive';
import {MbDatePipe} from './mb-date.pipe';
import {DatepickerComponent} from './datepicker/datepicker.component';
import {CalendarNavigationBarComponent} from './calendar-navigation-bar/calendar-navigation-bar.component';
import {MatButtonModule} from '@angular/material/button';
import {MatIconModule} from '@angular/material/icon';
import {RangeDatepickerComponent} from '@shared/modules/datepicker/range-datepicker/range-datepicker.component';


@NgModule({
  declarations: [
      MbDatepickerComponent,
      DateTriggerForDirective,
      DatepickerFooterDirective,
      MbDatePipe,
      DatepickerComponent,
      RangeDatepickerComponent,
      CalendarNavigationBarComponent
  ],
    imports: [
        CommonModule,
        FormsModule,
        MatButtonModule,
        MatIconModule
    ],
  exports: [
      MbDatepickerComponent,
      DateTriggerForDirective,
      DatepickerFooterDirective,
      MbDatePipe,
      RangeDatepickerComponent,
      DatepickerComponent,
      CalendarNavigationBarComponent
  ]
})
export class MbDatepickerModule { }
