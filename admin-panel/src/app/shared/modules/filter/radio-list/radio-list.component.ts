import {Component, OnInit} from '@angular/core';
import {FilterComponent} from '../models/filter-component';
import {RadioListFilter} from './radio-list-filter';
import {KeyValue} from '../models/key-value';

@Component({
    selector: 'app-radio-list',
    templateUrl: './radio-list.component.html',
    standalone: false
})
export class RadioListComponent extends FilterComponent<RadioListFilter> implements OnInit {
    selectedItem: KeyValue<string, string>;

    ngOnInit(): void {
        this.selectedItem = this.templateData.value;
    }
}
