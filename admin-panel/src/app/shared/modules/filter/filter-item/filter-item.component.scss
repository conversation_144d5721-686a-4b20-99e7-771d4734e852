.mat-mdc-outlined-button {
    border-radius: 18px;
    font-weight: normal;
    text-align: center;
    padding: 0;

    &.mat-primary {
        background-color: #e8f0fe;
        border-color: transparent;
        color: #1967d2;

        * {
            color: #1967d2;
        }
    }

    ::ng-deep .mdc-button__label {
        display: flex !important;
        align-items: center !important;
    }
}

.mdc-icon-button {
    width: 25px;
    height: 25px;
    padding: 8px 0 0 0;
    position: absolute;
    display: none;
    margin-top: 1px;

    .mat-icon {
        font-size: 1.4rem!important;
        margin-top: -3px;
    }
}

:host:hover .mdc-icon-button {
    display: block;
}

:host-context([dir="ltr"]) .mdc-icon-button {
    right: 11px;
    left: auto;
}

:host-context([dir="rtl"]) .mdc-icon-button {
    right: auto;
    left: 11px;
}
