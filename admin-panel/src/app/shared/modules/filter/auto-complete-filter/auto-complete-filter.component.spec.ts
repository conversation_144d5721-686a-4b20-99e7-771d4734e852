import {AutoCompleteFilterComponent} from './auto-complete-filter.component';
import {SharedModule} from 'src/app/shared/shared.module';
import {StoreService} from '@shared/services/store.service';
import {TestUtils} from '@test/test-utils';
import {ComponentHarness} from '@test/harness/component-harness';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {Subject} from 'rxjs';
import {TranslateTestingModule} from '../../../../../translate-testing.module';

describe('AutoCompleteFilterComponent', () => {
    let ha: ComponentHarness<AutoCompleteFilterComponent>;

    beforeEach(async () => {
        StoreService.instruments = TestUtils.getInstruments().content;

        ha = new ComponentHarness(AutoCompleteFilterComponent, {
            declarations: [AutoCompleteFilterComponent],
            imports: [
                TranslateTestingModule,
                SharedModule
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
            detectChanges: false
        });

        (ha.component.templateData as any) = { valueChange: new Subject<void>() }
        await ha.detectChanges();
    });

    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });

    it('should handle onSelectOption with a string value', () => {
        const mockValue = 'testValue';
        (ha.component as any).templateData = {onChange() {}};
        spyOn(ha.component.templateData, 'onChange');

        ha.component.onSelectOption(mockValue);

        expect(ha.component.templateData.onChange).toHaveBeenCalledWith(mockValue);
    });
});
