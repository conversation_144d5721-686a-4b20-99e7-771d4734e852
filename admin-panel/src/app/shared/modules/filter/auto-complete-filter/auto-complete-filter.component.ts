import {ChangeDetectionStrategy, Component, ViewChild} from '@angular/core';
import {FilterComponent} from '../models/filter-component';
import {AutoCompleteFilter} from './auto-complete-filter';
import {AutoCompleteComponent} from '@modules/filter/components/auto-complete/auto-complete.component';
import {filter} from 'rxjs/operators';

@Component({
    selector: 'app-auto-complete-filter',
    templateUrl: './auto-complete-filter.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class AutoCompleteFilterComponent extends FilterComponent<AutoCompleteFilter<any>> {
    @ViewChild(AutoCompleteComponent)
    private _autoCompleteComponent: AutoCompleteComponent<any>;

    onSelectOption(value: any[] | string): void {
        if (typeof value === 'object') {
            const filter: string = value?.map(option => option[this.templateData.filterField]).join('|');
            this.templateData.onChange(filter);
        } else {
            this.templateData.onChange(value);
        }
    }

    ngOnInit(): void {
        this.templateData.valueChange
            .pipe(filter(value => value === null))
            .subscribe(() => {
                this._autoCompleteComponent.destroy();
            })
    }
}
