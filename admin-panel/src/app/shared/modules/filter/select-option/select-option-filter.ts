import {Filter} from '../models/filter';
import {FilterInitial} from '@modules/filter/models/filter-initial';
import {SelectOptionComponent} from '@modules/filter/select-option/select-option.component';

interface SelectOptionFilterInitial<D> extends FilterInitial {
    data: D;
    skipTranslatingOptions?: boolean;
    getTitle?: (option: { key, value }) => string;
}

export class SelectOptionFilter<D extends {key, value}[], E extends {key, value}> extends Filter<E> {
    readonly _templateComponent = SelectOptionComponent;

    readonly data: D;

    readonly skipTranslatingValue: boolean;

    getTitle?(option: {key, value}): string;

    constructor({
        data,
        skipTranslatingOptions,
        queryParam,
        getTitle,
        queryParamValueField = 'value'
    }: SelectOptionFilterInitial<D>) {

        super({queryParam, queryParamValueField});

        this.data = data;
        this.skipTranslatingValue = skipTranslatingOptions;
        this.getTitle = getTitle;
    }
}
