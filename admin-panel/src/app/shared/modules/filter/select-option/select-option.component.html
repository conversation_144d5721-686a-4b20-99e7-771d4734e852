<select
    class="form-control"
    [(ngModel)]="templateData.value"
    (ngModelChange)="templateData.onChange($event)">
    <option selected></option>
    <option
        *ngFor="let option of data"
        [ngValue]="option">
        @if (templateData.skipTranslatingValue) {
            {{ getOptionTitle(option) }}
        } @else {
            {{ getOptionTitle(option) | camelCase | translate }}
        }
    </option>
</select>
