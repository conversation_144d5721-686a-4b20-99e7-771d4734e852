import {NgModule} from '@angular/core';
import {SharedDeclarations} from '@modules/shared-declarations/shared-declarations';
import {SharedModule} from '../../shared.module';
import {FilterItemComponent} from '@modules/filter/filter-item/filter-item.component';
import {RadioListComponent} from '@modules/filter/radio-list/radio-list.component';
import {InputComponent} from '@modules/filter/input/input.component';
import {IMaskDirective} from 'angular-imask';
import {BsDatepickerModule} from 'ngx-bootstrap/datepicker';
import {RangeComponent} from '@modules/filter/range/range.component';
import {TimeComponent} from '@modules/filter/time/time.component';
import {DatepickerComponent} from '@modules/filter/datepicker/datepicker.component';
import {SelectOptionComponent} from '@modules/filter/select-option/select-option.component';
import {
    RangeDatepickerFilterComponent
} from '@modules/filter/range-datepicker-filter/range-datepicker-filter.component';
import {AutoCompleteFilterComponent} from '@modules/filter/auto-complete-filter/auto-complete-filter.component';
import {AutoCompleteComponent} from '@modules/filter/components/auto-complete/auto-complete.component';

@NgModule({
    declarations: [
        FilterItemComponent,
        RadioListComponent,
        InputComponent,
        DatepickerComponent,
        RangeDatepickerFilterComponent,
        SelectOptionComponent,
        RangeComponent,
        TimeComponent,

        AutoCompleteComponent,
        AutoCompleteFilterComponent
    ],
    exports: [
        FilterItemComponent
    ],
    imports: [
        SharedDeclarations,
        SharedModule,
        IMaskDirective,
        BsDatepickerModule
    ]
})
export class FilterModule {}
