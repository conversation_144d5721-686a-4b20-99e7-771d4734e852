import {Filter} from '../models/filter';
import {FilterInitial} from '../models/filter-initial';
import {InputComponent} from './input.component';

interface InputFilterInitial extends Pick<FilterInitial, 'queryParam'> {
    imask?: any;
}

export class InputFilter<T = string | number> extends Filter<T> {
    readonly _templateComponent = InputComponent;

    private readonly _imask: any;
    get imask(): any { return this._imask; }

    constructor({imask, queryParam}: InputFilterInitial) {
        super({queryParam});

        this._imask = imask;
        this.value ||= '' as any;
    }
}
