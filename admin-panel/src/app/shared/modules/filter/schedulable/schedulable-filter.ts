import {Filter} from '../models/filter';
import {FilterInitial} from '../models/filter-initial';
import {Schedulable} from '@models/schedule-template/schedulable';
import {SchedulableComponent} from './schedulable.component';

export class SchedulableFilter extends Filter<Schedulable> {
    readonly _templateComponent = SchedulableComponent;

    constructor(params: FilterInitial) {
        super(params);
    }
}
