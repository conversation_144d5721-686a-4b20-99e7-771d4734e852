import {Component, OnInit} from '@angular/core';
import {FilterComponent} from '../models/filter-component';
import {TranslateKey} from '@shared/enums/translate-key';
import {InputMasks} from '@constants/input-masks';
import {TimeFilter} from '@modules/filter/time/time-filter';

@Component({
    selector: 'app-time',
    templateUrl: './time.component.html',
    standalone: false
})
export class TimeComponent extends FilterComponent<TimeFilter> implements OnInit {
    readonly translateKeys = TranslateKey;

    readonly timeMask = InputMasks.TIME_MASK;

    selectedItem: [string, string];

    ngOnInit(): void {
        this.selectedItem = ['00:00:00', '00:00:00'];
    }
}
