<input
    class="form-control"
    type="text"
    (blur)="isFocus = false"
    (focus)="isFocus = true"
    [(ngModel)]="searchStr"
    (ngModelChange)="onChange($event)"
    [matAutocomplete]="auto"
    (close)="onSelectOption.emit()">

<mat-autocomplete
    #auto="matAutocomplete"
    (closed)="onClosed()"
    (opened)="onOpened()"
    [panelWidth]="'auto'"
    [hideSingleSelectionIndicator]="true">
    <!-- Select All Options -->
    <mat-option *ngIf="(filteredOptions.length > 1 && searchStr()) || isAllUnselected()">
        <mat-checkbox
            (click)="$event.stopPropagation()"
            (change)="onToggleSelectAll($event)"
            [(ngModel)]="isAllSelected">
            {{(isAllSelected ? translateKeys.unselectAll : translateKeys.selectAll) | translate}}
        </mat-checkbox>
    </mat-option>

    <!-- All Options -->
    <mat-option *ngFor="let option of filteredOptions" [value]="option">
        <mat-checkbox
            (change)="onToggleSelectOption($event, option)"
            (click)="$event.stopPropagation()"
            [(ngModel)]="option.checked">
            {{optionTitleFormatter(option) | translate}}
        </mat-checkbox>
    </mat-option>
</mat-autocomplete>
