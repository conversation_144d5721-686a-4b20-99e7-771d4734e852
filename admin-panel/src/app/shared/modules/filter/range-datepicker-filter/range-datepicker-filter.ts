import {Filter} from '../models/filter';
import {FilterInitial} from '../models/filter-initial';
import {RangeDatepickerFilterComponent} from './range-datepicker-filter.component';

export class RangeDatepickerFilter extends Filter<string> {
    readonly _templateComponent = RangeDatepickerFilterComponent;

    constructor(params: Pick<FilterInitial, 'queryParam' | 'defaultValue'>) {
        super(params);

        if (params.defaultValue) {
            this.value = params.defaultValue;
        }
    }
}
