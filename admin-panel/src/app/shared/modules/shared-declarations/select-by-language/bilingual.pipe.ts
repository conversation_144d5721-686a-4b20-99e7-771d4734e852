import {Pipe, PipeTransform} from '@angular/core';
import {Name} from '@shared/models/name';

@Pipe({
    name: 'bilingual',
    standalone: false
})
export class BilingualPipe implements PipeTransform {
    transform(value: Name, ignoreHtmlElement?: boolean): string {
        if (!value) { return ''; }

        return ignoreHtmlElement
            ? `${value.en} - ${value.fa}`
            : `<span dir="ltr">${value.en}</span> - <span dir="rtl">${value.fa}</span>`;
    }
}
