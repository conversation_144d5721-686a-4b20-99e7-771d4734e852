import {Component, ContentChild, ContentChildren, Input, QueryList} from '@angular/core';
import {PanelTitleComponent} from './panel-title/panel-title.component';
import {PanelContentComponent} from './panel-content/panel-content.component';
import {ActionBtnDirective} from '@modules/page-template/navigation-bar/action-btn.directive';
import {TranslateKey} from '@shared/enums/translate-key';
import {BreadcrumbDirective} from '@modules/page-template/navigation-bar/breadcrumb.directive';
import {PaginatorDirective} from '@modules/page-template/navigation-bar/paginator.directive';
import {Router} from '@angular/router';
import {FilterProperty} from '@modules/filter/models/filter-property';

@Component({
    selector: 'app-page-panel',
    templateUrl: './page-panel.component.html',
    styleUrls: ['./page-panel.component.scss'],
    standalone: false
})
export class PagePanelComponent {
    readonly translateKeys = TranslateKey;

    private _isFullscreen: boolean;
    get isFullscreen(): boolean { return this._isFullscreen; }

    get pathname(): string { return window.location.pathname; }

    get homeTitle(): string { return this._router.url.split('/')[1]; }

    get homeRoute() {
        const match = this.pathname.match('(/[a-zA-Z]+)/');
        return match ? match[1] : ''
    }

    @Input()
    filterProperty: FilterProperty;

    @ContentChild(PanelTitleComponent)
    panelTitleComponent: PanelTitleComponent;

    @ContentChild(PanelContentComponent)
    panelContent: PanelContentComponent;

    @ContentChild(PaginatorDirective)
    paginatorDirective: PaginatorDirective;

    @ContentChildren(ActionBtnDirective)
    actionBtns: QueryList<ActionBtnDirective>;

    @ContentChildren(BreadcrumbDirective)
    breadcrumbs: QueryList<BreadcrumbDirective>;

    constructor(private _router: Router) {}

    toggleFullscreen(): void {
        this._isFullscreen = !this._isFullscreen;
        setTimeout(() => this.filterProperty.refreshPage());
    }

    toggleDisplayFilter(): void {
        this.filterProperty.isShownFilter = !this.filterProperty.isShownFilter;
    }
}
