import {ComponentFixture, TestBed} from '@angular/core/testing';
import {NavigationBarComponent} from './navigation-bar.component';
import {BreadcrumbDirective} from './breadcrumb.directive';
import {ActionBtnDirective} from './action-btn.directive';
import {TranslateModule} from '@ngx-translate/core';
import {RouterTestingModule} from '@angular/router/testing';
import {
    InstrumentSearchResultsComponent
} from '../../../../feature/home/<USER>/search-box/instrument-search-results/instrument-search-results.component';

describe('NavigationBarComponent', () => {
    let component: NavigationBarComponent;
    let fixture: ComponentFixture<NavigationBarComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [
                NavigationBarComponent,
                BreadcrumbDirective,
                ActionBtnDirective,
                InstrumentSearchResultsComponent
            ],
            imports: [
                TranslateModule.forRoot(),
                RouterTestingModule
            ]
        })
            .compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(NavigationBarComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
