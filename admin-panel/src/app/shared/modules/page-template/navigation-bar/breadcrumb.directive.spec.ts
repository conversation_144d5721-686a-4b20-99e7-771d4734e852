import {BreadcrumbDirective} from './breadcrumb.directive';
import {inject, TestBed} from '@angular/core/testing';
import {TemplateRef} from '@angular/core';
import {MockTemplateRef} from '@test/mock-template-ref';

describe('BreadcrumbDirective', () => {
    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [{provide: TemplateRef, useClass: MockTemplateRef}]
        })
    });

    it('should create an instance', inject([TemplateRef], (templateRef: TemplateRef<any>) => {
        const directive = new BreadcrumbDirective(templateRef);
        expect(directive).toBeTruthy();
    }));
});
