import {AfterViewInit, Component, ContentChildren, ElementRef, QueryList, ViewChild} from '@angular/core';
import {BreadcrumbDirective} from './breadcrumb.directive';
import {ActionBtnDirective} from './action-btn.directive';
import {TranslateKey} from '@shared/enums/translate-key';
import {RoutingLayout} from '@constants/routing-layout';

@Component({
    selector: 'app-navigation-bar',
    templateUrl: './navigation-bar.component.html',
    styleUrls: ['./navigation-bar.component.scss'],
    standalone: false
})
export class NavigationBarComponent implements AfterViewInit {
    readonly translateKeys = TranslateKey;
    readonly routesLayout = RoutingLayout;

    @ContentChildren(BreadcrumbDirective) breadcrumbs: QueryList<BreadcrumbDirective>;
    @ContentChildren(ActionBtnDirective) actionBtns: QueryList<ActionBtnDirective>;
    @ViewChild('actionBtnsContainer') actionBtnsContainer: ElementRef;

    ngAfterViewInit() {
        this.actionBtnsContainer.nativeElement
            .querySelectorAll('button')
            .forEach((btn: HTMLButtonElement) => {
                btn.classList.add('btn');
                btn.classList.add('btn-light');
            })
    }
}
