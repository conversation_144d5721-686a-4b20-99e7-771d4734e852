<div class="h-auto form-control" [class.disabled]="isDisabled">

    <mat-chip-grid #chipGrid [disabled]="isDisabled">
        <mat-chip-row
            class="custom-chip"
            *ngFor="let title of selectedItemsTitle"
            (removed)="remove(title)">
            {{ title }}
            <button matChipRemove>
                <mat-icon>cancel</mat-icon>
            </button>
        </mat-chip-row>

        <input
            #inputElement
            [placeholder]="translateKeys.addNewItem | translate"
            [matChipInputFor]="chipGrid"
            [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
            [matChipInputAddOnBlur]="true"
            (matChipInputTokenEnd)="submit($event)"/>
    </mat-chip-grid>
</div>
