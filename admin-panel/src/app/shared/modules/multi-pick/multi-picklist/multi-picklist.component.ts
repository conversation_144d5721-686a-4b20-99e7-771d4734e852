import {
    ChangeDetectionStrategy,
    Component,
    ContentChild,
    ElementRef,
    forwardRef,
    Input,
    QueryList,
    ViewChild,
    ViewChildren
} from '@angular/core';
import {TranslateKey} from '@shared/enums/translate-key';
import {ControlValueAccessor, NG_VALUE_ACCESSOR} from '@angular/forms';
import {PicklistItemDirective} from './picklist-item.directive';
import {ValueAccessor} from '@shared/value-accessor';
import {MatMenuTrigger} from '@angular/material/menu';
import {MultiPicklistSearchFlag} from '@modules/multi-pick/multi-picklist/multi-picklist-search-flag';
import {FilterPipe} from '@pipes/filter.pipe';
import {findAndSplice} from '@core/utils';

@Component({
    selector: 'app-multi-picklist',
    templateUrl: './multi-picklist.component.html',
    styleUrls: ['./multi-picklist.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [{
        provide: NG_VALUE_ACCESSOR,
        useExisting: forwardRef(() => MultiPicklistComponent),
        multi: true
    }],
    standalone: false
})
export class MultiPicklistComponent extends ValueAccessor<string[]> implements ControlValueAccessor {
    readonly translateKeys = TranslateKey;

    get notSelectedList(): string[] {
        return this.list.filter(listItem => !this.selectedList.find(selectedListItem => selectedListItem === listItem));
    }

    filterListBox: string;
    filterSelectedListBox: string;
    selectedItem: string;
    selectedList: string[] = [];

    selectedSearchFlag: MultiPicklistSearchFlag;

    @Input()
    list: any[] = [];

    @Input()
    searchField = 'searchExpression';

    @Input()
    searchFlags: MultiPicklistSearchFlag[];

    @Input()
    availableOptionsTitle: string = TranslateKey.availableOptions;

    @Input()
    selectedOptionsTitle: string = TranslateKey.selectedOptions;

    @ContentChild(PicklistItemDirective)
    picklistItem: PicklistItemDirective;

    get searchFlag(): string {
        return this.selectedSearchFlag
            ? this.selectedSearchFlag.searchField
            : this.searchField;
    }

    @ViewChildren(MatMenuTrigger)
    private _selectedMenus: QueryList<MatMenuTrigger>;

    @ViewChild('searchInput')
    private _searchInput: ElementRef<HTMLInputElement>;

    filterListBoxFilter(): void {
        if (this.searchFlags && this.filterListBox.search('/') === 0) {
            this._selectedMenus.get(0).openMenu();
        }
    }

    filterSelectedListBoxFilter(): void {
        if (this.searchFlags && this.filterSelectedListBox.search('/') === 0) {
            this._selectedMenus.get(1).openMenu();
        }
    }

    setFilterFlag(event: MouseEvent, flag: MultiPicklistSearchFlag): void {
        this.selectedSearchFlag = flag;
        this.filterListBox = '';
        this._searchInput.nativeElement.focus();
        event.stopPropagation();
        this._selectedMenus.get(0).closeMenu();
        this._selectedMenus.get(1).closeMenu();
    }

    selectItem(item: string): void {
        this.selectedItem = item;
    }

    isSelectedItem(item: string): boolean {
        return this.selectedItem === item;
    }

    addSelectedItem(): void {
        this.selectedList.push(this.selectedItem);
        findAndSplice(this.list, item => item === this.selectedItem);
        this.onChange(this.selectedList);
    }

    selectedAllItems(): void {
        const filteredItems = new FilterPipe().transform(this.notSelectedList, this.filterListBox, this.searchFlag || '');
        filteredItems.forEach(filteredItem => {
            this.selectedList.push(filteredItem);
            findAndSplice(this.list, item => item === filteredItem)
        });
        this.onChange(this.selectedList);
    }

    removeAllSelectedItems(): void {
        const filteredItems = new FilterPipe().transform(this.selectedList, this.filterSelectedListBox, this.searchFlag || '').slice(0)
        filteredItems.forEach(filteredItem => {
            this.list.push(filteredItem);
            findAndSplice(this.selectedList, item => item === filteredItem)
        });
        this.onChange(this.selectedList);
    }

    removeSelectedItem(selectedItem: string): void {
        findAndSplice(this.selectedList, item => item === selectedItem);
        this.list.push(selectedItem);
        this.onChange(this.selectedList);
    }

    isSelectedItemInSelectedList(): boolean {
        return !!this.selectedList.find(item => item === this.selectedItem);
    }

    override writeValue(selectedList: string[]): void {
        this.selectedList = selectedList || [];
        this.onChange(selectedList);
    }
}
