import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    Input,
    QueryList,
    ViewChildren
} from '@angular/core';
import {FormField, FormFields, FormFieldsGroup, FormFieldsRawValue} from './form-field';
import {Form<PERSON>rray, FormBuilder, FormGroup} from '@angular/forms';
import {FormFieldSet} from './form-field-sets';
import {ChangeDetectionService} from '@services/change-detection.service';
import {ReadOnlyFormField} from './form-fields/read-only-field/read-only-form-field';

@Component({
    selector: 'app-dynamic-form',
    templateUrl: './dynamic-form.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class DynamicFormComponent implements AfterViewInit {
    private _afterViewInit: boolean;
    get afterViewInit(): boolean { return this._afterViewInit; }

    @Input() formFields: FormFields;

    @Input() formGroup: FormGroup;

    @Input() formFieldSets: FormFieldSet[];

    @ViewChildren('fieldSetElm')
    private _fieldSetElms: QueryList<ElementRef<HTMLDivElement>>;

    private _fieldSets: { [fieldSetId: string]: HTMLDivElement } = {};

    constructor(
        private _formBuilder: FormBuilder,
        private _changeDetectorRef: ChangeDetectorRef
    ) {}

    ngAfterViewInit(): void {
        // The following line is needed when the "formFieldset" is changed.
        this._changeDetectorRef.detectChanges();

        this._setupFieldSetElms();
        this._setupFormGroup();

        this._afterViewInit = true;
        this._changeDetectorRef.detectChanges();
        ChangeDetectionService.onChange.next({type: 'FORM'});
    }


    reset(formFields?: FormFields): void {
        formFields ||= this.formFields;

        formFields.forEach(field => {
            if (field instanceof FormField) {
                field.formControl.setValue(field.defaultValue);
                field.formControl.markAsPristine();
                field.formControl.markAsUntouched();
            } else if (field.formGroupName) {
                this.reset(field.formFields as FormFields);
            } else if (field.formArrayName) {
                field.formFields.forEach(item => this.reset(item as FormFields));
            }
        });
    }

    private _setupFormGroup(): void {
        this._setupFormControls(this.formFields, this.formGroup, this.formFields.rawValue ||= {});
    }

    private _setupFormControls(formFields: FormFields, formGroup: FormGroup, rawValue: any): void {
        formFields.forEach(field => {
            if (field instanceof FormField) {
                this._initializeFormField(field, formGroup, rawValue);
            } else if (field.formArrayName) {
                this._initializeFormArray(field, formGroup, rawValue);
            } else {
                this._initializeFormGroup(field, formGroup, rawValue);
            }
        });
    }

    private _initializeFormField(field: FormField, formGroup: FormGroup, rawValue: FormFieldsRawValue<any>): void {
        if (field.formControl) {
            return;
        }

        field.fieldSetElm = this._fieldSets[field.fieldSetId];
        field.formControl = this._formBuilder.control({
            value: field.value,
            disabled: field.disable
        }, field.validations);

        formGroup.addControl(field.formControlName, field.formControl);
        field.defaultValue = field?.value;
        rawValue[field.formControlName] = field;
    }

    private _initializeFormArray(field: FormFieldsGroup, formGroup: FormGroup, rawValue: any): void {
        field.formFields.forEach((arrayFormGroup) => {
            if (arrayFormGroup[0].formControl) {
                return;
            }

            const formControlRawValue = {};

            const formControl: FormArray = formGroup.controls[field.formArrayName] as FormArray || this._formBuilder.array([]);
            if (!formGroup.controls[field.formArrayName]) {
                formGroup.addControl(field.formArrayName, formControl);
            }

            const formControlGroup = this._formBuilder.group({});
            formControl.push(formControlGroup);


            this._setupFormControls(arrayFormGroup, formControlGroup, formControlRawValue);

            rawValue[field.formArrayName] ||= [];
            rawValue[field.formArrayName].push(formControlRawValue);

            formControlRawValue['remove'] = () => {
                const index = rawValue[field.formArrayName].findIndex(item => item === formControlRawValue);
                rawValue[field.formArrayName].splice(index, 1);
                field.formFields.splice(index, 1);
                (formGroup.controls[field.formArrayName] as FormArray<any>).removeAt(index);

                const controlCount = this._getArrayFormGroupLength(arrayFormGroup);

                for (let i = 0; i < controlCount; i++) {
                    const elm = (arrayFormGroup[0].fieldSetElm as HTMLDivElement);
                    elm.removeChild(elm.children[index * controlCount]);
                }

                this.ngAfterViewInit();
            };

        });
    }

    private _initializeFormGroup(field: FormFieldsGroup, formGroup: FormGroup, rawValue: FormFieldsRawValue<any>): void {
        const formControl = this._formBuilder.group({});
        formGroup.addControl(field.formGroupName, formControl);

        rawValue[field.formGroupName] = {};
        this._setupFormControls(field.formFields as FormFields, formControl, rawValue[field.formGroupName]);
    }

    private _setupFieldSetElms(): void {
        this._fieldSetElms.forEach(fieldSet => {
            this._fieldSets[fieldSet.nativeElement.id] = fieldSet.nativeElement;
        });
    }

    private _getArrayFormGroupLength(formFields: FormFields | FormFields[]): number {
        let controlCount = 0;

        formFields.forEach(field => {
            if (field instanceof ReadOnlyFormField) { return; }

            controlCount += field instanceof FormField
                ? 1
                : this._getArrayFormGroupLength((field as FormFieldsGroup).formFields || field);
        });

        return controlCount;
    }

}
