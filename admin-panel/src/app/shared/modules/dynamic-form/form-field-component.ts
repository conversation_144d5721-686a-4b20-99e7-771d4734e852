import {Directive} from '@angular/core';
import {ComponentOutletInjector} from '../../component-outlet-injector';

@Directive()
export abstract class FormFieldComponent<T> {
    protected readonly _templateData: T;

    constructor(componentOutletInjector: ComponentOutletInjector<T>) {
        this._templateData = componentOutletInjector.data;
    }

    get templateData(): T {
        return this._templateData;
    }
}
