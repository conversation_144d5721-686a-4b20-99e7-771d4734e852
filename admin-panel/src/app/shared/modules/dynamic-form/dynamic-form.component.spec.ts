import {DynamicFormComponent} from './dynamic-form.component';
import {ComponentHarness} from '@test/harness/component-harness';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {FormFieldContainerComponent} from './form-field-container/form-field-container.component';
import {InputMasks} from '@constants/input-masks';
import {FormControlsComponent} from './form-controls/form-controls.component';
import {FormField} from './form-field';
import {InputFormFieldComponent} from '@modules/dynamic-form/form-fields/input-field/input-form-field.component';
import {TranslateTestingModule} from '../../../../translate-testing.module';
import {SharedModule} from '../../shared.module';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';

describe('DynamicFormComponent', () => {

    let ha: ComponentHarness<DynamicFormComponent>;

    beforeEach(async () => {
        ha = new ComponentHarness(DynamicFormComponent, {
            declarations: [
                DynamicFormComponent,
                FormFieldContainerComponent,
                FormControlsComponent,
                InputFormFieldComponent
            ],
            imports: [
                TranslateTestingModule,
                SharedModule
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
            detectChanges: false
        });

        ha.component.formFields = [
            new InputFormField({
                label: 'price',
                width: 4,
                validations: [Validators.required, Validators.min(2)],
                formControlName: 'numberInput',
                mask: InputMasks.COMMA_SEPERATED_NUMBER,
                hint: 'hint'
            })
        ];

        ha.component.formGroup = new FormGroup({});
    });

    it('should create', () => {
        ha.detectChanges();
        expect(ha.component).toBeTruthy();
    });

    it('should initialize the form group When component has initialized', () => {
        ha.detectChanges();

        expect(ha.component.formGroup).toBeDefined();
        expect(ha.component.formGroup.controls.numberInput instanceof FormControl).toBeTrue();
    });

    xit('should show error message when required validation is false', async () => {
        await ha.detectChanges();

        expect(ha.get('.invalid-feedback')).not.toHaveStyle('display', 'block');

        ha.get('input').nativeElement.focus()
        ha.component.formGroup.get('numberInput').markAsDirty();
        ha.component.formGroup.markAsTouched();
        await ha.detectChanges();
        expect(ha.get('.invalid-feedback')).toHaveStyle('display', 'block');
    });

    xit('should show error message when min validation is false', () => {
        ha.detectChanges();

        ha.component.formGroup.get('numberInput').setValue(1);
        ha.detectChanges();
        expect(ha.get('.invalid-feedback')).toHaveStyle('display', 'block');
        expect(ha.get('.invalid-feedback')).toHaveContained('Price Should Be At Least 2');

        ha.component.formGroup.get('numberInput').setValue(2);
        ha.detectChanges();
        expect(ha.get('.invalid-feedback')).not.toHaveStyle('display', 'block');
    });

    it('should form group inputs be append to element with form-row class', () => {
        ha.detectChanges();

        expect(ha.get('form > div.form-row > div.form-group')).toBeTruthy();
    });

    it('should initialize and assign field sets correctly', () => {
        const  formFiled = ha.component.formFields[0] as FormField;
        const priceStr = 'price';
        ha.component.formFieldSets = [{title: priceStr, id: priceStr}];
        formFiled.fieldSetId = priceStr;
        ha.detectChanges();

        expect(formFiled.fieldSetElm).toBeTruthy();
        expect(ha.get('form > fieldset > div.form-row > div.form-group')).toBeTruthy();
    });

    it('should check form group name', () => {
        ha.component.formFields = [
            {
                formGroupName: 'priceInput',
                formFields: [new InputFormField({
                    label: 'price',
                    width: 4,
                    validations: [Validators.required, Validators.min(2)],
                    formControlName: 'numberInput',
                    mask: InputMasks.COMMA_SEPERATED_NUMBER
                })]
            }
        ];
        ha.detectChanges();
        const nestedFormGroup = ha.component.formGroup.controls['priceInput'];

        expect(nestedFormGroup instanceof  FormGroup).toBeTruthy();
        expect(nestedFormGroup.get('numberInput')).toBeExists();
    });

    it('should check form group hint', () => {
        ha.detectChanges();

        expect(ha.get('.form-text.text-muted')).toBeExists();
    });
});
