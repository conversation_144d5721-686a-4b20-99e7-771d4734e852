<input
    class="form-control"
    type="text"
    [ngModel]="searchExp()"
    (ngModelChange)="onChange($event)"
    (focus)="onFocus()"
    [matAutocomplete]="auto"
    [disabled]="templateData.formControl?.disabled"
    [placeholder]="templateData.placeholder ? (translateKeys.example | translate:{example: templateData.placeholder | translate}) : ''">

<mat-autocomplete
    #auto="matAutocomplete"
    (optionSelected)="onSelectData($event)"
    [panelWidth]="templateData.panelWidth"
    [displayWith]="displayFn">
    <mat-option *ngFor="let option of filteredOptions" [value]="option">
        {{ templateData.getTitle(option) }}
    </mat-option>
</mat-autocomplete>
