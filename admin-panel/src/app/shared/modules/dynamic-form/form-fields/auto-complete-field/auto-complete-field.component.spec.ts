import {AutoCompleteFieldComponent} from './auto-complete-field.component';
import {ComponentHarness} from '@test/harness/component-harness';
import {AutoCompleteFormField} from './auto-complete-form-field';
import {FormControl, Validators} from '@angular/forms';
import {TestUtils} from '@test/test-utils';
import {MatAutocompleteSelectedEvent} from '@angular/material/autocomplete';
import {componentOutletInjectorHarness} from '../../../../component-outlet-injector.harness';
import {TranslateTestingModule} from '../../../../../../translate-testing.module';
import {SharedModule} from '../../../../shared.module';
import {ComponentOutletInjector} from '../../../../component-outlet-injector';
import {Broker} from '@homeModels/broker';

describe('AutoCompleteFieldComponent', () => {
    let ha: ComponentHarness<AutoCompleteFieldComponent>;

    beforeEach(() => {
        const brokers = TestUtils.getBrokers().content;
        componentOutletInjectorHarness.data = new AutoCompleteFormField({
            label: 'broker',
            width: 4,
            validations: [Validators.required],
            formControlName: 'brokerInput',
            searchExpField: 'searchExpression',
            value: brokers[0],
            options: brokers,
            getTitle(option: Broker.Simple): string { return option.id + ' - ' + option.name?.fa; }
        });
        spyOn(componentOutletInjectorHarness.data, 'onChange');

        ha = new ComponentHarness(AutoCompleteFieldComponent, {
            declarations: [AutoCompleteFieldComponent],
            imports: [SharedModule, TranslateTestingModule],
            providers: [
                {provide: ComponentOutletInjector, useValue: componentOutletInjectorHarness}
            ]
        });
    });

    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });

    it('should call #templateData.onChange method when an option is selected', () => {
        ha.component.onSelectData({option: {value: new Broker.Simple()}} as MatAutocompleteSelectedEvent);

        expect(componentOutletInjectorHarness.data.onChange).toHaveBeenCalled();
    });

    it('should filter options by given searchExpField', () => {
        ha.component.templateData.options.forEach(option => option.searchExpression = option.id + ' - ' + option.name.fa);

        const searchStr = '11';
        ha.component.onChange(searchStr);

        const filteredOption = ha.component.filteredOptions[0];
        expect(filteredOption[ha.component.templateData.searchExpField]).toContain(searchStr);
    });

    it('should searchExp attribute has value when component has initialized', () => {
        const searchExp = ha.component.searchExp();
        const templateDataSearchExp = ha.component.templateData.getTitle(ha.component.templateData.value);

        expect(searchExp).toEqual(templateDataSearchExp);
    });

    it('should disable input when templateData has disabled value', async () => {
        await ha.detectChanges();
        ha.component.templateData.formControl = new FormControl();
        ha.component.templateData.formControl.disable();
        await ha.detectChanges();

        expect(ha.get('input')).toHaveAttribute('disabled');
    });

    it('should have options when initialized', () => {
        ha.detectChanges();

        expect(ha.component.filteredOptions.length).toBeTruthy();
    });

    it('should pass the right value to #_templateData.onChange', () => {
        const groups = TestUtils.getGroups().content;
        const group = groups[0];
        componentOutletInjectorHarness.data.options = groups;
        componentOutletInjectorHarness.data.optionFieldName = 'code';
        ha.component.onSelectData({option: {value: group}} as MatAutocompleteSelectedEvent);
        ha.detectChanges();

        expect(componentOutletInjectorHarness.data.onChange).toHaveBeenCalledWith(group.code);
    });

});
