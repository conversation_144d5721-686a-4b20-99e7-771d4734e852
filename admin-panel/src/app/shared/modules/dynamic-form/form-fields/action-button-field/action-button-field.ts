import {ActionButtonFieldComponent} from './action-button-field.component';
import {FormField} from '../../form-field';

interface CustomFormField extends Pick<FormField, 'fieldSetId' | 'disable' | 'width' | 'style'> {
    title: string;
    onClick: () => void;
}

export class ActionButtonField extends FormField {
    templateComponent = ActionButtonFieldComponent;

    title: string;

    onClick: () => void;

    constructor(formField: CustomFormField) {
        super(formField as any);

        this.title = formField.title;
        this.onClick = formField.onClick;
        this.width = formField.width || 12;
    }
}
