import {FormField} from '../../form-field';

interface CustomFormField extends Pick<FormField, 'label' | 'fieldSetId' | 'width' | 'style' | 'required'> {}

export class LabelFormField extends FormField {
    templateComponent = null;

    constructor(formField: CustomFormField) {
        formField.style ||= {
            lineHeight: '40px',
            marginBottom: 'unset'
        };
        super(formField as any);

        this.required = formField.required;
    }
}
