import {MultiTextFieldComponent} from './multi-text-field.component';
import {ComponentHarness} from "@test/harness/component-harness";
import {SharedModule} from "../../../../shared.module";
import {TranslateTestingModule} from "../../../../../../translate-testing.module";
import {ComponentOutletInjector} from "../../../../component-outlet-injector";
import {componentOutletInjectorHarness} from "../../../../component-outlet-injector.harness";

describe('MultiTextFieldComponent', () => {
    let ha: ComponentHarness<MultiTextFieldComponent>;
    beforeEach(() => {
        ha = new ComponentHarness(MultiTextFieldComponent, {
            declarations: [
                MultiTextFieldComponent
            ],
            imports: [
                SharedModule,
                TranslateTestingModule
            ],
            providers: [
                {provide: ComponentOutletInjector, useValue: componentOutletInjectorHarness}
            ],
            detectChanges: false
        });
    });


    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });
});
