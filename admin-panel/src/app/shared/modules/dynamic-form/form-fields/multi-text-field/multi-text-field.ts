import {<PERSON><PERSON>ield, FormFieldPrivateAttrs} from '../../form-field';
import {MultiTextFieldComponent} from './multi-text-field.component';

interface CustomFormField extends Omit<FormField, FormFieldPrivateAttrs> {}

export class MultiTextField extends FormField {
    templateComponent = MultiTextFieldComponent;

    optionFieldName: string;

    constructor(formField: CustomFormField) {
        super(formField as any);

        this.value = formField.value;
    }
}
