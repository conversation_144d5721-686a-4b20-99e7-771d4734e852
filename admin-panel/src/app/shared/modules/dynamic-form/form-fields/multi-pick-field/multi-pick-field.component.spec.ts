import {MultiPickFieldComponent} from './multi-pick-field.component';
import {ComponentHarness} from '@test/harness/component-harness';
import {componentOutletInjectorHarness} from '../../../../component-outlet-injector.harness';
import {SharedModule} from '../../../../shared.module';
import {TranslateTestingModule} from '../../../../../../translate-testing.module';
import {ComponentOutletInjector} from '../../../../component-outlet-injector';
import {FormControl} from '@angular/forms';
import {MultiPickField} from './multi-pick-field';

describe('MultiPickFieldComponent', () => {
    let ha: ComponentHarness<MultiPickFieldComponent>;
    const mockList = [
        {id: 1, name: 'Item 1'},
        {id: 2, name: 'Item 2'},
        {id: 3, name: 'Item 3'}
    ];

    const mockFormControl = new FormControl();

    const setupTemplateData = () => {
        const templateData = new MultiPickField<typeof mockList[0]>({
            list: mockList,
            searchExpField: 'name',
            optionFieldName: 'id',
            getTitle: (item) => item.name,
            formControlName: 'testField',
        });

        templateData.formControl = mockFormControl;

        componentOutletInjectorHarness.data = templateData;
        return templateData;
    };

    beforeEach(async () => {
        ha = new ComponentHarness(MultiPickFieldComponent, {
            declarations: [
                MultiPickFieldComponent
            ],
            imports: [
                SharedModule,
                TranslateTestingModule
            ],
            providers: [
                {provide: ComponentOutletInjector, useValue: componentOutletInjectorHarness}
            ],
            detectChanges: false
        });

        const templateData = setupTemplateData();
        spyOnProperty(ha.component, 'templateData', 'get').and.returnValue(templateData);
    });

    it('should create', () => {
        ha.detectChanges();
        expect(ha.component).toBeTruthy();
    });

    describe('clonedList functionality', () => {
        it('should initialize clonedList with a copy of the template data list', async () => {
            // Act
            await ha.detectChanges();

            // Assert
            expect(ha.component.clonedList).toEqual(mockList);
            expect(ha.component.clonedList).not.toBe(mockList); // Should be a different instance
        });

        it('should reset clonedList when form control value is set to null', async () => {
            // Arrange
            await ha.detectChanges();

            // Modify the clonedList to verify it gets reset
            ha.component.clonedList = [{id: 1, name: 'Item 1'}]; // Only one item

            // Act
            mockFormControl.setValue(null);
            // await ha.detectChanges();

            // Assert
            expect(ha.component.clonedList).toEqual(mockList);
            expect(ha.component.pickedItems).toEqual([]);
        });

        it('should update clonedList when onPickItem is called', async () => {
            // Arrange
            await ha.detectChanges();

            // Initially all items should be in the clonedList
            expect(ha.component.clonedList.length).toBe(3);

            // Act - pick an item
            ha.component.pickedItems = [{id: 2, name: 'Item 2'}];
            ha.component.onPickItem();
            await ha.detectChanges();

            // Assert
            // The setValue method should have been called with the picked item's ID
            expect(ha.component.templateData.value).toEqual([2]);
        });
    });
});
