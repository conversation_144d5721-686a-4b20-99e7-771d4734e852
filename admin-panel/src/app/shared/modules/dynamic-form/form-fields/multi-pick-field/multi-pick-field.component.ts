import {Component, OnInit} from '@angular/core';
import {MultiPickField} from './multi-pick-field';
import {TranslateKey} from '@shared/enums/translate-key';
import {FormFieldComponent} from '../../form-field-component';
import {filter} from 'rxjs/operators';

@Component({
    selector: 'app-input-form-field',
    templateUrl: './multi-pick-field.component.html',
    standalone: false
})
export class MultiPickFieldComponent extends FormFieldComponent<MultiPickField<any>> implements OnInit {
    readonly translateKeys = TranslateKey;

    pickedItems: any[] = [];

    clonedList: any[];

    onPickItem(): void {
        const value = this.templateData.optionFieldName
            ? this.pickedItems.map((item) => item[this.templateData.optionFieldName])
            : this.pickedItems;
        this.templateData.setValue(value)
    }

    ngOnInit() {
        this._cloneList();
        this._excludePickedFromList();
        this._subscribeToFormControlChanges();
    }

    private _cloneList(): void {
        this.clonedList = this.templateData?.list?.slice(0);
    }

    private _subscribeToFormControlChanges(): void {
        this.templateData.formControl.valueChanges
            .pipe(filter(value => !value))
            .subscribe(() => {
                this._resetPickedItemsAndClonedList()
            });
    }

    private _resetPickedItemsAndClonedList(): void {
        this.pickedItems = [];
        this._cloneList();
    }

    private _excludePickedFromList(): void {
        if (this.templateData.value) {
            this.pickedItems = this.templateData.value.map(item => {
                return typeof item !== 'object'
                    ? {[this.templateData.optionFieldName]: item}
                    : item;
            });
            this.onPickItem();

            this.clonedList = this.clonedList.filter(listItem =>
                !this.pickedItems.find(valueItem =>
                    listItem[this.templateData.optionFieldName] === valueItem[this.templateData.optionFieldName]));
        }
    }
}
