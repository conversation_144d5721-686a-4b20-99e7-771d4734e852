<div [isDisabled]="templateData.disable || templateData.formControl.disabled" dropdown>
    <div class="form-control h-auto overflow-auto multi-pick-field-input"
         dropdownToggle>
        @if (pickedItems.length) {
         <span class="badge badge-light m-1 font-weight-light"
               style="font-size: 90%"
               *ngFor="let item of pickedItems">
             {{ templateData.getTitle(item) }}
         </span>
        } @else {
            <span class="text-muted" style="opacity: 0.6">
                {{translateKeys.example | translate:{example: templateData.placeholder || translateKeys.example} }}
            </span>
        }
    </div>

    <div class="dropdown-menu p-2 mx-1" *dropdownMenu>
            <app-multi-picklist
            [(ngModel)]="pickedItems"
            (ngModelChange)="onPickItem()"
            [list]="clonedList"
            [searchField]="templateData.searchExpField"
            [searchFlags]="templateData.searchFlags">
            <ng-container *picklistItem="let item">
                {{ templateData.getTitle(item) }}
            </ng-container>
        </app-multi-picklist>
    </div>
</div>
