import {TextAreaFieldComponent} from './text-area-field.component';
import {Form<PERSON>ield, FormFieldPrivateAttrs} from '../../form-field';

interface CustomFormField extends Omit<FormField, FormFieldPrivateAttrs> {}

export class TextAreaFormField extends FormField {
    templateComponent = TextAreaFieldComponent;

    constructor(formField: CustomFormField) {
        super(formField as any);
    }
}
