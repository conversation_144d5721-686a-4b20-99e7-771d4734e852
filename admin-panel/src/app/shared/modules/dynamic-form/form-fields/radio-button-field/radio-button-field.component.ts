import {Component, OnInit} from '@angular/core';
import {RadioButtonFormField} from './radio-button-form-field';
import {FormFieldComponent} from '../../form-field-component';

@Component({
    selector: 'app-radio-button-field',
    templateUrl: './radio-button-field.component.html',
    standalone: false
})
export class RadioButtonFieldComponent extends FormFieldComponent<RadioButtonFormField<any>> implements OnInit {

    ngOnInit() {
        const optionFieldName: string = this._templateData.optionFieldName;
        if (this._templateData.value && optionFieldName) {
            const outputValue = this._templateData.value[optionFieldName];
            this._templateData.onChange(outputValue);
        }
    }

    onSelect(event): void {
        const outputValue = this._templateData.optionFieldName
            ? event[this._templateData.optionFieldName]
            : event;
        this._templateData.onChange(outputValue);
    }
}
