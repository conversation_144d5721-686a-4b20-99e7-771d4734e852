import {ChangeDetectorRef, Component, OnInit} from '@angular/core';
import {ButtonToggleFormField} from './button-toggle-form-field';
import {FormFieldComponent} from '../../form-field-component';
import {ComponentOutletInjector} from '../../../../component-outlet-injector';

@Component({
    selector: 'app-button-toggle-field',
    templateUrl: './button-toggle-field.component.html',
    standalone: false
})
export class ButtonToggleFieldComponent extends FormFieldComponent<ButtonToggleFormField> implements OnInit {

    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        componentOutletInjector: ComponentOutletInjector<ButtonToggleFormField>) {
        super(componentOutletInjector);
    }

    ngOnInit() {
        this._templateData.formControl?.valueChanges.subscribe((value) => {
            this._templateData.value = value;
            this._changeDetectorRef.detectChanges();
        });
    }
}
