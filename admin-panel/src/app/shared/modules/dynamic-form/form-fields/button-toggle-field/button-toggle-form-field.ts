import {ButtonToggleFieldComponent} from './button-toggle-field.component';
import {FormField, FormFieldPrivateAttrs} from '../../form-field';

interface CustomFormField extends Omit<FormField, FormFieldPrivateAttrs> {
    options: string[];
}

export class ButtonToggleFormField extends FormField {
    templateComponent = ButtonToggleFieldComponent;
    options: string[];

    constructor(formField: CustomFormField) {
        super(formField as any);

        this.options = formField.options;
    }
}
