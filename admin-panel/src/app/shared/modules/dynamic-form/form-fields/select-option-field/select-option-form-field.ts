import {SelectOptionFormFieldComponent} from './select-option-form-field.component';
import {FormField, FormFieldPrivateAttrs} from '../../form-field';
import {signal} from '@angular/core';

interface CustomFormField<T> extends Omit<FormField<T>, 'mask' | FormFieldPrivateAttrs> {
    options: T[];
    optionFieldName?: string;
    getTitle?(option: T): string
}

export class SelectOptionFormField<T> extends FormField<T> {
    templateComponent = SelectOptionFormFieldComponent;
    options = signal<T[]>([]);
    optionFieldName: string;
    getTitle: (option: any) => string;

    constructor(formField: CustomFormField<T>) {
        super(formField as any);

        this.options.set(formField.options);
        this.optionFieldName = formField.optionFieldName;
        this.getTitle = formField.getTitle;

        this.value = (this.value !== undefined && formField.optionFieldName)
            ? formField.options.find(item => item[formField.optionFieldName] === this.value)
            : this.value;
    }
}
