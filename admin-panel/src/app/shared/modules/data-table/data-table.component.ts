import {
    ChangeDetectionStrategy,
    Component,
    ContentChild,
    ContentChildren,
    Input,
    QueryList,
    TemplateRef
} from '@angular/core';
import {TranslateKey} from '@shared/enums/translate-key';
import {Filter} from '@modules/filter/models/filter';
import {RowActionBtnDirective} from './row-action-btn.directive';
import {RowPreActionBtnDirective} from './row-pre-action-btn.directive';
import {DataTableColumn, DataTableColumnClass} from '@models/data-table';
import {CustomColumnDirective} from './custom-column.directive';
import {FilterProperty} from '@modules/filter/models/filter-property';
import {animate, state, style, transition, trigger} from '@angular/animations';
import {ExpansionRowDirective} from './expansion-row.directive';

@Component({
    selector: 'app-data-table',
    templateUrl: './data-table.component.html',
    styleUrl: './data-table.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    animations: [
        trigger('detailExpand', [
            state('collapsed', style({height: '0px', minHeight: '0'})),
            state('expanded', style({height: '*'})),
            transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
        ]),
    ],
    standalone: false
})
export class DataTableComponent {
    protected readonly translateKeys = TranslateKey;

    @Input()
    dataSource: any[];

    @Input()
    dataTableColumns: string[];

    @Input()
    noDataRowText: TranslateKey;

    @Input()
    filterProperty: FilterProperty;

    @Input()
    isPageDataLoaded: boolean;

    @ContentChild(RowActionBtnDirective)
    readonly rowActionBtns: RowActionBtnDirective;

    @ContentChild(RowPreActionBtnDirective)
    readonly rowPreActionBtns: RowPreActionBtnDirective;

    @ContentChild(ExpansionRowDirective)
    readonly expansionRowDirective: ExpansionRowDirective;

    @ContentChildren(CustomColumnDirective)
    readonly customColumnDirectives: QueryList<CustomColumnDirective>;

    expandedRow: any | null;

    @Input()
    columns: DataTableColumn<any>[] = [];

    onFilterChange(event: Filter<any>): void {
        if (this.filterProperty.filterParams) {
            this.filterProperty.filterParams[event.queryParam] = event.queryParamValueField && event.value
                ? event.value[event.queryParamValueField]
                : event.value;
        }
        this.filterProperty.callback();
    }

    getHeaderCellDef(title: string): TemplateRef<any> {
        return this.customColumnDirectives.find(column => column.name === title).customHeaderDefDirective?.templateRef;
    }

    getCellDef(title: string): TemplateRef<any> {
        return this.customColumnDirectives.find(column => column.name === title).customCellDefDirective?.templateRef;
    }

    getClass(columnClass: DataTableColumnClass<any>, data: any): string {
        if (!columnClass) { return null }

        switch (typeof columnClass) {
            case 'string':
                return columnClass;

            case 'function':
                return columnClass(data);

            case 'object':
                return columnClass.map(item => typeof item === 'string' ? item : item(data) ).join(' ');

            default:
                return '';
        }
    }

    toggleSelectRow(row: any): void {
        this.expandedRow = this.expandedRow === row ? null : row;
    }
}
