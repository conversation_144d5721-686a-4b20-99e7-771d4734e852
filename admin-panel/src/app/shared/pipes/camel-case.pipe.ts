import {Pipe, PipeTransform} from '@angular/core';

@Pipe({
    name: 'camelCase',
    standalone: false
})
export class CamelCasePipe implements PipeTransform {

    transform(str: string): string {
        if (!/[a-z]/.test(str)) {
            str = str.split('_').map(item => item[0].toUpperCase() + item.slice(1).toLowerCase()).join('');
        }
        str = str ? str[0].toLowerCase() + str.slice(1) : '';

        return str;
    }

}
