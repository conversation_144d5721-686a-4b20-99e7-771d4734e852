import {Pipe, PipeTransform} from '@angular/core';

type NumberType = null | number | string | undefined;

@Pipe({
    name: 'bigNumber',
    standalone: false
})
export class BigNumberPipe implements PipeTransform {

    transform(value: NumberType, skipPrefix?: boolean): string {
        try {
            return this._tryToTransform(value, skipPrefix);
        } catch (e) {
            return '---';
        }
    }

    private _tryToTransform(value: NumberType, skipPrefix?: boolean): string {
        this._checkIsValid(value);

        let prefix = '';
        const locale = ',2BM';

        value = Number(value);

        if (
            !skipPrefix
            && locale.includes('M')
            && value >= Math.pow(10, 6)
            && (locale.includes('B') ? value <= Math.pow(10, 9) : true)
        ) {
            value = +value / Math.pow(10, 6);
            prefix += 'M';
        } else if (
            !skipPrefix
            && locale.includes('B')
            && value >= Math.pow(10, 9)
        ) {
            value = +value / Math.pow(10, 9);
            prefix += 'B';
        }

        const match = locale.match(/(\.)([0-9]+)/);
        const decimalPlaces = match ? +match[2] : null;
        if (decimalPlaces) {
            const newValue = (+value).toFixed(decimalPlaces);
            const decimal = newValue.split('.')[1];

            if (+decimal) {
                (value as any) = newValue;
            }
        }

        if (locale.includes(',')) {
            value = this._numberWithCommas(value);
        }

        return value + prefix;
    }

    private _checkIsValid(value: NumberType) {
        if (value === null || value === undefined || value === 'invalid') {
            throw Error('Invalid Value');
        }
    }

    private _numberWithCommas(x): string {
        const parts = x.toString().split('.');
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        return parts.join('.');
    }
}
