import {TitleValuePipe} from './title-value.pipe';

describe('TitleValuePipe', () => {
    let pipe: TitleValuePipe;

    beforeEach(() => {
        pipe = new TitleValuePipe();
    });

    it('should create an instance', () => {
        expect(pipe).toBeTruthy();
    });

    it('should return null when value is null', () => {
        expect(pipe.transform(null)).toBeNull();
    });

    it('should return null when value is empty string', () => {
        expect(pipe.transform('')).toBeNull();
    });

    it('should return the value when it is not null or empty string', () => {
        expect(pipe.transform('test')).toBe('test');
        expect(pipe.transform(123)).toBe(123);
        expect(pipe.transform(false)).toBe(false);
        expect(pipe.transform(0)).toBe(0);
        expect(pipe.transform({})).toEqual({});
    });
});
