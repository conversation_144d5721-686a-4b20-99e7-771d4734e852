import {Injectable} from '@angular/core';
import {BehaviorSubject, Subject} from 'rxjs';
import {Group} from '@models/group';
import {AdminCommandCallback} from '@models/admin-command-callback';
import {SystemState} from '@models/system-state';
import {Trader} from '@models/trader';
import {Origin} from '@homeModels/origin';
import {Instrument} from '@homeModels/instrument';
import {Broker} from '@homeModels/broker';

@Injectable({
    providedIn: 'root'
})
export class StoreService {
    static readonly systemState = new BehaviorSubject<SystemState.State>(null);
    static currentSystemState: SystemState.State;

    static readonly adminCommandCallBacks: AdminCommandCallback[] = [];

    static readonly marketStartChange = new Subject<boolean>();
    static isMarketStarted: boolean;

    static instruments: Instrument.Simple[] = [];
    static instrumentsObj: { [instrumentId: string]: Instrument.Simple } = {};

    static groups: Group[] = [];
    static groupsObj: { [group: string]: Group } = {};

    static brokers: Broker.Simple[] = [];
    static brokersObj: { [brokerId: string]: Broker.Simple } = {};

    static traders: Trader[] = [];
    static tradersObj: { [traderId: string]: Trader } = {};

    static origins: Origin[] = [];

    private static _systemDateTime: Date = new Date();
    static get systemDateTime(): Date { return new Date(this._systemDateTime); }

    static set systemDateTime(date: Date) { this._systemDateTime = date; }
}
