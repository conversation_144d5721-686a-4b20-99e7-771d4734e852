import {inject, Injectable} from '@angular/core';
import {TranslateService} from '@ngx-translate/core';
import {UtilConstants} from '@constants/util-constants';
import {Language} from '@constants/language';
import {LanguageLayout} from '@shared/enums/language-layout';
import {Name} from '@shared/models/name';

@Injectable()
export class LangService {
    private static _translateService: TranslateService;

    constructor() {
        LangService._translateService = inject(TranslateService);
    }

    static getName(name: Name): string {
        return LangService._translateService.currentLang === LanguageLayout.en
            ? name.en
            : name.fa;
    }

    static toggleLanguage(lang: LanguageLayout): void {
        LangService._translateService
            .use(lang)
            .subscribe(() => {
                localStorage.setItem(UtilConstants.CURRENT_LANG_KEY, lang);
                document.body.dir = Language.direction[lang.toUpperCase()];
            })
    }
}
