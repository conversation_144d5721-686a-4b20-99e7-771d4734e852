import {Injectable} from '@angular/core';
import {MatPaginatorIntl} from '@angular/material/paginator';
import {TranslateService} from '@ngx-translate/core';
import {forkJoin} from 'rxjs';

@Injectable()
export class CustomMatPaginatorIntl extends MatPaginatorIntl {
    itemsPerPageLabel = null;
    nextPageLabel = null;
    previousPageLabel = null;
    firstPageLabel = null;
    lastPageLabel = null;
    of = null;

    constructor(private translateService: TranslateService) {
        super();

        this.translateLabels();
    }

    getRangeLabel = (page, pageSize, length) => {
        if (length === 0 || pageSize === 0) {
            return '0 - ' + length;
        }
        length = Math.max(length, 0);
        const startIndex = page * pageSize;
        const endIndex = startIndex < length
            ? Math.min(startIndex + pageSize, length)
            : startIndex + pageSize;
        return `${startIndex + 1} - ${endIndex} ${this.of} ${length}`;
    };

    private translateLabels(): void {
        forkJoin([
            this.translateService.get('nextPage'),
            this.translateService.get('previousPage'),
            this.translateService.get('firstPage'),
            this.translateService.get('lastPage'),
            this.translateService.get('of'),
        ]).subscribe(([nextPage, previousPage, firstPage, lastPage, of]) => {
            this.nextPageLabel = nextPage;
            this.previousPageLabel = previousPage;
            this.firstPageLabel = firstPage;
            this.lastPageLabel = lastPage;
            this.of = of;
        })
    }
}
