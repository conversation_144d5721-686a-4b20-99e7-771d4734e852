import {Permission} from '@models/permission';
import {
    CHANGE_GROUP_STATE_PERMISSION,
    CHANGE_OPENING_STATIC_PRICE_PERMISSION,
    CHANGE_STATIC_PRICE_PERMISSION,
    CREATE_GROUP_PERMISSION,
    DELETE_GROUP_PERMISSION,
    F<PERSON><PERSON><PERSON>ED_GROUP_PRICE_BAND_PERMISSION,
    GROUP_CREDIT_CHECKING_PERMISSION,
    GROUPS_PERMISSION,
    NOT_FOLLOWED_GROUP_PRICE_BAND_PERMISSION,
    P<PERSON>GE_ORDERS_GROUP_PERMISSION,
    UPDATE_GROUP_PERMISSION
} from './permissions/groups.permission.constant';
import {
    BOARD_PERMISSION,
    CO<PERSON>ORATE_ACTIONS_PERMISSION,
    CREATE_BOARD_PERMISSION,
    CREATE_PRODUCT_PERMISSION,
    CREATE_PRODUCT_SUB_TYPE_PERMISSION,
    CREATE_PRODUCT_TYPE_PERMISSION,
    DELETE_BOARD_PERMISSION,
    DELETE_PRODUCT_PERMISSION,
    DELETE_PRODUCT_SUB_TYPE_PERMISSION,
    DELETE_PRODUCT_TYPE_PERMISSION,
    MARKET_FLOW_PERMISSION,
    PRODUCT_DETAILS_PERMISSION,
    PRODUCT_PURGE_ORDERS_PERMISSION,
    PRODUCT_SUB_TYPE_PERMISSION,
    PRODUCT_TYPE_PERMISSION,
    PRODUCTS_PERMISSION,
    UPDATE_BOARD_PERMISSION,
    UPDATE_PRODUCT_PERMISSION,
    UPDATE_PRODUCT_SELL_AVAILABILITY,
    UPDATE_PRODUCT_SUB_TYPE_PERMISSION,
    UPDATE_PRODUCT_TYPE_PERMISSION
} from './permissions/products.permission.constant';
import {
    APPLIED_CORPORATE_ACTIONS_PERMISSION,
    DELETE_CORPORATE_ACTION_PERMISSION,
    QUEUED_COMMANDS_PERMISSION,
    UPDATE_CORPORATE_ACTION_PERMISSION
} from './permissions/queued-commands.permission.constant';
import {
    ALLOW_SHORT_SELL_PERMISSION,
    CHANGE_GROUP_PERMISSION,
    CHANGE_INSTRUMENT_STATE_PERMISSION,
    CREATE_INSTRUMENT_PERMISSION,
    DELETE_INSTRUMENT_PERMISSION,
    INSTRUMENT_PERMISSION
} from './permissions/instruments.permission.constant';
import {
    CREATE_TRADE_PERMISSION,
    INSERT_ORDER_PERMISSION,
    ORDER_REQUESTS_PERMISSION,
    PURGE_ORDER_PERMISSION,
    TRACE_ORDER_REQUEST_PERMISSION
} from './permissions/order.permission.constant';
import {
    BLOCK_BROKER_PERMISSION,
    BROKER_CREDIT_CHECKING_PERMISSION,
    BROKERS_PERMISSION,
    CREATE_BROKER_PERMISSION,
    PURGE_ORDERS_BROKER_PERMISSION,
    SET_BROKER_CREDIT_PERMISSION,
    UPDATE_BROKER_PERMISSION
} from './permissions/brokers.permission.constant';
import {BLOCK_SHAREHOLDER_PERMISSION} from './permissions/shareholders.permission.constant';
import {
    CREATE_TRADER_PERMISSION,
    DELETE_TRADER_PERMISSION,
    PURGE_ORDERS_TRADER_PERMISSION,
    TRADER_PERMISSION
} from './permissions/traders.permission.constant';
import {
    COMPANIES_PERMISSION,
    COMPANY_DETAILS_PERMISSION,
    CREATE_COMPANY_PERMISSION,
    CREATE_SECTOR_PERMISSION,
    CREATE_SUB_SECTOR_PERMISSION,
    DELETE_COMPANY_PERMISSION,
    DELETE_SECTOR_PERMISSION,
    DELETE_SUB_SECTOR_PERMISSION,
    SECTOR_PERMISSION,
    SUB_SECTOR_PERMISSION,
    UPDATE_COMPANY_PERMISSION,
    UPDATE_SECTOR_PERMISSION,
    UPDATE_SUB_SECTOR_PERMISSION
} from './permissions/companies.permission.constant';
import {
    CREATE_TOWN_PERMISSION,
    TOWNS_PERMISSION,
    UPDATE_TOWN_PERMISSION
} from './permissions/towns.permission.constant';
import {
    ASSOCIATED_TEMPLATE_PERMISSION,
    CALENDAR_PERMISSION,
    CREATE_GROUP_SCHEDULE_PERMISSION,
    CREATE_SCHEDULE_TEMPLATE_PERMISSION,
    CREATE_SYSTEM_SCHEDULE_PERMISSION,
    CREATE_WEEKEND_PERMISSION,
    DELETE_DEFERRED_SCHEDULE_PERMISSION,
    DELETE_GROUP_SCHEDULE_PERMISSION,
    DELETE_SCHEDULE_TEMPLATE_PERMISSION,
    DELETE_SESSION_SCHEDULE_PERMISSION,
    DELETE_SYSTEM_SCHEDULE_PERMISSION,
    GET_GROUP_SCHEDULES_PERMISSION,
    GET_GROUP_SESSION_SCHEDULE_PERMISSION,
    GET_GROUPS_SCHEDULES_PERMISSION,
    GET_SCHEDULE_TEMPLATE_PERMISSION,
    GET_SYSTEM_SCHEDULE_PERMISSION,
    GET_SYSTEM_SCHEDULES_PERMISSION,
    GET_SYSTEM_SESSION_SCHEDULE_PERMISSION,
    SCHEDULE_TEMPLATE_PERMISSION,
    SESSION_SCHEDULE_PERMISSION,
    SET_DEFERRED_SCHEDULE_PERMISSION,
    UPDATE_GROUP_SCHEDULE_PERMISSION,
    UPDATE_GROUP_SESSION_SCHEDULE_PERMISSION,
    UPDATE_SESSION_SCHEDULE_PERMISSION,
    UPDATE_SYSTEM_SCHEDULE_PERMISSION, UPLOAD_DEFERRED_SCHEDULE_PERMISSION
} from './permissions/schedules.permission.constant';
import {
    CREATE_NOTIFY_MARKET_TEMPLATE_PERMISSION,
    DELETE_NOTIFY_MARKET_TEMPLATE_PERMISSION,
    MY_NOTIFICATIONS_PERMISSION,
    NOTIFICATIONS_PERMISSION,
    NOTIFY_MARKET_PERMISSION,
    NOTIFY_MARKET_TEMPLATE_PERMISSION,
    REPORTS_PERMISSION,
    UPDATE_NOTIFY_MARKET_TEMPLATE_PERMISSION
} from './permissions/market-notification.permission.constant';
import {
    BROKER_ACCESS_MATRIX_PERMISSION,
    COPY_ACCESS_MATRIX_PERMISSION,
    CREATE_BROKER_ACCESS_MATRIX_PERMISSION,
    CREATE_TRADER_ACCESS_MATRIX_PERMISSION,
    DELETE_ACCESS_MATRIX_PERMISSION,
    TRADER_ACCESS_MATRIX_PERMISSION,
    UPDATE_ACCESS_MATRIX_PERMISSION
} from './permissions/access-matrix.permission.constant';
import {
    CLEARING_REPORT_PERMISSION,
    DOWNLOAD_ARAMIS_PERMISSION,
    M1_FILES_PERMISSION,
    UPLOAD_ABSOLUTE_STATIC_THRESHOLD_PERMISSION,
    UPLOAD_CSD_FILE_PERMISSION,
    UPLOAD_GROUP_STATIC_THRESHOLD_PERMISSION,
    UPLOAD_INSTRUMENT_ALLOW_SHORT_SELL,
    UPLOAD_INSTRUMENT_IMMEDIATE_STATE_UPDATE_PERMISSION,
    UPLOAD_INSTRUMENT_IMMEDIATE_UPDATE_PERMISSION,
    UPLOAD_ORDERS_PERMISSION,
    UPLOAD_PERCENTAGE_STATIC_THRESHOLD_PERMISSION, UPLOAD_PRODUCT_SELL_AVAILABILITY,
    UPLOAD_REFERENCE_PRICE_PERMISSION
} from './permissions/files.permission.constant';
import {
    ADD_CAP_TO_GROUP_PERMISSION,
    ADD_SUB_GROUP_PERMISSION,
    APPLY_ROUTE_TREE_CONFIGURATION_PERMISSION,
    BIND_ROUTE_NODE_PERMISSION,
    CAP_DETAILS_PERMISSION,
    CAP_GROUP_DETAILS_PERMISSION,
    CAP_GROUPS_PERMISSION,
    CAPS_PERMISSION,
    CREATE_NEW_CAP_GROUP_PERMISSION,
    CREATE_RULE_ENGINE_PERMISSION,
    DELETE_ALL_RULE_ENGINE_PERMISSION,
    DELETE_CAP_GROUP_PERMISSION,
    DELETE_CAP_PERMISSION,
    DELETE_ROUTE_PREDICATE_PERMISSION,
    DELETE_RULE_ENGINE_PERMISSION,
    GET_ROUTE_PREDICATES_PERMISSION,
    RESEND_RULE_ENGINE_PERMISSION,
    ROUTE_TREE_PERMISSION,
    RULE_ENGINE_PERMISSION,
    SAVE_ROUTE_TREE_PERMISSION,
    UNBIND_ROUTE_NODE_PERMISSION,
    UPDATE_CAP_PERMISSION,
    UPDATE_ROUTE_PREDICATE_PERMISSION,
    UPDATE_ROUTE_TREE_PERMISSION,
    UPDATE_RULE_ENGINE_PERMISSION
} from './permissions/message-routing.permisson.constant';
import {
    CREATE_USER_PERMISSION,
    REGISTER_NEW_DEVICE_PERMISSION,
    UPDATE_USER_PERMISSION,
    USERS_PERMISSION
} from './permissions/user-management.permission.constant';
import {
    CREATE_ROLE_PERMISSION,
    DELETE_ROLE_PERMISSION,
    ROLES_MANAGEMENT_PERMISSION,
    UPDATE_ROLE_PERMISSION
} from './permissions/user-roles-management.permission.constant';
import {
    CREATE_PERMISSION_PERMISSION,
    DELETE_PERMISSION_PERMISSION,
    PERMISSIONS_PERMISSION,
    UPDATE_PERMISSION_PERMISSION
} from './permissions/permissions.permission.constant';
import {
    CHANGE_CREDIT_CHECKING_PERMISSION,
    START_INITIALIZATION_PERMISSION,
    TOGGLE_SYSTEM_ACTIVATION_PERMISSION,
    UPDATE_SYSTEM_STATE_PERMISSION
} from './permissions/system.permission.constant';
import {
    GET_ORIGIN_PERMISSION,
    GET_ORIGINS_PERMISSION,
    PUT_ORIGIN_PERMISSION
} from './permissions/origin.permission.constant';

export const Permissions: {[prop: string]: Pick<Permission, 'uri' | 'method'>[]} = {
    // SCHEDULE
    SCHEDULE_TEMPLATE_PERMISSION,
    GET_SCHEDULE_TEMPLATE_PERMISSION,
    CREATE_SCHEDULE_TEMPLATE_PERMISSION,
    DELETE_SCHEDULE_TEMPLATE_PERMISSION,
    ASSOCIATED_TEMPLATE_PERMISSION,

    GET_GROUPS_SCHEDULES_PERMISSION,
    CREATE_GROUP_SCHEDULE_PERMISSION,
    GET_GROUP_SCHEDULES_PERMISSION,
    UPDATE_GROUP_SCHEDULE_PERMISSION,
    DELETE_GROUP_SCHEDULE_PERMISSION,

    GET_SYSTEM_SCHEDULES_PERMISSION,
    CREATE_SYSTEM_SCHEDULE_PERMISSION,
    GET_SYSTEM_SCHEDULE_PERMISSION,
    UPDATE_SYSTEM_SCHEDULE_PERMISSION,
    DELETE_SYSTEM_SCHEDULE_PERMISSION,

    GET_GROUP_SESSION_SCHEDULE_PERMISSION,
    UPDATE_GROUP_SESSION_SCHEDULE_PERMISSION,

    SESSION_SCHEDULE_PERMISSION,
    SET_DEFERRED_SCHEDULE_PERMISSION,
    UPLOAD_DEFERRED_SCHEDULE_PERMISSION,
    DELETE_DEFERRED_SCHEDULE_PERMISSION,

    GET_SYSTEM_SESSION_SCHEDULE_PERMISSION,

    UPDATE_SESSION_SCHEDULE_PERMISSION,
    DELETE_SESSION_SCHEDULE_PERMISSION,
    CALENDAR_PERMISSION,
    CREATE_WEEKEND_PERMISSION,

    // PRODUCT
    PRODUCTS_PERMISSION,
    CREATE_PRODUCT_PERMISSION,
    UPDATE_PRODUCT_PERMISSION,
    DELETE_PRODUCT_PERMISSION,
    PRODUCT_PURGE_ORDERS_PERMISSION,
    CORPORATE_ACTIONS_PERMISSION,
    PRODUCT_DETAILS_PERMISSION,
    PRODUCT_TYPE_PERMISSION,
    CREATE_PRODUCT_TYPE_PERMISSION,
    UPDATE_PRODUCT_TYPE_PERMISSION,
    DELETE_PRODUCT_TYPE_PERMISSION,
    PRODUCT_SUB_TYPE_PERMISSION,
    CREATE_PRODUCT_SUB_TYPE_PERMISSION,
    UPDATE_PRODUCT_SUB_TYPE_PERMISSION,
    DELETE_PRODUCT_SUB_TYPE_PERMISSION,
    BOARD_PERMISSION,
    CREATE_BOARD_PERMISSION,
    UPDATE_BOARD_PERMISSION,
    DELETE_BOARD_PERMISSION,
    MARKET_FLOW_PERMISSION,
    QUEUED_COMMANDS_PERMISSION,
    UPDATE_CORPORATE_ACTION_PERMISSION,
    DELETE_CORPORATE_ACTION_PERMISSION,
    APPLIED_CORPORATE_ACTIONS_PERMISSION,
    UPDATE_PRODUCT_SELL_AVAILABILITY,

    // INSTRUMENT
    INSTRUMENT_PERMISSION,
    CREATE_INSTRUMENT_PERMISSION,
    DELETE_INSTRUMENT_PERMISSION,
    CHANGE_GROUP_PERMISSION,
    ALLOW_SHORT_SELL_PERMISSION,
    CHANGE_INSTRUMENT_STATE_PERMISSION,
    INSERT_ORDER_PERMISSION,
    CREATE_TRADE_PERMISSION,

    // GROUP
    GROUPS_PERMISSION,
    CREATE_GROUP_PERMISSION,
    UPDATE_GROUP_PERMISSION,
    DELETE_GROUP_PERMISSION,
    FOLLOWED_GROUP_PRICE_BAND_PERMISSION,
    NOT_FOLLOWED_GROUP_PRICE_BAND_PERMISSION,
    PURGE_ORDERS_GROUP_PERMISSION,
    CHANGE_STATIC_PRICE_PERMISSION,
    GROUP_CREDIT_CHECKING_PERMISSION,
    CHANGE_GROUP_STATE_PERMISSION,
    CHANGE_OPENING_STATIC_PRICE_PERMISSION,

    // BROKER
    BROKERS_PERMISSION,
    CREATE_BROKER_PERMISSION,
    UPDATE_BROKER_PERMISSION,
    BLOCK_BROKER_PERMISSION,
    SET_BROKER_CREDIT_PERMISSION,
    PURGE_ORDERS_BROKER_PERMISSION,
    BROKER_CREDIT_CHECKING_PERMISSION,

    // SHAREHOLDER
    BLOCK_SHAREHOLDER_PERMISSION,

    // TRADER
    TRADER_PERMISSION,
    CREATE_TRADER_PERMISSION,
    DELETE_TRADER_PERMISSION,
    PURGE_ORDERS_TRADER_PERMISSION,

    // COMPANY
    COMPANIES_PERMISSION,
    CREATE_COMPANY_PERMISSION,
    COMPANY_DETAILS_PERMISSION,
    UPDATE_COMPANY_PERMISSION,
    DELETE_COMPANY_PERMISSION,
    SECTOR_PERMISSION,
    CREATE_SECTOR_PERMISSION,
    UPDATE_SECTOR_PERMISSION,
    DELETE_SECTOR_PERMISSION,
    SUB_SECTOR_PERMISSION,
    CREATE_SUB_SECTOR_PERMISSION,
    UPDATE_SUB_SECTOR_PERMISSION,
    DELETE_SUB_SECTOR_PERMISSION,

    // TOWN
    TOWNS_PERMISSION,
    CREATE_TOWN_PERMISSION,
    UPDATE_TOWN_PERMISSION,

    // ORDER
    PURGE_ORDER_PERMISSION,
    ORDER_REQUESTS_PERMISSION,
    TRACE_ORDER_REQUEST_PERMISSION,

    // ORIGIN
    GET_ORIGINS_PERMISSION,
    GET_ORIGIN_PERMISSION,
    PUT_ORIGIN_PERMISSION,

    // MARKET NOTIFICATION
    NOTIFY_MARKET_TEMPLATE_PERMISSION,
    CREATE_NOTIFY_MARKET_TEMPLATE_PERMISSION,
    UPDATE_NOTIFY_MARKET_TEMPLATE_PERMISSION,
    DELETE_NOTIFY_MARKET_TEMPLATE_PERMISSION,
    NOTIFY_MARKET_PERMISSION,
    NOTIFICATIONS_PERMISSION,
    MY_NOTIFICATIONS_PERMISSION,
    REPORTS_PERMISSION,

    // ACCESS MATRIX
    TRADER_ACCESS_MATRIX_PERMISSION,
    CREATE_TRADER_ACCESS_MATRIX_PERMISSION,
    BROKER_ACCESS_MATRIX_PERMISSION,
    CREATE_BROKER_ACCESS_MATRIX_PERMISSION,
    UPDATE_ACCESS_MATRIX_PERMISSION,
    DELETE_ACCESS_MATRIX_PERMISSION,
    COPY_ACCESS_MATRIX_PERMISSION,

    // FILES
    CLEARING_REPORT_PERMISSION,
    M1_FILES_PERMISSION,
    UPLOAD_REFERENCE_PRICE_PERMISSION,
    UPLOAD_ABSOLUTE_STATIC_THRESHOLD_PERMISSION,
    UPLOAD_PERCENTAGE_STATIC_THRESHOLD_PERMISSION,
    UPLOAD_GROUP_STATIC_THRESHOLD_PERMISSION,
    UPLOAD_INSTRUMENT_IMMEDIATE_UPDATE_PERMISSION,
    UPLOAD_INSTRUMENT_IMMEDIATE_STATE_UPDATE_PERMISSION,
    UPLOAD_CSD_FILE_PERMISSION,
    UPLOAD_ORDERS_PERMISSION,
    DOWNLOAD_ARAMIS_PERMISSION,
    UPLOAD_INSTRUMENT_ALLOW_SHORT_SELL,
    UPLOAD_PRODUCT_SELL_AVAILABILITY,

    // MESSAGE ROUTING
    ROUTE_TREE_PERMISSION,
    SAVE_ROUTE_TREE_PERMISSION,
    UPDATE_ROUTE_TREE_PERMISSION,
    APPLY_ROUTE_TREE_CONFIGURATION_PERMISSION,
    CAP_GROUPS_PERMISSION,
    CREATE_NEW_CAP_GROUP_PERMISSION,
    ADD_CAP_TO_GROUP_PERMISSION,
    ADD_SUB_GROUP_PERMISSION,
    BIND_ROUTE_NODE_PERMISSION,
    UNBIND_ROUTE_NODE_PERMISSION,
    CAP_GROUP_DETAILS_PERMISSION,
    DELETE_CAP_GROUP_PERMISSION,
    CAPS_PERMISSION,
    CAP_DETAILS_PERMISSION,
    UPDATE_CAP_PERMISSION,
    DELETE_CAP_PERMISSION,
    RULE_ENGINE_PERMISSION,
    CREATE_RULE_ENGINE_PERMISSION,
    RESEND_RULE_ENGINE_PERMISSION,
    DELETE_ALL_RULE_ENGINE_PERMISSION,
    DELETE_RULE_ENGINE_PERMISSION,
    UPDATE_RULE_ENGINE_PERMISSION,
    GET_ROUTE_PREDICATES_PERMISSION,
    UPDATE_ROUTE_PREDICATE_PERMISSION,
    DELETE_ROUTE_PREDICATE_PERMISSION,

    // USER MANAGEMENT
    USERS_PERMISSION,
    CREATE_USER_PERMISSION,
    UPDATE_USER_PERMISSION,
    REGISTER_NEW_DEVICE_PERMISSION,

    // ROLE MANAGEMENT
    ROLES_MANAGEMENT_PERMISSION,
    CREATE_ROLE_PERMISSION,
    UPDATE_ROLE_PERMISSION,
    DELETE_ROLE_PERMISSION,

    // PERMISSIONS
    PERMISSIONS_PERMISSION,
    CREATE_PERMISSION_PERMISSION,
    UPDATE_PERMISSION_PERMISSION,
    DELETE_PERMISSION_PERMISSION,

    // SYSTEM
    TOGGLE_SYSTEM_ACTIVATION_PERMISSION,
    CHANGE_CREDIT_CHECKING_PERMISSION,
    START_INITIALIZATION_PERMISSION,
    UPDATE_SYSTEM_STATE_PERMISSION

}
