export const GROUPS_PERMISSION = [
    {
        method: 'GET',
        uri: '/groups'
    }
];

export const CREATE_GROUP_PERMISSION = [
    {
        method: 'POST',
        uri: '/groups'
    }
];

export const UPDATE_GROUP_PERMISSION = [
    {
        method: 'PUT',
        uri: '/groups/groupCode'
    }
];

export const DELETE_GROUP_PERMISSION = [
    {
        method: 'GET',
        uri: '/groups/groupCode/queued-securities'
    },
    {
        method: 'DELETE',
        uri: '/groups/groupCode'
    }
];

export const PURGE_ORDERS_GROUP_PERMISSION = [
    {
        method: 'DELETE',
        uri: '/groups/groupCode/orders'
    }
];

export const CHANGE_STATIC_PRICE_PERMISSION = [
    {
        method: 'POST',
        uri: '/groups/groupCode/static-threshold'
    }
];

export const GROUP_CREDIT_CHECKING_PERMISSION = [
    {
        method: 'POST',
        uri: '/groups/groupCode/credit-checking'
    }
];

export const CHANGE_GROUP_STATE_PERMISSION = [
    {
        method: 'POST',
        uri: '/groups/groupCode/state'
    }
];

export const FOLLOWED_GROUP_PRICE_BAND_PERMISSION = [
    {
        method: 'GET',
        uri: '/groups/groupCode/static-threshold/following-securities'
    }
];

export const NOT_FOLLOWED_GROUP_PRICE_BAND_PERMISSION = [
    {
        method: 'GET',
        uri: '/groups/31/static-threshold/not-following-securities'
    }
];

export const CHANGE_OPENING_STATIC_PRICE_PERMISSION = [
    {
        method: 'POST',
        uri: '/groups/groupCode/opening-update-price-band'
    }
];
