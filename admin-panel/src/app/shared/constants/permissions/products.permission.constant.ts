export const PRODUCT_TYPE_PERMISSION = [
    {
        method: 'GET',
        uri: '/product-types'
    }
];

export const CREATE_PRODUCT_TYPE_PERMISSION = [
    {
        method: 'POST',
        uri: '/product-types'
    }
];

export const UPDATE_PRODUCT_TYPE_PERMISSION = [
    {
        method: 'PUT',
        uri: '/product-types/productTypeCode'
    }
];

export const DELETE_PRODUCT_TYPE_PERMISSION = [
    {
        method: 'GET',
        uri: '/product-types/productTypeCode/queued-products'
    },
    {
        method: 'DELETE',
        uri: '/product-types/productTypeCode'
    }
];

export const PRODUCT_SUB_TYPE_PERMISSION = [
    {
        method: 'GET',
        uri: '/product-types'
    }
];

export const CREATE_PRODUCT_SUB_TYPE_PERMISSION = [
    ...PRODUCT_TYPE_PERMISSION,
    {
        method: 'POST',
        uri: '/product-sub-types'
    }
];

export const UPDATE_PRODUCT_SUB_TYPE_PERMISSION = [
    ...PRODUCT_TYPE_PERMISSION,
    {
        method: 'PUT',
        uri: '/product-sub-types/productSubTypeCode'
    }
];

export const DELETE_PRODUCT_SUB_TYPE_PERMISSION = [
    {
        method: 'GET',
        uri: '/product-sub-types/1/queued-products'
    },
    {
        method: 'DELETE',
        uri: '/product-sub-types/productSubTypeCode'
    }
];

export const BOARD_PERMISSION = [
    {
        method: 'GET',
        uri: '/boards'
    }
];

export const CREATE_BOARD_PERMISSION = [
    {
        method: 'POST',
        uri: '/boards'
    }
];

export const UPDATE_BOARD_PERMISSION = [
    {
        method: 'PUT',
        uri: '/boards/boardCode'
    }
];

export const DELETE_BOARD_PERMISSION = [
    {
        method: 'GET',
        uri: '/boards/boardCode/queued-products'
    },
    {
        method: 'DELETE',
        uri: '/boards/boardCode'
    }
];

export const MARKET_FLOW_PERMISSION = [
    {
        method: 'GET',
        uri: '/market-flows'
    },
];

export const PRODUCTS_PERMISSION = [
    ...BOARD_PERMISSION,
    ...PRODUCT_TYPE_PERMISSION,
    ...PRODUCT_SUB_TYPE_PERMISSION,
    {
        method: 'GET',
        uri: '/products'
    }
];

export const CREATE_PRODUCT_PERMISSION = [
    {
        method: 'POST',
        uri: '/products'
    },
    {
        method: 'GET',
        uri: '/companies'
    },
    ...BOARD_PERMISSION,
    ...MARKET_FLOW_PERMISSION,
    ...PRODUCT_TYPE_PERMISSION,
    ...PRODUCT_SUB_TYPE_PERMISSION
];

export const PRODUCT_DETAILS_PERMISSION = [
    {
        method: 'GET',
        uri: '/products/productId'
    }
];

export const UPDATE_PRODUCT_PERMISSION = [
    {
        method: 'PUT',
        uri: '/products/productId'
    },
    ...BOARD_PERMISSION,
    ...PRODUCT_DETAILS_PERMISSION,
    ...MARKET_FLOW_PERMISSION,
];

export const DELETE_PRODUCT_PERMISSION = [
    {
        method: 'DELETE',
        uri: '/products/productId'
    }
];

export const PRODUCT_PURGE_ORDERS_PERMISSION = [
    {
        method: 'DELETE',
        uri: '/products/productId/orders'
    }
];

export const CORPORATE_ACTIONS_PERMISSION = [
    {
        method: 'POST',
        uri: '/products/productId/dividends-corporate-action'
    },
      {
        method: 'POST',
        uri: '/products/productId/rights-corporate-action'
    },
    {
        method: 'POST',
        uri: '/products/productId/bonus-corporate-action'
    },
    {
        method: 'POST',
        uri: '/products/productId/bonusrights-corporate-action'
    },
    {
        method: 'POST',
        uri: '/products/productId/custom-corporate-action'
    }
];

export const UPDATE_PRODUCT_SELL_AVAILABILITY = [
    {
        method: 'PUT',
        uri: '/products/productId/update-sell-availability'
    }
];
