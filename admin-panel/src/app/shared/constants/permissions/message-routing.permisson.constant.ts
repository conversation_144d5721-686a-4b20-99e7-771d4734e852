import {TOWNS_PERMISSION} from './towns.permission.constant';

export const CAPS_PERMISSION = [
    {
        method: 'GET',
        uri: '/caps'
    }
];

export const UPDATE_CAP_PERMISSION = [
    {
        method: 'PUT',
        uri: '/caps/capId'
    }
];

export const DELETE_CAP_PERMISSION = [
    {
        method: 'DELETE',
        uri: '/caps/capId'
    }
];

export const CAP_DETAILS_PERMISSION = [
    {
        method: 'GET',
        uri: '/caps/capId'
    }
];

export const CAP_GROUPS_PERMISSION = [
    ...CAPS_PERMISSION,
    {
        method: 'GET',
        uri: '/cap-groups'
    },
    {
        method: 'GET',
        uri: '/route-tree/simple-view'
    }
];

export const CREATE_NEW_CAP_GROUP_PERMISSION = [
    {
        method: 'POST',
        uri: '/cap-groups'
    }
];

export const CAP_GROUP_DETAILS_PERMISSION = [
    {
        method: 'GET',
        uri: '/cap-groups/CapGroupName'
    }
];

export const DELETE_CAP_GROUP_PERMISSION = [
    {
        method: 'DELETE',
        uri: '/cap-groups/CapGroupName'
    }
];

export const ADD_CAP_TO_GROUP_PERMISSION = [
    {
        method: 'POST',
        uri: 'cap-groups/CapGroupName/caps'
    }
];

export const ADD_SUB_GROUP_PERMISSION = [
    {
        method: 'POST',
        uri: '/cap-groups/CapGroupName'
    }
];

export const BIND_ROUTE_NODE_PERMISSION = [
    {
        method: 'POST',
        uri: '/cap-groups/capGroupName/bind'
    }
];

export const UNBIND_ROUTE_NODE_PERMISSION = [
    {
        method: 'DELETE',
        uri: '/cap-groups/CapGroupName/bind/routeNode'
    }
];


export const ROUTE_TREE_PERMISSION = [
    {
        method: 'GET',
        uri: '/route-tree'
    },
    {
        method: 'GET',
        uri: '/cap-groups'
    },
    {
        method: 'GET',
        uri: '/route-tree/valid-root-codes'
    }
];

export const SAVE_ROUTE_TREE_PERMISSION = [
    {
        method: 'POST',
        uri: '/route-tree'
    }
];

export const UPDATE_ROUTE_TREE_PERMISSION = [
    {
        method: 'POST',
        uri: '/route-tree/lock/routeTreeType'
    }
];

export const APPLY_ROUTE_TREE_CONFIGURATION_PERMISSION = [
    {
        method: 'POST',
        uri: '/route-tree/submit'
    }
];

export const RULE_ENGINE_PERMISSION = [
    {
        method: 'GET',
        uri: '/broker-requests/rules'
    }
];

export const CREATE_RULE_ENGINE_PERMISSION = [
    ...TOWNS_PERMISSION,
    {
        method: 'POST',
        uri: '/broker-requests/rules'
    }
];

export const RESEND_RULE_ENGINE_PERMISSION = [
    {
        method: 'PUT',
        uri: '/broker-requests/rules/resend'
    }
];

export const DELETE_ALL_RULE_ENGINE_PERMISSION = [
    {
        method: 'DELETE',
        uri: '/broker-requests/rules'
    }
];

export const DELETE_RULE_ENGINE_PERMISSION = [
    {
        method: 'DELETE',
        uri: '/broker-requests/rules/ruleId'
    }
];

export const UPDATE_RULE_ENGINE_PERMISSION = [
    {
        method: 'PUT',
        uri: '/broker-requests/rules/ruleId'
    }
];

export const GET_ROUTE_PREDICATES_PERMISSION = [
    {
        method: 'GET',
        uri: '/route-tree/predicate'
    }
];

export const UPDATE_ROUTE_PREDICATE_PERMISSION = [
    {
        method: 'PUT',
        uri: '/route-tree/predicate/title'
    }
];

export const DELETE_ROUTE_PREDICATE_PERMISSION = [
    {
        method: 'DELETE',
        uri: '/route-tree/predicate/title'
    }
];
