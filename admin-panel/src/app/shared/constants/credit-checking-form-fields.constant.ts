import {Validators} from '@angular/forms';
import {Translate<PERSON><PERSON>} from '@shared/enums/translate-key';
import {SelectOptionFormField} from '@modules/dynamic-form/form-fields/select-option-field/select-option-form-field';
import {FormFields} from '@modules/dynamic-form/form-field';

export function creditCheckingFormFields(creditCheckingStatus: boolean): FormFields {
    return [
        new SelectOptionFormField({
            formControlName: 'creditCheckingStatus',
            label: TranslateKey.creditCheckingStatus,
            width: 12,
            validations: [Validators.required],
            options: [true, false],
            getTitle(data): string { return data.toString(); },
            value: creditCheckingStatus
        })
    ];
}
