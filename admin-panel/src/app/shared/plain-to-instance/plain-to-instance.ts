import {ClassConstructor} from './class-constructor';
import {GETTER_KEY} from './getter';
import {getTransformerKey} from './transform';
import {getTypeKey} from './type';

export function plainToInstance<T, V>(instance: ClassConstructor<T>, plain: V[]): T[];
export function plainToInstance<T, V>(instance: ClassConstructor<T>, plain: V): T;
export function plainToInstance<T, V>(instance: ClassConstructor<T>, plainObj: V | V[]): T | T[] {
    return Array.isArray(plainObj)
        ? plainObj.map(item => mapPlainToInstance(instance, item))
        : mapPlainToInstance(instance, plainObj);
}

function mapPlainToInstance<T, V>(instance: ClassConstructor<T>, plainObj: V): T {
    if (plainObj instanceof instance) {
        plainObj = plainObj['$plainObj'];
    }
    const targetType = new instance();
    targetType['$plainObj'] = plainObj;

    for (let key in plainObj) {
        const prop = key.toString();
        const value = plainObj[prop];

        if (prop.startsWith('_')) {
            targetType[prop] = value;
            continue;
        }

        if (isDefined(prop)) {
            if (targetType[getTypeKey(prop)]) {
                const type = targetType[getTypeKey(prop)](value);
                targetType[type.propertyName] = type.value;
                if (type.propertyName !== prop) {
                    targetType[prop] = value;
                }
            } else if (targetType[getTransformerKey(prop)]) {
                const transform = targetType[getTransformerKey(prop)](value, plainObj);
                targetType[transform.propertyName] = transform.value;
            } else {
                targetType[prop] = value;
            }

            continue;
        }

        targetType['_' + prop] = targetType[getTypeKey('_' + prop)]
            ? targetType[getTypeKey('_' + prop)](value).value
            : targetType[getTransformerKey('_' + prop)]
                ? targetType[getTransformerKey('_' + prop)](value, plainObj).value
                : value;
    }

    targetType['$initials']?.forEach(item => item(targetType));
    delete targetType['$initials'];

    return targetType;

    function isDefined(propertyName: string): boolean {
        return Object.getOwnPropertyDescriptor(instance.prototype, propertyName)?.get
            ? false
            : (targetType[propertyName] === GETTER_KEY || instance.prototype[propertyName] === GETTER_KEY);
    }
}
