import {ClassConstructor} from './class-constructor';
import {GETTER_KEY} from './getter';
import {plainToInstance} from './plain-to-instance';

export function Type(instance: ClassConstructor, plainProp?: string) {
    return (target: any, prop: string): void  => {
        if (plainProp) {
            target[plainProp] = GETTER_KEY;
        }

        target[getTypeKey(plainProp || prop)] = (_) => ({
            propertyName: prop,
            value: plainToInstance(instance, _)
        });
    }
}

export function getTypeKey(propertyName: string): string{
    return '$type_' + propertyName;
}
