import {plainToInstance} from './plain-to-instance';
import {Transform} from './transform';

describe('Transform', () => {
    class User {
        private readonly _name: string;
        get name(): string { return this._name; }

        private readonly _userId: number;
        get userId(): number { return this._userId; }

        constructor(name: string, userId: number) {
            this._name = name;
            this._userId = userId;
        }
    }
    class Accounts {
        @Transform(Accounts.mapUserId, 'userId')
        readonly user: User;

        private static mapUserId(id: number): User {
            return new User('Bob', id);
        }
    }

    it('should #Transform map plain object/array by its value', () => {
        const plainObj = {
            userId: 1,
        }

        const myObj = plainToInstance(Accounts, plainObj);
        expect(myObj.user.userId).toEqual(1);
        expect(myObj.user.name).toEqual('Bob');
    });
});
