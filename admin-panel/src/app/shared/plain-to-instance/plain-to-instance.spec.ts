import {plainToInstance} from './plain-to-instance';

describe('plainToInstance', () => {
    class InstanceClass {
        private _name: string;
        get name(): string { return this._name; }

        private _lName: string;
        get lName(): string { return this._lName; }

        private _age: number;
        get age(): number { return this._age; }
    }

    it('should #plainToInstance convert plain object to instance object', () => {
        const plainObj = {
            name: 'Name',
            lName: 'Last Name',
            age: 3
        }

        const myObj = plainToInstance(InstanceClass, plainObj);
        expect(myObj.name).toEqual('Name');
        expect(myObj.lName).toEqual('Last Name');
        expect(myObj.age).toEqual(3);
    });

    it('should #plainToInstance convert plain array to instance array', () => {
        const plainArray = [{
            name: 'Name',
            lName: 'Last Name',
            age: 3
        }]

        const myObj = plainToInstance(InstanceClass, plainArray);
        expect(myObj[0].name).toEqual('Name');
        expect(myObj[0].lName).toEqual('Last Name');
        expect(myObj[0].age).toEqual(3);
    });
});
