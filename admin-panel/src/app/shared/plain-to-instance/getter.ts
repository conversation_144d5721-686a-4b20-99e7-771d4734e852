import {Type} from '@angular/core';

export const GETTER_KEY = '$GETTER';

export function Getter(target: Type<any>): void;
// tslint:disable-next-line:unified-signatures
export function Getter(target: object, propertyName?: string): void;

export function Getter(target: object | Type<any>, propertyName?: string): void {
    if (typeof target === 'object') {
        target[propertyName] = GETTER_KEY;
    } else {
        const instanceOfTargetClass = new target();
        for (let prop in instanceOfTargetClass) {
            if (instanceOfTargetClass[prop] === undefined) {
                target.prototype[prop] = GETTER_KEY;
            }
        }
    }
}
