import {Directive, HostListener, Input} from '@angular/core';
import {Shareholder} from '@models/shareholder';
import {BsModalService} from 'ngx-bootstrap/modal';
import {TransferShareDialog} from '@models/transfer-share-dialog';
import {
    TransferShareDialogComponent
} from '../../feature/home/<USER>/transfer-share-dialog/transfer-share-dialog.component';

@Directive({
    selector: '[transferShareDialog]',
    standalone: false
})
export class TransferShareDialogDirective {
    @Input() shareholder: Shareholder;

    @HostListener('click') onClick(): void {
        this.modalService.show(TransferShareDialogComponent, {
            initialState: new TransferShareDialog({
                shareholder: this.shareholder
            })
        })
    }

    constructor(private modalService: BsModalService) { }

}
