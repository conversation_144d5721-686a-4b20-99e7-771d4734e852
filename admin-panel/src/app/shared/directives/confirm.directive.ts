import {Directive, EventEmitter, HostListener, Input, Output} from '@angular/core';
import {BsModalService} from 'ngx-bootstrap/modal';
import {ConfirmDialogComponent} from '../components/confirm-dialog/confirm-dialog.component';
import {ConfirmDialog} from '@models/confirm-dialog';
import {ThemePalette} from '@angular/material/core';
import {Observable} from 'rxjs';

@Directive({
    selector: '[confirm]',
    standalone: false
})
export class ConfirmDirective {
    @Input() message: string | Observable<string>;

    @Input() type: ThemePalette = 'primary';

    @Input() disabled: boolean;

    @Input() confirmAction: 'click' | 'blur' | 'change' = 'click'

    @Output() confirm = new EventEmitter();

    @Output() cancel = new EventEmitter();

    constructor(private _modalService: BsModalService) {}

    @HostListener('click') onClick() {
        if (this.confirmAction === 'click') {
            this._onAction();
        }
    }

    @HostListener('blur') onBlur() {
        if (this.confirmAction === 'blur') {
            this._onAction();
        }
    }

    @HostListener('change') onChange() {
        if (this.confirmAction === 'change') {
            this._onAction();
        }
    }

    private _onAction(): void {
        if (this.disabled) { return; }

        this._modalService.show(ConfirmDialogComponent, {
            initialState: new ConfirmDialog({
                onConfirm: () => this.confirm.emit(),
                onCancel: () => this.cancel.emit(),
                type: this.type,
                message: this.message
            })
        })
    }
}
