import {ConfirmDirective} from './confirm.directive';
import {inject, TestBed} from '@angular/core/testing';
import {BsModalService, ModalModule} from 'ngx-bootstrap/modal';

describe('ConfirmDirective', () => {
    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [BsModalService],
            imports: [ModalModule.forRoot()]
        });
    });

    it('should create an instance', inject([BsModalService], (modalService: BsModalService) => {
        const directive = new ConfirmDirective(modalService);
        expect(directive).toBeTruthy();
    }));
});
