import {Directive, Input, OnInit, TemplateRef, ViewContainerRef} from '@angular/core';
import {Permission} from '@models/permission';
import {HasPermissionService} from '@directives/has-permission.service';

@Directive({
    selector: '[hasPermission]',
    standalone: false
})
export class HasPermissionDirective implements OnInit {
    @Input()
    hasPermission: Pick<Permission, 'uri' | 'method'>[];

    constructor(
        private _templateRef: TemplateRef<any>,
        private _viewContainerRef: ViewContainerRef,
        private _hasPermissionService: HasPermissionService
    ) { }

    ngOnInit() {
        this._updateView();
    }

    private _updateView() {
        if (this._hasPermissionService.hasPermission(this.hasPermission)) {
            this._viewContainerRef.createEmbeddedView(this._templateRef);
        } else {
            this._viewContainerRef.clear();
        }
    }
}
