import {Directive, EventEmitter, HostListener, Input, Output} from '@angular/core';
import {BsModalService} from 'ngx-bootstrap/modal';
import {GuardedClickDialogComponent} from '../../core/guarded-click-dialog/guarded-click-dialog.component';
import {GuardedClickDto} from '../../core/guarded-click-dialog/guarded-click-dto';

@Directive({
    selector: '[conditionalClick]',
    standalone: false
})
export class GuardedClickDirective {
    @Input() message: string;
    @Input() condition: boolean;
    @Output() conditionalClick = new EventEmitter();

    @HostListener('click') onClick() {
        if (this.condition) {
            this.conditionalClick.emit();
            return;
        }

        this.modalService.show(GuardedClickDialogComponent, {
            initialState: new GuardedClickDto(this.message)
        })
    }

    constructor(private modalService: BsModalService) {}
}
