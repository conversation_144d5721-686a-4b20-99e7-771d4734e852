import {Directive, EventEmitter, HostBinding, HostListener, Output} from '@angular/core';

@Directive({
    selector: '[appDragDrop]',
    standalone: false
})
export class DragDropDirective {
    @Output() fileDropped = new EventEmitter<FileList>();

    @HostBinding('style.background-color') private backgroundColor = 'rgba(0,0,0,0.05)';
    @HostBinding('style.opacity') private opacity = '1';

    @HostListener('dragover', ['$event'])
    private onDragover(event: MouseEvent): void {
        event.preventDefault();
        event.stopPropagation();

        this.backgroundColor = '#f6fbfe';
        this.opacity = '0.8';
    }

    @HostListener('drop', ['$event'])
    private onDrop(event: MouseEvent): void {
        event.preventDefault();
        event.stopPropagation();

        this.backgroundColor = 'rgba(40, 167, 69, 0.08)';
        this.opacity = '1';

        const files = (event as any).dataTransfer.files;
        if (files.length > 0) {
            this.fileDropped.emit(files);
        }
    }
}
