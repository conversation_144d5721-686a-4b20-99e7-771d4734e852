import {Injectable} from '@angular/core';
import {Permission} from '@models/permission';

@Injectable({
    providedIn: 'root'
})
export class HasPermissionService {
    static permissions: Permission[] = [];

    private static _permissions: Permission[];

    constructor() {
        this._loadUserPermissions();
    }

    hasPermission(hasPermission: Pick<Permission, 'uri' | 'method'>[]): boolean {
        if (!hasPermission || hasPermission.length === 0) {
            return false;
        }

        const permissions = [];

        HasPermissionService._permissions
            .forEach(userPermission => {
                const matchedPermission = hasPermission.filter((featurePermission) => {
                    const uriExp = ('^' + userPermission.uri)
                        .replace(/{[^}]+}/g, '[^\/]+')
                        .replace('/**', '(/.+)*') + '$';
                    return new RegExp(uriExp).test(featurePermission.uri) && featurePermission.method === userPermission.method
                });
                permissions.push(...matchedPermission);
            });

        return permissions.length >= hasPermission.length
    }

    private _loadUserPermissions() {
        if (!HasPermissionService._permissions) {
            HasPermissionService._permissions = [];
        }

        const uniquePermissions = new Set<string>();

        HasPermissionService.permissions.forEach(permission => {
            const key = `${permission.method}:${permission.uri}`;
            if (!uniquePermissions.has(key)) {
                uniquePermissions.add(key);
                HasPermissionService._permissions.push(permission);
            }
        });
    }
}
