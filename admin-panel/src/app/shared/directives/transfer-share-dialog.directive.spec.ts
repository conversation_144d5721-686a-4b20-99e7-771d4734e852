import {TransferShareDialogDirective} from './transfer-share-dialog.directive';
import {inject, TestBed} from '@angular/core/testing';
import {BsModalService, ModalModule} from 'ngx-bootstrap/modal';
import {TransferShareDialogComponent} from '../../feature/home/<USER>/transfer-share-dialog/transfer-share-dialog.component';

describe('TransferShareDialogDirective', () => {
    beforeEach(() => {
        TestBed.configureTestingModule({
            declarations: [TransferShareDialogComponent],
            imports: [ModalModule.forRoot()]
        })
    });

    it('should create an instance', inject([BsModalService], (modalService: BsModalService) => {
        const directive = new TransferShareDialogDirective(modalService);
        expect(directive).toBeTruthy();
    }));
});
