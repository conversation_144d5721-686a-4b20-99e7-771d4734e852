import {Directive, ElementRef} from '@angular/core';

@Directive({
    selector: '[password]',
    host: {
        '[type]': 'type'
    },
    standalone: false
})
export class PasswordDirective {
    type = 'password';

  constructor(private elementRef: ElementRef) {
      this.appendToggleShowPasswordBtn();
  }

  private appendToggleShowPasswordBtn(): void {
      const btn = document.createElement('div');
      const icon = document.createElement('i');

      icon.classList.add('icon');
      icon.classList.add('icon-eye-slash');
      btn.classList.add('toggle-show-password');
      btn.onclick = () => {
          if (this.type === 'password') {
              this.type = 'text';
              icon.classList.add('icon-eye');
              icon.classList.remove('icon-eye-slash');
          } else {
              this.type = 'password';
              icon.classList.remove('icon-eye');
              icon.classList.add('icon-eye-slash');
          }
      };
      btn.appendChild(icon);

      this.elementRef.nativeElement.after(btn);
  }
}
