import {ChangeDetectionStrategy, Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {UserManagementDataService} from '@dataServices/user-management-data.service';
import {UserRole} from '@models/user-role';
import {Page} from '@models/page';
import {TranslateKey} from '@enums/translate-key';
import {BsModalService} from 'ngx-bootstrap/modal';
import {UserRoleDialogComponent} from './user-role-dialog/user-role-dialog.component';
import {UserRoleDialog} from '@models/user-role-dialog';
import {MatPaginator} from '@angular/material/paginator';
import {map, takeUntil} from 'rxjs/operators';
import {PaginatedDataTable} from '@models/paginated-data-table';
import {UserRolesManagementDataTableColumns} from './user-roles-management-data-table-columns';
import {Observable} from 'rxjs';
import {Permissions} from '../../../../shared/constants/permissions.constant';

@Component({
    selector: 'app-user-roles-management',
    templateUrl: './user-roles-management.page.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class UserRolesManagementPage extends PaginatedDataTable<UserRole> implements OnInit {
    readonly translateKeys = TranslateKey;

    readonly permissions = Permissions;

    readonly columns = UserRolesManagementDataTableColumns();

    @ViewChild('tableContainer')
    _tableContainer: ElementRef<HTMLDivElement>;

    @ViewChild(MatPaginator)
    _paginator: MatPaginator;

    constructor(
        private _userManagementDataService: UserManagementDataService,
        private _modalService: BsModalService
    ) {
        super();

        this.filterProperty.actionBtns.unshift(...[
            {
                title: TranslateKey.addUserRole,
                icon: 'assignment_ind',
                callback: this._openAddRoleDialog.bind(this),
                permissions: this.permissions.CREATE_ROLE_PERMISSION
            }
        ]);
        this.filterProperty.refreshPage = this._fetchPageData.bind(this);
        this.filterProperty.callback = this._filterPageData.bind(this);
    }

    ngOnInit(): void {
        this._modalService.onHide
            .pipe(takeUntil(this._onDestroy))
            .subscribe(this._fetchPageData.bind(this));
    }

    removeRole(userRole: UserRole): void {
        this._userManagementDataService
            .removeUserRole(userRole.name)
            .subscribe();
    }

    openEditUserRoleDialog(userRole: UserRole) {
        this._modalService.show(UserRoleDialogComponent, {
            initialState: new UserRoleDialog(userRole)
        });
    }

    override fetchExcelData(): Observable<UserRole[]> {
        return this._userManagementDataService
            .getRoles(this.getFilterQueryParams())
            .pipe(map(resp => resp.content));
    }

    override _fetchPageData(): void {
        this._calcPageSize();
        this._pageData = new Page<UserRole>();
        this.isPageDataLoaded.set(false);

        this._userManagementDataService
            .getRoles(this.getFilterQueryParams())
            .subscribe(resp => {
                this._pageData = resp;
                this.isPageDataLoaded.set(true);
                this._changeDetectorRef.detectChanges();
            });
    }

    private _openAddRoleDialog(): void {
        this._modalService.show(UserRoleDialogComponent);
    }
}
