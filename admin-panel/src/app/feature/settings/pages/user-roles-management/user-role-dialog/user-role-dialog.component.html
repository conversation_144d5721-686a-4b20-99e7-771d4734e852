<app-modal-header>{{modalTitle | translate}}</app-modal-header>

<!-- Body -->
<div class="modal-body">
    <form [formGroup]="formGroup" class="form-row">
        <!-- Username -->
        <div class="form-group col-12">
            <label class="required" for="name">{{ translateKeys.name | translate }}</label>
            <input
                [placeholder]="translateKeys.name | translate"
                class="form-control"
                id="name"
                formControlName="name"
                required>
        </div>

        <!-- Password -->
        <div class="form-group col-12">
            <label class="required" for="description">{{ translateKeys.description | translate }}</label>
            <input
                [placeholder]="translateKeys.description | translate"
                class="form-control"
                id="description"
                formControlName="description"
                required>
        </div>

        <!-- Roles -->
        <div class="form-group col-12">
            <label>{{translateKeys.permissions | translate}}</label>

            <app-multi-picklist
                [list]="permissionsList"
                [searchField]="undefined"
                formControlName="permissions"></app-multi-picklist>
        </div>
    </form>
</div>

<!-- Footer -->
<div class="modal-footer">
    <!-- Submit Button -->
    <button
        type="button"
        mat-flat-button
        color="primary"
        (click)="onSubmit(formGroup)"
        [disabled]="formGroup.invalid">
        {{submitBtnTitle | translate}}
    </button>
    <!-- Cancel Button -->
    <button
        type="button"
        mat-button
        (click)="modalRef.hide()">
        {{translateKeys.cancel | translate}}
    </button>
</div>
