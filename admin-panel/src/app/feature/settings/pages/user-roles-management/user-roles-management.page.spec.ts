import {TestBed} from '@angular/core/testing';
import {UserRolesManagementPage} from './user-roles-management.page';
import {BsModalService} from 'ngx-bootstrap/modal';
import {HttpClient} from '@angular/common/http';
import {UserRoleDialogComponent} from './user-role-dialog/user-role-dialog.component';
import {Http} from '@http';
import {RouterTestingModule} from '@angular/router/testing';
import {MultiPicklistComponent} from '@modules/multi-pick/multi-picklist/multi-picklist.component';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {TestUtils} from '@test/test-utils';
import {UserManagementDataService} from '@dataServices/user-management-data.service';
import {of} from 'rxjs';
import {UserRoleDialog} from '@models/user-role-dialog';
import {PageTemplateModule} from '@modules/page-template/page-template.module';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {SharedModule} from '@shared/shared.module';
import {DataTableFunctionality} from '@test/data-table-functionality';
import {ComponentHarness} from '@test/harness/component-harness';
import {DynamicPipe} from '@pipes/dynamic.pipe';
import {HasPermissionService} from '@directives/has-permission.service';
import {hasPermissionServiceHarness} from '@directives/has-permission.service.harness';
import {HttpClientTestingModule} from '@angular/common/http/testing';

describe('UserRolesManagementPage', () => {
    let ha: ComponentHarness<UserRolesManagementPage>;
    let userManagementDataService: UserManagementDataService;
    let modalService: BsModalService;

    beforeEach(() => {
        ha = new ComponentHarness<UserRolesManagementPage>(UserRolesManagementPage, {
            declarations: [
                UserRolesManagementPage,
                MultiPicklistComponent,
                UserRoleDialogComponent,
                DynamicPipe
            ],
            imports: [
                HttpClientTestingModule,
                BrowserAnimationsModule,
                TranslateTestingModule,
                RouterTestingModule,
                PageTemplateModule,
                SharedModule
            ],
            providers: [
                BsModalService,
                {provide: HasPermissionService, useValue: hasPermissionServiceHarness}
            ],
            detectChanges: false
        });

        Http.httpClient = TestBed.inject(HttpClient);
        modalService = TestBed.inject(BsModalService);
        userManagementDataService = TestBed.inject(UserManagementDataService);
        spyOn(userManagementDataService, 'getRoles').and.returnValue(of(TestUtils.getRoles()));
        spyOn(userManagementDataService, 'removeUserRole').and.returnValue(of(null));

        ha.detectChanges();
    });

    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });

    it('should open related dialog-box by clicking on "Add User Role" Button', () => {
        spyOn(modalService, 'show');

        ha.get('button').thatContains('Add User Role').click();

        expect(modalService.show).toHaveBeenCalledWith(UserRoleDialogComponent);
    });

    it('should open related dialog-box by clicking on "Edit User Role" Button', () => {
        spyOn(modalService, 'show');

        ha.get('button').thatContains('more_vert').click();
        ha.get('button').thatContains('Edit').click();

        expect(modalService.show).toHaveBeenCalledWith(UserRoleDialogComponent, jasmine.objectContaining({
            initialState: jasmine.any(UserRoleDialog)
        }));
    });

    it('should delete role when clicking "Delete" button', () => {
        ha.get('button').thatContains('more_vert').click();
        ha.get('button').thatContains('Delete').confirm();

        expect(userManagementDataService.removeUserRole).toHaveBeenCalled();
    });

    describe('DataTable functionality', () => {
        it('when the total number of rows exceeds the page size, pagination should be displayed', () => {
            DataTableFunctionality.when_the_total_number_of_rows_exceeds_the_page_size_pagination_should_be_displayed(ha);
        });

        it('pagination should be hidden when the total number of rows is less than the page size', () => {
            DataTableFunctionality.pagination_should_be_hidden_when_the_total_number_of_rows_is_less_than_the_page_size(ha);
        });
    })
});
