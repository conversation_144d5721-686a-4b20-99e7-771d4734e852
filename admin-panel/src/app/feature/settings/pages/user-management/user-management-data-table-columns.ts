import {DataTableColumn} from '@models/data-table';
import {TranslateKey} from '@enums/translate-key';
import {TranslatePipe} from '@ngx-translate/core';
import {User} from '@models/user';
import {SelectOptionFilter} from '@modules/filter/select-option/select-option-filter';
import {InputFilter} from '@modules/filter/input/input-filter';

export function UserManagementDataTableColumns(): DataTableColumn<User>[] {
    return [
        {
            title: TranslateKey.username,
            isSticky: true,
            value(data) { return data.username },
            filter: new InputFilter<string>({
                queryParam: 'username'
            })
        },
        {
            title: TranslateKey.roles,
            value(data) { return data.roles },
            filter: new InputFilter<string>({
                queryParam: 'roles'
            })
        },
        {
            title: TranslateKey.blocked,
            pipeToken: TranslatePipe,
            value(data) { return data.blocked.toString() },
            filter: new SelectOptionFilter({
                data: [
                    {key: true, value: TranslateKey.yes},
                    {key: false, value: TranslateKey.no}
                ],
                queryParam: 'blocked',
                queryParamValueField: 'key'
            })
        }
    ];
}
