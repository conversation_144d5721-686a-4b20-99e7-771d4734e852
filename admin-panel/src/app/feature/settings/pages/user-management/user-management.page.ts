import {ChangeDetectionStrategy, Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {UserManagementDataService} from '@dataServices/user-management-data.service';
import {TranslateKey} from '@enums/translate-key';
import {Page} from '@models/page';
import {User} from '@models/user';
import {BsModalService} from 'ngx-bootstrap/modal';
import {UserDialogComponent} from './user-dialog/user-dialog.component';
import {UserDialog} from '@models/user-dialog';
import {MatPaginator} from '@angular/material/paginator';
import {map, takeUntil} from 'rxjs/operators';
import {PaginatedDataTable} from '@models/paginated-data-table';
import {UserManagementDataTableColumns} from './user-management-data-table-columns';
import {Observable} from 'rxjs';
import {Permissions} from '../../../../shared/constants/permissions.constant';

@Component({
    selector: 'app-user-management',
    templateUrl: './user-management.page.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class UserManagementPage extends PaginatedDataTable<User> implements OnInit {
    readonly translateKeys = TranslateKey;

    readonly permissions = Permissions;

    readonly columns = UserManagementDataTableColumns();

    @ViewChild('tableContainer')
    _tableContainer: ElementRef<HTMLDivElement>;

    @ViewChild(MatPaginator)
    _paginator: MatPaginator;

    constructor(
        private _userManagementDataService: UserManagementDataService,
        private _modalService: BsModalService
    ) {
        super();

        this.filterProperty.actionBtns.unshift(...[
            {
                title: TranslateKey.addUser,
                icon: 'person_add',
                callback: this._openAddUserDialog.bind(this),
                permissions: this.permissions.CREATE_USER_PERMISSION
            }
        ]);
        this.filterProperty.refreshPage = this._fetchPageData.bind(this);
        this.filterProperty.callback = this._filterPageData.bind(this);
    }

    ngOnInit(): void {
        this._modalService.onHide
            .pipe(takeUntil(this._onDestroy))
            .subscribe(this._fetchPageData.bind(this));
    }

    openEditUserDialog(user: User): void {
        this._modalService.show(UserDialogComponent, {
            initialState: new UserDialog(user)
        });
    }

    addDevice(user: User): void {
        this._userManagementDataService.addDevice(user.username).subscribe();
    }

    override fetchExcelData(): Observable<User[]> {
        return this._userManagementDataService
            .getUsers(this.getFilterQueryParams())
            .pipe(map(resp => resp.content));
    }

    override _fetchPageData(): void {
        this._calcPageSize();
        this._pageData = new Page<User>();
        this.isPageDataLoaded.set(false);

        this._userManagementDataService
            .getUsers(this.getFilterQueryParams())
            .subscribe(resp => {
                this._pageData = resp;
                this.isPageDataLoaded.set(true);
                this._changeDetectorRef.detectChanges();
            })
    }

    private _openAddUserDialog(): void {
        this._modalService.show(UserDialogComponent);
    }
}
