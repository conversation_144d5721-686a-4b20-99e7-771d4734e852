<app-page-panel [filterProperty]="filterProperty">
    <ng-container *breadcrumb>{{translateKeys.userManagement | translate}}</ng-container>

    <app-panel-content>
        <div class="w-100 h-100 position-absolute" #tableContainer>
            <table mat-table [dataSource]="dataSource" class="w-100">
                @for (column of columns; track column) {
                    <ng-container [matColumnDef]="column.title" [sticky]="column.isSticky">
                        <th mat-header-cell *matHeaderCellDef [style.min-width]="column.minWidth + 'px'">{{ column.title | translate }}</th>
                        <td mat-cell *matCellDef="let row" [class]="column.class">{{ column.value(row) | dynamicPipe:column.pipeToken:column.pipeArgs }}</td>
                    </ng-container>
                }

                <!-- Actions -->
                <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef width="40"></th>
                    <td mat-cell *matCellDef="let row">
                        <button
                            mat-icon-button
                            [matMenuTriggerFor]="menu">
                            <mat-icon>more_vert</mat-icon>
                        </button>

                        <mat-menu #menu="matMenu">
                            <button
                                *hasPermission="permissions.UPDATE_USER_PERMISSION"
                                mat-menu-item
                                (click)="openEditUserDialog(row)">
                                <mat-icon>edit</mat-icon>
                                <span>{{translateKeys.edit | translate}}</span>
                            </button>
                            <button
                                *hasPermission="permissions.REGISTER_NEW_DEVICE_PERMISSION"
                                mat-menu-item
                                (click)="addDevice(row)">
                                <mat-icon>security_key</mat-icon>
                                <span>{{translateKeys.addDevice | translate}}</span>
                            </button>
<!--                            <button-->
<!--                                mat-menu-item-->
<!--                                (confirm)="removeUser(row)"-->
<!--                                [disabled]="isCurrentUser(row)">-->
<!--                                <mat-icon>delete</mat-icon>-->
<!--                                <span>{{translateKeys.delete | translate}}</span>-->
<!--                            </button>-->
                        </mat-menu>
                    </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="getDataTableColumns(); sticky: true"></tr>
                <tr mat-row *matRowDef="let row; columns: getDataTableColumns()"></tr>
                <tr class="mat-row" *matNoDataRow>
                    <td class="mat-cell" [attr.colspan]="getDataTableColumns().length">{{noDataRowText | translate}}</td>
                </tr>
            </table>

            <mat-paginator
                *paginator
                [ngClass]="paginatorClass"
                [length]="dataSourceLength"
                [pageSize]="dataSourcePageSize"
                [showFirstLastButtons]="true">
            </mat-paginator>
        </div>
    </app-panel-content>
</app-page-panel>
