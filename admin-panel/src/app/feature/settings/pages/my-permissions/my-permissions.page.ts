import {ChangeDetectionStrategy, Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {Page} from '@models/page';
import {BsModalService} from 'ngx-bootstrap/modal';
import {Permission} from '@models/permission';
import {MatPaginator} from '@angular/material/paginator';
import {takeUntil} from 'rxjs/operators';
import {animate, state, style, transition, trigger} from '@angular/animations';
import {PaginatedDataTable} from '@models/paginated-data-table';
import {MyPermissionsDataTableColumns} from './my-permissions-data-table-columns';
import {UtilConstants} from '@constants/util-constants';

@Component({
    selector: 'my-permissions.component',
    templateUrl: './my-permissions.page.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    animations: [
        trigger('detailExpand', [
            state('collapsed', style({height: '0px', minHeight: '0'})),
            state('expanded', style({height: '*'})),
            transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
        ]),
    ],
    standalone: false
})
export class MyPermissionsPage extends PaginatedDataTable<Permission> implements OnInit {
    readonly translateKeys = TranslateKey;

    readonly columns = MyPermissionsDataTableColumns();

    private _expandedRow: any | null;
    get expandedRow() { return this._expandedRow; }

    set expandedRow(row: any | null) { this._expandedRow = row; }

    @ViewChild('tableContainer')
    _tableContainer: ElementRef<HTMLDivElement>;

    @ViewChild(MatPaginator)
    _paginator: MatPaginator;

    constructor(
        private _modalService: BsModalService
    ) {
        super();
    }

    ngOnInit(): void {
        this._modalService.onHide
            .pipe(takeUntil(this._onDestroy))
            .subscribe(() => this.refreshPage());
    }

    refreshPage(): void {
        this._fetchPageData();
    }

    override getDataTableColumns(): string[] {
        return this.columns.map(column => column.title);
    }

    override _fetchPageData(): void {
        this._pageData = new Page<Permission>([]);

        const localUserInfo = localStorage.getItem(UtilConstants.USER_INFO_KEY);
        if (!localUserInfo) { return; }

        JSON.parse(localUserInfo)
            .roles.forEach(role => {
            role.permissions.forEach(permission => {
                permission.role = {
                    description: role.description,
                    name: role.name
                }

                this._pageData.content.push(permission);
            });
        });

        this._changeDetectorRef.detectChanges();
    }
}
