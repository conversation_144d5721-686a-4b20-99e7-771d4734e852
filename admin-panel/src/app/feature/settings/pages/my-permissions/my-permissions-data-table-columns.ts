import {DataTableColumn} from '@models/data-table';
import {TranslateKey} from '@enums/translate-key';
import {Permission} from '@models/permission';
import {InputFilter} from '@modules/filter/input/input-filter';

export function MyPermissionsDataTableColumns(): DataTableColumn<Permission>[] {
    return [
        {
            title: TranslateKey.name,
            isSticky: true,
            value(data) { return data.name },
            filter: new InputFilter<string>({
                queryParam: 'name'
            })
        },
        {
            title: TranslateKey.description,
            value(data) { return data.description }
        },
        {
            title: TranslateKey.method,
            value(data) { return data.method }
        },
        {
            title: TranslateKey.url,
            value(data) { return data.uri }
        }
    ];
}
