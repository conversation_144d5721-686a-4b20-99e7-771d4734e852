import {Component, OnInit} from '@angular/core';
import {UserPermissionDialog} from '@models/user-permission-dialog';
import {Permission} from '@models/permission';
import {TranslateKey} from '@enums/translate-key';
import {UntypedFormBuilder, UntypedFormGroup, Validators} from '@angular/forms';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {Observable} from 'rxjs';
import {PermissionDataService} from '@dataServices/permission-data.service';

@Component({
    selector: 'app-user-permission-dialog',
    templateUrl: './user-permission-dialog.component.html',
    standalone: false
})
export class UserPermissionDialogComponent implements OnInit, UserPermissionDialog {
    readonly translateKeys = TranslateKey;
    formGroup: UntypedFormGroup;
    permission = new Permission();
    methodsLayout = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];

    private permissionsName: string;

    constructor(
        public modalRef: BsModalRef,
        private _formBuilder: UntypedFormBuilder,
        private _permissionDataService: PermissionDataService
    ) { }

    ngOnInit(): void {
        this.setupFormGroup();
        this.permissionsName = this.permission.name;
    }

    onSubmit({value, valid}: { value: Permission, valid: boolean }): void {
        if (!valid) { return }

        (this.permission.name
            ? this.editPermission.bind(this)
            : this.addPermission.bind(this))(value)
            .subscribe(this.modalRef.hide);
    }

    addPermission(permission: Permission): Observable<any> {
        return this._permissionDataService
            .addPermission(permission);
    }

    editPermission(permission: Permission): Observable<any> {
        return this._permissionDataService
            .editPermission(this.permissionsName, permission);
    }

    private setupFormGroup(): void {
        this.formGroup = this._formBuilder.group({
            name: [{value: this.permission.name, disabled: this.permission.name}, Validators.required],
            description: this.permission.description,
            method: [this.permission.method, Validators.required],
            uri: [this.permission.uri, Validators.required]
        });
    }

    get submitBtnTitle(): string {
        return this.permission.name ? TranslateKey.edit : TranslateKey.submit;
    }

    get modalTitle(): string {
        return this.permission.name ? TranslateKey.editPermission : TranslateKey.addPermission;
    }

}
