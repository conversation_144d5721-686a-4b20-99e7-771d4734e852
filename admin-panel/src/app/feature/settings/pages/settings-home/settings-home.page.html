<div class="row justify-content-center">
    <div class="col-md-7 col-m-12 mt-5">
        <h5>{{translateKeys.settings | translate}}</h5>

        <div class="card shadow-sm rounded-lg mt-3">
            <ul class="list-group list-group-flush">
                <li
                    *hasPermission="permissions.USERS_PERMISSION"
                    class="list-group-item d-flex justify-content-between align-items-center cursor-pointer"
                    [routerLink]="routesLayout.USER_MANAGEMENT">
                    <section>
                        <span>{{translateKeys.userManagement | translate}}: </span>
                        <small class="text-muted">{{translateKeys.userManagementDesc | translate}}</small>
                    </section>

                    <section>
                        <button mat-icon-button>
                            <mat-icon>keyboard_arrow_left</mat-icon>
                        </button>
                    </section>
                </li>

                <li
                    *hasPermission="permissions.ROLES_MANAGEMENT_PERMISSION"
                    class="list-group-item d-flex justify-content-between align-items-center cursor-pointer"
                    [routerLink]="routesLayout.USER_ROLES_MANAGEMENT">
                    <section>
                        <span>{{translateKeys.userRolesManagement | translate}}: </span>
                        <small class="text-muted">{{translateKeys.userRolesManagementDesc | translate}}</small>
                    </section>

                    <section>
                        <button mat-icon-button>
                            <mat-icon>keyboard_arrow_left</mat-icon>
                        </button>
                    </section>
                </li>

                <li
                    *hasPermission="permissions.PERMISSIONS_PERMISSION"
                    class="list-group-item d-flex justify-content-between align-items-center cursor-pointer"
                    [routerLink]="routesLayout.USER_PERMISSIONS_MANAGEMENT">
                    <section>
                        <span>{{translateKeys.permissionManagement | translate}}: </span>
                        <small class="text-muted">{{translateKeys.permissionManagementDesc | translate}}</small>
                    </section>

                    <section>
                        <button mat-icon-button>
                            <mat-icon>keyboard_arrow_left</mat-icon>
                        </button>
                    </section>
                </li>

                <li
                    class="list-group-item d-flex justify-content-between align-items-center cursor-pointer"
                    [routerLink]="routesLayout.MY_PERMISSIONS">
                    <section>
                        <span>{{translateKeys.myPermissions | translate}}: </span>
                        <small class="text-muted">{{translateKeys.myPermissionsDesc | translate}}</small>
                    </section>

                    <section>
                        <button mat-icon-button>
                            <mat-icon>keyboard_arrow_left</mat-icon>
                        </button>
                    </section>
                </li>

                <li
                    *hasPermission="permissions.TOGGLE_SYSTEM_ACTIVATION_PERMISSION"
                    class="list-group-item d-flex justify-content-between align-items-center cursor-pointer"
                    (click)="openToggleActiveSystemStateDialog()">
                    <section>
                        <span>{{translateKeys.systemState | translate}}: </span>
                        <small class="text-muted">{{translateKeys.toggleActiveSystemState | translate}}</small>
                    </section>

                    <section>
                        <button mat-icon-button>
                            <mat-icon>keyboard_arrow_left</mat-icon>
                        </button>
                    </section>
                </li>

                <!-- Disable Credit Checking for System -->
                <li
                    *hasPermission="permissions.CHANGE_CREDIT_CHECKING_PERMISSION"
                    class="list-group-item d-flex justify-content-between align-items-center cursor-pointer"
                    (click)="getAndOpenCreditChecking()">
                    <section>
                        <span>{{translateKeys.creditCheckingStatus | translate}}: </span>
                        <small class="text-muted">{{translateKeys.updateCreditCheckingStatus | translate}}</small>
                    </section>

                    <section>
                        <button mat-icon-button>
                            <mat-icon>keyboard_arrow_left</mat-icon>
                        </button>
                    </section>
                </li>
            </ul>
        </div>
    </div>
</div>

