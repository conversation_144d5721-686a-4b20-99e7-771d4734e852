import {NgModule} from '@angular/core';
import {SettingsPage} from './settings.page';
import {SettingsRoutingModule} from './settings-routing.module';
import {UserRolesManagementPage} from './pages/user-roles-management/user-roles-management.page';
import {UserRoleDialogComponent} from './pages/user-roles-management/user-role-dialog/user-role-dialog.component';
import {UserPermissionManagementPage} from './pages/user-permission-management/user-permission-management.page';
import {
    UserPermissionDialogComponent
} from './pages/user-permission-management/user-permission-dialog/user-permission-dialog.component';
import {UserManagementPage} from './pages/user-management/user-management.page';
import {UserDialogComponent} from './pages/user-management/user-dialog/user-dialog.component';
import {PageTemplateModule} from '@modules/page-template/page-template.module';
import {MultiPickModule} from '@modules/multi-pick/multi-pick.module';
import {SettingsHomePage} from './pages/settings-home/settings-home.page';
import {SharedDeclarations} from '@modules/shared-declarations/shared-declarations';
import {SharedModule} from '../../shared/shared.module';
import {
    ToggleSystemStateDialogComponent
} from './pages/settings-home/toggle-system-state-dialog/toggle-system-state-dialog.component';
import {MyPermissionsPage} from './pages/my-permissions/my-permissions.page';
import {PageLayoutModule} from '@layouts/main-layout/page-layout.module';

@NgModule({
    declarations: [
        SettingsPage,
        UserRolesManagementPage,
        UserRoleDialogComponent,
        UserPermissionManagementPage,
        UserPermissionDialogComponent,
        UserManagementPage,
        UserDialogComponent,
        SettingsHomePage,
        ToggleSystemStateDialogComponent,
        MyPermissionsPage
    ],
    imports: [
        SettingsRoutingModule,
        PageTemplateModule,
        MultiPickModule,
        PageLayoutModule,
        SharedDeclarations,
        SharedModule
    ],
    providers: []
})
export class SettingsModule {}
