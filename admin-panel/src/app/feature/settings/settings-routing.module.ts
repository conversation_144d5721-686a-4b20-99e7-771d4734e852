import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {SettingsPage} from './settings.page';
import {UserManagementPage} from './pages/user-management/user-management.page';
import {UserRolesManagementPage} from './pages/user-roles-management/user-roles-management.page';
import {UserPermissionManagementPage} from './pages/user-permission-management/user-permission-management.page';
import {SettingsHomePage} from './pages/settings-home/settings-home.page';
import {RoutingLayout} from '@constants/routing-layout';
import {MyPermissionsPage} from './pages/my-permissions/my-permissions.page';


const routes: Routes = [
    {
        path: '',
        component: SettingsPage,
        children: [
            {
                path: '',
                component: SettingsHomePage
            },
            {
                path: RoutingLayout.USER_MANAGEMENT,
                component: UserManagementPage
            },
            {
                path: RoutingLayout.USER_ROLES_MANAGEMENT,
                component: UserRolesManagementPage
            },
            {
                path: RoutingLayout.USER_PERMISSIONS_MANAGEMENT,
                component: UserPermissionManagementPage
            },
            {
                path: RoutingLayout.MY_PERMISSIONS,
                component: MyPermissionsPage
            }
        ]
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class SettingsRoutingModule {}
