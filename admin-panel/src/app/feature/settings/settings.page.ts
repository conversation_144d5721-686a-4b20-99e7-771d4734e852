import {Component} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {Router} from '@angular/router';
import {RoutingLayout} from '@constants/routing-layout';
import {Sidebar} from '@layouts/main-layout/sidebar/sidebar';
import {Permissions} from '../../shared/constants/permissions.constant';

@Component({
    selector: 'app-settings',
    templateUrl: './settings.page.html',
    standalone: false
})
export class SettingsPage {
    readonly translateKeys = TranslateKey;

    private _sidebar: Sidebar = {
        items: [
            {
                title: TranslateKey.userManagement,
                icon: 'people_outline',
                submenu: [
                    {
                        title: TranslateKey.userManagement,
                        onClick: () => this._navigateRoute(RoutingLayout.USER_MANAGEMENT),
                        permissions: Permissions.USERS_PERMISSION
                    },
                    {
                        title: TranslateKey.userRolesManagement,
                        onClick: () => this._navigateRoute(RoutingLayout.USER_ROLES_MANAGEMENT),
                        permissions: Permissions.ROLES_MANAGEMENT_PERMISSION
                    },
                    {
                        title: TranslateKey.permissionManagement,
                        onClick: () => this._navigateRoute(RoutingLayout.USER_PERMISSIONS_MANAGEMENT),
                        permissions: Permissions.PERMISSIONS_PERMISSION
                    },
                    {
                        title: TranslateKey.myPermissions,
                        onClick: () => this._navigateRoute(RoutingLayout.MY_PERMISSIONS)
                    }
                ]
            }
        ],
        footerItems: [
            {
                title: TranslateKey.home,
                icon: 'home',
                onClick: this._navigateToHome.bind(this)
            }
        ]
    }
    get sidebar(): Sidebar {
        return this._sidebar;
    }

    constructor(private _router: Router) {}

    private _navigateRoute(route: string): void {
        this._router
            .navigate([RoutingLayout.SETTINGS + '/' + route])
            .catch(console.error);
    }

    private _navigateToHome(): void {
        this._router
            .navigate([RoutingLayout.HOME])
            .catch(console.error);
    }
}
