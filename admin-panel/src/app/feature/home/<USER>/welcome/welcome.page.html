<div class="mt-4 w-100 text-center" style="max-width: 1100px; margin: auto">
    <div class="banner"></div>
    <div class="font-weight-light" style="font-size: 2.5rem; margin-top: -50px; margin-bottom: 20px">RADIN TRADING
        PLATFORM
    </div>
</div>

<div class="d-flex flex-column flex-center w-100">
    <div style="height: 56px;">
        <div class="search-input-container"
             [class.active]="isOpenSearchBox">

            <mat-icon>search</mat-icon>
            <input
                type="text"
                (focus)="openSearchBox()"
                [placeholder]="translateKeys.search | translate">
        </div>
    </div>
    <div class="w-100 justify-content-center mt-3 pt-3 bg-white pb-2 d-flex">
        <a *hasPermission="permissions.SCHEDULE_TEMPLATE_PERMISSION"
           class="main-card"
           [routerLink]="associatedScheduleRoute">
            <mat-icon>calendar_month</mat-icon>
            <small>{{ translateKeys.scheduleTemplates | translate }}</small>
        </a>

        <a
            *hasPermission="permissions.GROUPS_PERMISSION"
            class="main-card"
            [routerLink]="groupsRoute">
            <mat-icon>folder</mat-icon>
            <small>{{ translateKeys.groups | translate }}</small>
        </a>

        <a
            *hasPermission="permissions.PRODUCTS_PERMISSION"
            class="main-card"
            [routerLink]="productsRoute">
            <mat-icon>shopping_bag</mat-icon>
            <small>{{ translateKeys.products | translate }}</small>
        </a>

        <a
            *hasPermission="permissions.QUEUED_COMMANDS_PERMISSION"
            class="main-card"
            [routerLink]="commandQueueRoute">
            <mat-icon>terminal</mat-icon>
            <small>{{ translateKeys.commandQueue | translate }}</small>
        </a>
    </div>
</div>
