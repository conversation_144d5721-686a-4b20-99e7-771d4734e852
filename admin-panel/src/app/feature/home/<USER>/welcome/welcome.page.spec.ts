import {WelcomePage} from './welcome.page';
import {NO_ERRORS_SCHEMA} from '@angular/core';
import {SharedModule} from '../../../../shared/shared.module';
import {RouterTestingModule} from '@angular/router/testing';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {ComponentHarness} from '@test/harness/component-harness';
import {HttpClientTestingModule} from '@angular/common/http/testing';

describe('WelcomePage', () => {
    let ha: ComponentHarness<WelcomePage>;

    beforeEach(() => {
        ha = new ComponentHarness(WelcomePage, {
            declarations: [],
            imports: [
                HttpClientTestingModule,
                TranslateTestingModule,
                RouterTestingModule,
                SharedModule,
                WelcomePage
            ],
            schemas: [NO_ERRORS_SCHEMA],
            detectChanges: false
        })
    });

    it('should create', () => {
        ha.detectChanges();
        expect(ha.component).toBeTruthy();
    });
});
