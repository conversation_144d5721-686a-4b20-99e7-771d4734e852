import {PrepareToShutDownWarningComponent} from './prepare-to-shut-down-warning.component';
import {ComponentHarness} from '@test/harness/component-harness';
import {BsModalRef, BsModalService} from 'ngx-bootstrap/modal';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {SharedModule} from '../../../../shared/shared.module';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {SystemStateDataService} from '@dataServices/system-state-data.service';
import {systemStateDataServiceHarness} from '@dataServices/system-state-data.service.harness';
import {of} from 'rxjs';
import {TestUtils} from '@test/test-utils';

describe('PrepareToShutDownWarningComponent', () => {
    let ha: ComponentHarness<PrepareToShutDownWarningComponent>;

    beforeEach(() => {
        ha = new ComponentHarness(PrepareToShutDownWarningComponent, {
            declarations: [ PrepareToShutDownWarningComponent ],
            imports: [
                TranslateTestingModule,
                SharedModule,
                BrowserAnimationsModule
            ],
            providers: [
                BsModalRef,
                BsModalService,
                {provide: SystemStateDataService, useValue: systemStateDataServiceHarness}
            ],
            detectChanges: false
        });
    });

    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });

    it('should call #back function when confirm button is clicked', () => {
        spyOn(systemStateDataServiceHarness, 'updateSystemState').and.returnValue(of(TestUtils.getPostSessionCommand()));
        ha.detectChanges();

        ha.get('button').thatContains('Confirm').confirm();
        expect(systemStateDataServiceHarness.updateSystemState).toHaveBeenCalled();
    });
});
