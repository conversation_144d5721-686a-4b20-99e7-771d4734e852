import {<PERSON><PERSON><PERSON><PERSON>} from '@enums/translate-key';
import {Valida<PERSON>} from '@angular/forms';
import {InputMasks} from '@constants/input-masks';
import {ValidityQualifierType} from '@enums/validity-qualifier-type';
import {OrderType} from '@enums/order-type';
import {CustomValidators} from '@constants/custom-validators';
import {StoreService} from '@services/store.service';
import {FormFieldSets} from './form-field-sets';
import {Order} from '@models/order/order';
import {OrderBook} from '@models/order/order-book';
import {Town} from '@models/town';
import {Trader} from '@models/trader';
import {FieldsPlaceholder} from '../../../../shared/constants/fields-placeholder';
import {ButtonToggleFormField} from '@modules/dynamic-form/form-fields/button-toggle-field/button-toggle-form-field';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';
import {ReadOnlyFormField} from '@modules/dynamic-form/form-fields/read-only-field/read-only-form-field';
import {AutoCompleteFormField} from '@modules/dynamic-form/form-fields/auto-complete-field/auto-complete-form-field';
import {DatePickerFormField} from '@modules/dynamic-form/form-fields/date-picker-field/date-picker-form-field';
import {SelectOptionFormField} from '@modules/dynamic-form/form-fields/select-option-field/select-option-form-field';
import {CheckboxFormField} from '@modules/dynamic-form/form-fields/checkbox-field/checkbox-form-field';
import {LangService} from '@shared/services/lang.service';

export function setUpInsertOrderFormFields(order: OrderBook) {
    const disable: boolean = !!order;
    return [
        new ButtonToggleFormField({
            formControlName: 'side',
            width: 12,
            validations: [Validators.required],
            value: order?.side || Order.Side.BUY,
            options: [Order.Side.BUY, Order.Side.SELL],
            disable,
            hasRemoveBtn: false
        }),

        new CheckboxFormField({
            formControlName: 'isPreOpeningOrder',
            label: TranslateKey.isPreOpeningOrder,
            width: 12,
            style: {paddingBottom: '20px'},
            value: false
        }),

        new InputFormField({
            formControlName: 'quantity',
            label: TranslateKey.quantity,
            width: 4,
            value: order?.quantity,
            validations: [Validators.required, CustomValidators.greaterThan(0)],
            mask: InputMasks.COMMA_SEPERATED_NUMBER,
            placeholder: FieldsPlaceholder.DECIMAL_NUMBER
        }),

        new InputFormField({
            formControlName: 'price',
            label: TranslateKey.price,
            width: 4,
            value: order?.price ? +order.price : '',
            validations: [Validators.required, CustomValidators.greaterThan(0), Validators.pattern(InputMasks.ORDER_PRICE.pattern)],
            mask: InputMasks.FLOATING_NUMBER,
            placeholder: FieldsPlaceholder.DECIMAL_NUMBER
        }),

        new SelectOptionFormField({
            formControlName: 'validityQualifierType',
            label: TranslateKey.validityQualifierType,
            width: 4,
            validations: [Validators.required],
            value: order?.validityQualifierType || ValidityQualifierType.GOOD_TILL_CANCEL,
            options: Object.keys(ValidityQualifierType)
        }),

        new InputFormField({
            formControlName: 'minimumQuantity',
            label: TranslateKey.minimumQuantity,
            width: 4,
            value: order?.minimumQuantity ? +order.minimumQuantity : null,
            validations: [CustomValidators.greaterThan(0)],
            mask: InputMasks.COMMA_SEPERATED_NUMBER,
            placeholder: FieldsPlaceholder.DECIMAL_NUMBER,
            disable
        }),

        new DatePickerFormField({
            formControlName: 'validityDate',
            label: TranslateKey.validityDate,
            width: 4,
            value: order?.validityDate,
            disable: order?.validityQualifierType !== ValidityQualifierType.GOOD_TILL_DATE
        }),

        new SelectOptionFormField({
            formControlName: 'type',
            label: TranslateKey.orderType,
            width: 4,
            validations: [Validators.required],
            value: order?.type || OrderType.LIMIT,
            options: Object.keys(OrderType).filter(type => type !== OrderType.CROSS),
            disable
        }),

        new InputFormField({
            formControlName: 'disclosedQuantity',
            label: TranslateKey.disclosedQuantity,
            width: 4,
            value: order?.disclosedQuantity,
            validations: [CustomValidators.greaterThan(0), CustomValidators.lessThanAnother('quantity')],
            mask: InputMasks.COMMA_SEPERATED_NUMBER,
            disable: true,
            placeholder: FieldsPlaceholder.DECIMAL_NUMBER
        }),

        new AutoCompleteFormField({
            formControlName: 'brokerId',
            label: TranslateKey.broker,
            width: 8,
            value: order?.broker?.id,
            validations: [Validators.required, CustomValidators.fixedLength(3)],
            searchExpField: 'searchExpression',
            options: StoreService.brokers,
            getTitle(option): string { return option ? option.id + ' - ' + option.name.fa : ''; },
            optionFieldName: 'id',
            placeholder: FieldsPlaceholder.BROKER,
            disable
        }),

        new InputFormField({
            formControlName: 'shareholderId',
            label: TranslateKey.shareholderId,
            width: 4,
            value: order?.shareholderId,
            validations: [Validators.required, Validators.maxLength(16)],
            mask: InputMasks.SHAREHOLDER,
            placeholder: FieldsPlaceholder.SHAREHOLDER_ID,
            disable
        }),

        {
            formGroupName: 'clearingData',
            formFields: [
                new AutoCompleteFormField<Trader>({
                    formControlName: 'traderId',
                    label: TranslateKey.trader,
                    width: 4,
                    validations: [Validators.required],
                    value: order?.clearingData?.traderId,
                    fieldSetId: FormFieldSets.CLEARING_DATA,
                    placeholder: FieldsPlaceholder.TRADER_ID,
                    searchExpField: 'traderId',
                    options: StoreService.traders,
                    getTitle(option): string { return option ? option.traderId : ''; },
                    optionFieldName: 'traderId',
                    disable
                }),

                new InputFormField({
                    formControlName: 'traderOrderNumber',
                    label: TranslateKey.traderOrderNumber,
                    width: 4,
                    validations: [Validators.maxLength(8)],
                    value: order?.clearingData?.traderOrderNumber,
                    mask: InputMasks.TRADER,
                    fieldSetId: FormFieldSets.CLEARING_DATA,
                    placeholder: FieldsPlaceholder.TRADER_ORDER_NUMBER,
                    disable
                }),

                new DatePickerFormField({
                    formControlName: 'brokerOrderEntryDateTime',
                    label: TranslateKey.brokerOrderEntryDateTime,
                    width: 4,
                    value: order?.clearingData?.brokerOrderEntryDateTime || StoreService.systemDateTime,
                    fieldSetId: FormFieldSets.CLEARING_DATA,
                    disable
                }),

                new AutoCompleteFormField({
                    formControlName: 'giveUpBrokerId',
                    label: TranslateKey.giveUpBroker,
                    width: 8,
                    value: order?.clearingData?.giveUpBrokerId,
                    searchExpField: 'searchExpression',
                    options: StoreService.brokers,
                    getTitle(option): string { return option ? option.id + ' - ' + option.name.fa : ''; },
                    optionFieldName: 'id',
                    fieldSetId: FormFieldSets.CLEARING_DATA,
                    placeholder: FieldsPlaceholder.BROKER,
                    disable
                }),

                new InputFormField({
                    formControlName: 'freeText',
                    label: TranslateKey.freeText,
                    width: 4,
                    validations: [Validators.maxLength(18)],
                    value: order?.clearingData?.freeText,
                    fieldSetId: FormFieldSets.CLEARING_DATA,
                    placeholder: FieldsPlaceholder.FREE_TEXT,
                    disable
                }),

                {
                    formGroupName: 'brokerBusinessIdentificationCode',
                    formFields: [
                        new InputFormField({
                            formControlName: 'bankCode',
                            label: TranslateKey.bankCode,
                            width: 4,
                            validations: [Validators.maxLength(3)],
                            value: order?.clearingData?.brokerBusinessIdentificationCode?.bankCode,
                            mask: InputMasks.BANK_CODE,
                            fieldSetId: FormFieldSets.CLEARING_DATA,
                            placeholder: FieldsPlaceholder.BANK_CODE,
                            disable
                        }),

                        new AutoCompleteFormField<Town>({
                            formControlName: 'townCode',
                            label: TranslateKey.townCode,
                            width: 4,
                            value: order?.clearingData?.brokerBusinessIdentificationCode?.townCode,
                            fieldSetId: FormFieldSets.CLEARING_DATA,
                            placeholder: FieldsPlaceholder.TOWN_CODE,
                            searchExpField: 'searchExpression',
                            options: [],
                            getTitle(option): string {
                                return option
                                    ? option.code + ' - ' + LangService.getName(option.name)
                                    : '';
                            },
                            optionFieldName: 'code',
                            disable,
                            panelWidth: 'auto'
                        }),

                        new InputFormField({
                            formControlName: 'branchCode',
                            label: TranslateKey.branchCode,
                            width: 4,
                            validations: [Validators.maxLength(3)],
                            value: order?.clearingData?.brokerBusinessIdentificationCode?.branchCode,
                            mask: InputMasks.BRANCH_CODE,
                            fieldSetId: FormFieldSets.CLEARING_DATA,
                            placeholder: FieldsPlaceholder.BRANCH_CODE,
                            disable
                        }),

                        new ReadOnlyFormField({
                            formControlName: 'countryCode',
                            value: 'IR',
                            fieldSetId: FormFieldSets.CLEARING_DATA
                        })
                    ]
                }
            ]
        }
    ];
}
