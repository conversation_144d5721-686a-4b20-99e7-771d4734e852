import {InsertOrderFormService} from './insert-order-form.service';
import {FormControl, FormGroup} from '@angular/forms';
import {OrderType} from '@enums/order-type';
import {ValidityQualifierType} from '@enums/validity-qualifier-type';
import {Subject} from 'rxjs';
import {InsertOrderFormGroup} from './insert-order-form-group';
import {StoreService} from '@services/store.service';

describe('InsertOrderFormService', () => {
    let service: InsertOrderFormService;
    let formGroup: FormGroup<InsertOrderFormGroup>;

    beforeEach(() => {
        service = new InsertOrderFormService();

        formGroup = new FormGroup<InsertOrderFormGroup>({
            brokerId: new FormControl(null),
            isPreOpeningOrder: new FormControl(null),
            shareholderId: new FormControl(null),
            side: new FormControl(null),
            type: new FormControl(null),
            quantity: new FormControl(null),
            minimumQuantity: new FormControl(null),
            disclosedQuantity: new FormControl(null),
            validityQualifierType: new FormControl(null),
            validityDate: new FormControl(null),
            price: new FormControl(null),
            clearingData: new FormGroup({} as any) // stub
        });

        service.setupFormLogic(formGroup, new Subject());
    });

    it('should disable minimumQuantity when validityQualifierType is FILL_AND_KILL', () => {
        formGroup.controls.validityQualifierType.setValue(ValidityQualifierType.FILL_AND_KILL);
        expect(formGroup.controls.minimumQuantity.disabled).toBeTrue();
        expect(formGroup.controls.minimumQuantity.value).toBeNull();
    });

    it('should enable minimumQuantity when validityQualifierType is not FILL_AND_KILL', () => {
        formGroup.controls.validityQualifierType.setValue('SOME_OTHER_TYPE' as any);
        expect(formGroup.controls.minimumQuantity.enabled).toBeTrue();
    });

    it('should enable disclosedQuantity only when quantity exists and type is ICEBERG', () => {
        formGroup.controls.quantity.setValue(10);
        formGroup.controls.type.setValue(OrderType.ICEBERG);
        expect(formGroup.controls.disclosedQuantity.enabled).toBeTrue();

        formGroup.controls.type.setValue(OrderType.LIMIT);
        expect(formGroup.controls.disclosedQuantity.disabled).toBeTrue();
    });

    it('should enable and set validityDate for GOOD_TILL_DATE', () => {
        formGroup.controls.validityQualifierType.setValue(ValidityQualifierType.GOOD_TILL_DATE);
        expect(formGroup.controls.validityDate.enabled).toBeTrue();
        expect(formGroup.controls.validityDate.value).toEqual(StoreService.systemDateTime);
    });

    it('should disable and clear validityDate for other types', () => {
        formGroup.controls.validityQualifierType.setValue('OTHER' as any);
        expect(formGroup.controls.validityDate.disabled).toBeTrue();
        expect(formGroup.controls.validityDate.value).toBeNull();
    });

    it('should disable and clear price when type is MARKET_TO_LIMIT', () => {
        formGroup.controls.type.setValue(OrderType.MARKET_TO_LIMIT);
        expect(formGroup.controls.price.disabled).toBeTrue();
        expect(formGroup.controls.price.value).toBeNull();
    });

    it('should enable price when type is not MARKET_TO_LIMIT', () => {
        formGroup.controls.type.setValue(OrderType.LIMIT);
        expect(formGroup.controls.price.enabled).toBeTrue();
    });
});
