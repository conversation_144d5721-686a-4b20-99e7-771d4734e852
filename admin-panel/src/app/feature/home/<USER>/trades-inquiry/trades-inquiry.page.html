<app-page-panel [filterProperty]="filterProperty">
    <ng-container *breadcrumb>{{translateKeys.trades | translate}}</ng-container>
    <ng-container *breadcrumb>{{instrument?.mnemonic | selectByLanguage}}</ng-container>

    <app-panel-title>
        <span class="d-flex">
            <span class="text-nowrap">{{ translateKeys.trades | translate }}</span>

            <app-market-data-widget [widgetData]="marketWidgetData()"></app-market-data-widget>
        </span>
    </app-panel-title>

    <button
        mat-button
        *actionBtn
        [disabled]="!isTradingSession"
        [openInsertOrderDialog]="instrument">
        <mat-icon color="primary" class="material-icons-outlined">note_add</mat-icon>
        {{translateKeys.insertOrder | translate}}
    </button>

    <button
        mat-button
        *actionBtn
        #openInstrumentInfoDialog="openInstrumentInfoDialog"
        [openInstrumentInfoDialog]="instrument"
        (shortKeypress)="openInstrumentInfoDialog.onClick()"
        shortKeyHint="i">
        <mat-icon color="primary">feed</mat-icon>
        {{translateKeys.instrumentInfo | translate}}
    </button>

    <app-panel-content>
        <div class="w-100 h-100" #tableContainer>
            <app-data-table
                [isPageDataLoaded]="isPageDataLoaded()"
                [dataSource]="dataSource"
                [filterProperty]="filterProperty"
                [noDataRowText]="noDataRowText"
                [columns]="columns"
                [dataTableColumns]="getDataTableColumns()">

                <ng-container *rowActionBtns="let row">
                    <button
                        mat-menu-item
                        (click)="openTradeDetailsDialog(toTrade(row))">
                        <mat-icon>list</mat-icon>
                        <span>{{translateKeys.tradeDetails | translate}}</span>
                    </button>
                    <button
                        mat-menu-item
                        [disabled]="!isTradingSession"
                        (confirm)="cancelTrades([toTrade(row).sequenceId])">
                        <mat-icon>delete</mat-icon>
                        <span>{{translateKeys.cancelTrade | translate}}</span>
                    </button>
                </ng-container>

                <ng-container customColumn="select">
                    <ng-container *customHeaderDef>
                        <mat-checkbox
                            (change)="toggleAllRows()"
                            [checked]="selection.hasValue() && isAllSelected()"
                            [indeterminate]="selection.hasValue() && !isAllSelected()">
                        </mat-checkbox>
                    </ng-container>
                    <ng-container *customCellDef="let row">
                        <mat-checkbox
                            (click)="$event.stopPropagation()"
                            (change)="$event ? selection.toggle(row) : null"
                            [checked]="selection.isSelected(row)">
                        </mat-checkbox>
                    </ng-container>
                </ng-container>
            </app-data-table>

            <mat-paginator
                *paginator
                [ngClass]="paginatorClass"
                [length]="dataSourceLength"
                [pageSize]="dataSourcePageSize"
                [showFirstLastButtons]="true">
            </mat-paginator>
        </div>
    </app-panel-content>
</app-page-panel>
