import {CustomDecimalPipe} from '@shared/pipes/custom-decimal.pipe';
import {TranslateKey} from '@enums/translate-key';
import {TableGroupData} from '@models/table-representation/table-group-data';
import {Instrument} from '@homeModels/instrument';

export function getTradesWidgetTableData(instrumentDynamic: Instrument.DynamicData): TableGroupData[] {
    return [
        {
            title: TranslateKey.closingPrice,
            value: instrumentDynamic.closingPrice,
            pipeToken: CustomDecimalPipe
        },
        {
            title: TranslateKey.lastTradedPrice,
            value: instrumentDynamic.lastTradedPrice,
            pipeToken: CustomDecimalPipe
        },
        {
            title: 'TOP' as any,
            value: instrumentDynamic.iop,
            pipeToken: CustomDecimalPipe
        },
        {
            title: TranslateKey.totalTradedWorth,
            value: instrumentDynamic.totalTradedWorth,
            pipeToken: CustomDecimalPipe
        },
        {
            title: TranslateKey.totalTradeCount,
            value: instrumentDynamic.totalTradeCount,
            pipeToken: CustomDecimalPipe
        },
        {
            title: TranslateKey.totalShareCount,
            value: instrumentDynamic.totalShareCount,
            pipeToken: CustomDecimalPipe
        }
    ]
}
