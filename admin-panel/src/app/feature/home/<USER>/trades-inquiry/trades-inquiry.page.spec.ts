import {TestBed} from '@angular/core/testing';
import {RouterTestingModule} from '@angular/router/testing';
import {OpenInsertOrderDialogDirective} from '@directives/open-insert-order-dialog.directive';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {Http} from '@http';
import {HttpClient} from '@angular/common/http';
import {NavigationBarComponent} from '@modules/page-template/navigation-bar/navigation-bar.component';
import {BsModalService} from 'ngx-bootstrap/modal';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {SharedModule} from '../../../../shared/shared.module';
import {HttpClientTestingModule} from '@angular/common/http/testing';
import {PageTemplateModule} from '@modules/page-template/page-template.module';
import {DataTableFunctionality} from '@test/data-table-functionality';
import {OpenInstrumentInfoDialogDirective} from '@directives/open-instrument-info-dialog.directive';
import {ComponentHarness} from '@test/harness/component-harness';
import {Trade} from '@models/trade';
import {TestUtils} from '@test/test-utils';
import {TradeDataService} from '@dataServices/trade-data.service';
import {tradeDataServiceHarness} from '@dataServices/trade-data.service.harness';
import {of} from 'rxjs';
import {mockBsModalService} from '@test/harness/fake/mock-bs-modal-service';
import {DynamicPipe} from '@pipes/dynamic.pipe';
import {DataTableModule} from '@modules/data-table/data-table.module';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {InstrumentDataService} from '@dataServices/instrument-data.service';
import {instrumentDataServiceHarness} from '@dataServices/instrument-data.service.harness';
import {TradesInquiryPage} from './trades-inquiry.page';

describe('TradesInquiryPage', () => {
    let ha: ComponentHarness<TradesInquiryPage>;

    beforeEach(() => {
        ha = new ComponentHarness(TradesInquiryPage, {
            declarations: [
                OpenInsertOrderDialogDirective,
                OpenInstrumentInfoDialogDirective,
                NavigationBarComponent,
                SelectByLanguagePipe,
                DynamicPipe
            ],
            imports: [
                TradesInquiryPage,
                RouterTestingModule,
                TranslateTestingModule,
                BrowserAnimationsModule,
                HttpClientTestingModule,
                SharedModule,
                PageTemplateModule,
                DataTableModule
            ],
            providers: [
                SelectByLanguagePipe,
                {provide: InstrumentDataService, useValue: instrumentDataServiceHarness},
                {provide: TradeDataService, useValue: tradeDataServiceHarness},
                {provide: BsModalService, useValue: mockBsModalService}
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
            detectChanges: false
        });

        Http.httpClient = TestBed.inject(HttpClient);
        ha.component['_instrument'] = TestUtils.getInstrument() as any; // TODO: remove any
    });

    it('should create', () => {
        ha.detectChanges();
        expect(ha.component).toBeTruthy();
    });

    describe('DataTable functionality', () => {
        it('when the total number of rows exceeds the page size, pagination should be displayed', () => {
            ha.detectChanges();
            DataTableFunctionality.when_the_total_number_of_rows_exceeds_the_page_size_pagination_should_be_displayed(ha);
        });

        it('pagination should be hidden when the total number of rows is less than the page size', () => {
            ha.detectChanges();
            DataTableFunctionality.pagination_should_be_hidden_when_the_total_number_of_rows_is_less_than_the_page_size(ha);
        });
    });

    it('should "Cancel Trades" button be disabled when none trade row is checked', () => {
        ha.detectChanges();
        const cancelTradeBtn = ha.get('button').thatContains('cancel');
        expect(cancelTradeBtn).toHaveAttribute('disabled');
    });

    it('should display "Cancel Trades" button when a trade row is checked', () => {
        ha.component.selection.select(new Trade());
        ha.detectChanges();

        expect(ha.component.selection.selected.length).toEqual(1);

        const cancelTradeBtn2 = ha.get('button').thatContains('cancel');
        expect(cancelTradeBtn2).toBeExists();
    });

    it('should toggle select all trade rows by clicking on toggle selectAll checkbox', () => {
        const tradesData = TestUtils.getTrades();
        spyOn(tradeDataServiceHarness, 'getTrades').and.returnValue(of(tradesData));
        ha.detectChanges();

        ha.get('th mat-checkbox input').click(new MouseEvent('click'));
        expect(ha.component.selection.selected.length).toEqual(tradesData.content.length);

        ha.get('th mat-checkbox input').click(new MouseEvent('click'));
        expect(ha.component.selection.selected.length).toEqual(0);
    });

    it('should call #cancelTrades service method while calling #cancelTrades method', () => {
        spyOn(tradeDataServiceHarness, 'cancelTrades').and.returnValue(of())

        ha.component.cancelTrades([0]);
        expect(tradeDataServiceHarness.cancelTrades).toHaveBeenCalled();
    });

    it('should call #cancelTrdes while calling #cancelSelectedTrades', () => {
        spyOn(ha.component, 'cancelTrades');
        const trade: Trade = TestUtils.getTrades().content[0];
        ha.component.selection.select(trade);

        ha.component['_cancelSelectedTrades']();
        expect(ha.component.cancelTrades).toHaveBeenCalledWith([trade.sequenceId]);
    });
});
