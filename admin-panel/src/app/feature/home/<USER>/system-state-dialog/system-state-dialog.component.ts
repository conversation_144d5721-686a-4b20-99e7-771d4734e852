import {Component, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {SystemStateDataService} from '@dataServices/system-state-data.service';
import {StoreService} from '@shared/services/store.service';
import {AdminCommandCallback} from '@models/admin-command-callback';
import {AdminCommand} from '@models/admin-command';
import {BsModalRef, BsModalService} from 'ngx-bootstrap/modal';
import {ClearingDataService} from '@dataServices/clearing-data.service';
import {SystemState} from '@models/system-state';
import {SystemStateAdminCommands} from '@models/system-state-admin-commands';
import {takeUntil} from 'rxjs/operators';
import {Subject} from 'rxjs';

@Component({
    selector: 'app-system-state-dialog',
    templateUrl: './system-state-dialog.component.html',
    styleUrls: ['./system-state-dialog.component.scss'],
    standalone: false
})
export class SystemStateDialogComponent implements OnInit, OnDestroy {
    readonly translateKeys = TranslateKey;

    get isPreSessionState(): boolean { return this._currentState === SystemState.State.PRE_SESSION; }

    get isTradingSessionState(): boolean { return this._currentState === SystemState.State.TRADING_SESSION; }

    get isPostSessionState(): boolean { return this._currentState === SystemState.State.POST_SESSION; }

    get isPrepareToShutdownState(): boolean { return this._currentState === SystemState.State.PREPARE_TO_SHUTDOWN; }

    get isPreSessionDisabled(): boolean { return this.isTradingSessionState || this.isPostSessionState; }

    get isTradingSessionDisabled(): boolean { return this.isPostSessionState; }

    get isPostSessionDisabled(): boolean { return this.isPreSessionState; }

    get isPrepareToShutdownDisabled(): boolean {
        return this.isPreSessionState || this.isTradingSessionState || this.shouldBeUploaded;
    }

    private _allSystemStatesActivated: boolean;
    get allSystemStatesActivated(): boolean { return this._allSystemStatesActivated; }
    set allSystemStatesActivated(value: boolean) { this._allSystemStatesActivated = value; }

    private _isSystemStateEnabled: boolean;
    get isSystemStateEnabled() { return this._isSystemStateEnabled; }

    private _shouldBeUploaded: boolean;
    get shouldBeUploaded(): boolean { return this._shouldBeUploaded; }

    private _currentState: SystemState.State;

    private _onDestroy = new Subject();

    constructor(
        private _systemStateDataService: SystemStateDataService,
        private _clearingDataService: ClearingDataService,
        private _modalService: BsModalService,
        private _modalRef: BsModalRef
    ) { }

    ngOnInit(): void {
        this._fetchSystemState();
        this._fetchSystemStateChecking();
        this._updateUploadStatus();
    }

    ngOnDestroy(): void {
        this._onDestroy.next();
        this._onDestroy.complete();
    }

    activePreSession(): void {
        this._updateSystemState(SystemState.State.PRE_SESSION);
    }

    activeTradingSession(): void {
        this._updateSystemState(SystemState.State.TRADING_SESSION);
    }

    activePostSession(): void {
        this._updateSystemState(SystemState.State.POST_SESSION);
    }

    activePrepareToShutdownSession(): void {
        this._modalService.onHidden
            .pipe(takeUntil(this._onDestroy))
            .subscribe(() => {
                this._modalRef.hide();
                // Before redirecting to prepare-to-shutdown state we need to close all open modals,
                // Then, we update system-state after 200ms
                setTimeout(() => this._updateSystemState(SystemState.State.PREPARE_TO_SHUTDOWN), 200);
            });
    }

    private _fetchSystemState(): void {
        StoreService.systemState.subscribe(resp => this._currentState = resp);
    }

    private _fetchSystemStateChecking(): void {
        this._systemStateDataService
            .getSystemStateChecking()
            .subscribe(resp => {
                this._isSystemStateEnabled = resp;
            });
    }

    private _updateSystemState(targetState: SystemState.State): void {
        this._systemStateDataService
            .updateSystemState(targetState)
            .subscribe(resp => {
                this._currentState = targetState;

                const adminCommandCallBack = new AdminCommandCallback(resp.commandId, this._onAdminCommandSuccess.bind(this));
                StoreService.adminCommandCallBacks.push(adminCommandCallBack);
            })
    }

    private _onAdminCommandSuccess(adminCommand: AdminCommand<SystemStateAdminCommands.Change>): void {
        StoreService.systemState.next(adminCommand.request.targetState);
    }

    private _updateUploadStatus(): void {
        this._clearingDataService
            .shouldBeUploaded()
            .subscribe(resp => {
                this._shouldBeUploaded = resp;
            })
    }
}
