import {TestBed} from '@angular/core/testing';
import {SystemStateDialogComponent} from './system-state-dialog.component';
import {HttpClientTestingModule} from '@angular/common/http/testing';
import {Http} from '@http';
import {HttpClient} from '@angular/common/http';
import {ModalHeaderComponent} from '@modules/shared-declarations/modal-header/modal-header.component';
import {BsModalRef, ModalModule} from 'ngx-bootstrap/modal';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {SharedModule} from '../../../../shared/shared.module';
import {SharedDeclarations} from '@modules/shared-declarations/shared-declarations';
import {SystemStateDataService} from '@dataServices/system-state-data.service';
import {systemStateDataServiceHarness} from '@dataServices/system-state-data.service.harness';
import {Subject} from 'rxjs';
import {StoreService} from '@shared/services/store.service';
import {ComponentHarness} from '@test/harness/component-harness';
import {ConfirmDialogComponent} from '../../../../shared/components/confirm-dialog/confirm-dialog.component';
import {SystemState} from '@models/system-state';

describe('SystemStateDialogComponent', () => {
    let ha: ComponentHarness<SystemStateDialogComponent>;

    beforeEach(() => {
        ha = new ComponentHarness(SystemStateDialogComponent, {
            declarations: [
                SystemStateDialogComponent,
                ModalHeaderComponent,
                ConfirmDialogComponent
            ],
            imports: [
                HttpClientTestingModule,
                TranslateTestingModule,
                ModalModule.forRoot(),
                SharedModule,
                SharedDeclarations
            ],
            providers: [
                BsModalRef,
                {provide: SystemStateDataService, useValue: systemStateDataServiceHarness}
            ],
            detectChanges: false
        });

        Http.httpClient = TestBed.inject(HttpClient);
    });

    it('should create', () => {
        ha.detectChanges();
        expect(ha.component).toBeTruthy();
    });

    it('should not call #updateSystemState by clicking Pre Session button when current state is PRE_SESSION', () => {
        spyOnProperty(ha.component, 'isSystemStateEnabled', 'get').and.returnValue(true);
        spyOn(systemStateDataServiceHarness, 'updateSystemState').and.returnValue(new Subject());
        StoreService.systemState.next(SystemState.State.PRE_SESSION);
        ha.detectChanges();

        ha.get('.session-card').thatContains('Pre Session').click();
        expect(systemStateDataServiceHarness.updateSystemState).not.toHaveBeenCalled();
    });

    it('should not call #updateSystemState by clicking Trading Session button when current state is TRADING_SESSION', () => {
        spyOnProperty(ha.component, 'isSystemStateEnabled', 'get').and.returnValue(true);
        spyOn(systemStateDataServiceHarness, 'updateSystemState').and.returnValue(new Subject());
        StoreService.systemState.next(SystemState.State.TRADING_SESSION);
        ha.detectChanges();

        ha.get('.session-card').thatContains('Trading Session').click();
        expect(systemStateDataServiceHarness.updateSystemState).not.toHaveBeenCalled();
    });

    it('should not call #updateSystemState by clicking Post Session button when current state is POST_SESSION', () => {
        spyOnProperty(ha.component, 'isSystemStateEnabled', 'get').and.returnValue(true);
        spyOn(systemStateDataServiceHarness, 'updateSystemState').and.returnValue(new Subject());
        StoreService.systemState.next(SystemState.State.POST_SESSION);
        ha.detectChanges();

        ha.get('.session-card').thatContains('Post Session').click();
        expect(systemStateDataServiceHarness.updateSystemState).not.toHaveBeenCalled();
    });

    it('should not call #updateSystemState by clicking Prepare To Shutdown button when current state is PREPARE_TO_SHUTDOWN', () => {
        spyOnProperty(ha.component, 'isSystemStateEnabled', 'get').and.returnValue(true);
        spyOn(systemStateDataServiceHarness, 'updateSystemState').and.returnValue(new Subject());
        StoreService.systemState.next(SystemState.State.PREPARE_TO_SHUTDOWN);
        ha.detectChanges();

        ha.get('.session-card').thatContains('Prepare To Shutdown').click();
        expect(systemStateDataServiceHarness.updateSystemState).not.toHaveBeenCalled();
    });

    it('should be able to click Pre Session button when current state is TRADING_SESSION and system state is disabled', () => {
        spyOnProperty(ha.component, 'isSystemStateEnabled', 'get').and.returnValue(false);
        spyOn(systemStateDataServiceHarness, 'updateSystemState').and.returnValue(new Subject());
        StoreService.systemState.next(SystemState.State.TRADING_SESSION);
        ha.component.allSystemStatesActivated = true;
        ha.detectChanges();

        expect(ha.get('.session-card').thatContains('Pre Session')).not.toHaveClass('disabled');
    });
});
