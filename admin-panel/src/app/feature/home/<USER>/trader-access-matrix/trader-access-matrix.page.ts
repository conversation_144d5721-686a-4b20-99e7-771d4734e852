import {Component, ElementRef, ViewChild} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {MatPaginator} from '@angular/material/paginator';
import {PaginatedDataTable} from '@models/paginated-data-table';
import {TraderAccessMatrixDataTableColumns} from './trader-access-matrix-data-table-columns';
import {RequestOptions} from '@models/request-options';
import {Observable} from 'rxjs';
import {map, takeUntil} from 'rxjs/operators';
import {PagesSharedModule} from '../../shared/modules/pages-shared.module';
import {AccessMatrixDataService} from '../../shared/data-services/access-matrix-data.service';
import {TraderAccessMatrix} from '@homeModels/access-matrix/trader-access-matrix';
import {BsModalService} from 'ngx-bootstrap/modal';
import {FormGroup} from '@angular/forms';
import {traderAccessMatrixFormFields} from './trader-access-matrix-form-fields';
import {AdminCommandCallback} from '@models/admin-command-callback';
import {StoreService} from '@shared/services/store.service';
import {AdminCommand} from '@models/admin-command';
import {BrokerAccessMatrix} from '@homeModels/access-matrix/broker-access-matrix';
import {DynamicFormDialogService} from '../../dialogs/dynamic-form-dialog/dynamic-form-dialog.service';
import {Permissions} from '../../../../shared/constants/permissions.constant';
import {copyAccessMatrixFormFields} from '../../shared/constants/copy-access-matrix-form-fields';
import {AccessMatrix} from '@homeModels/access-matrix';

@Component({
    selector: 'app-trader-access-matrix-page',
    templateUrl: './trader-access-matrix.page.html',
    imports: [
        PagesSharedModule,
    ]
})
export class TraderAccessMatrixPage extends PaginatedDataTable<TraderAccessMatrix> {
    readonly translateKeys = TranslateKey;

    readonly permissions = Permissions;

    readonly columns = TraderAccessMatrixDataTableColumns();

    @ViewChild('tableContainer')
    override _tableContainer: ElementRef<HTMLDivElement>;

    @ViewChild(MatPaginator)
    override _paginator: MatPaginator;

    constructor(
        private _modalService: BsModalService,
        private _accessMatrixDataService: AccessMatrixDataService,
        private _dynamicFormDialogService: DynamicFormDialogService
    ) {
        super();

        this.filterProperty.refreshPage = this._refreshPageData.bind(this);
        this.filterProperty.callback = this._filterPageData.bind(this);
        this.filterProperty.actionBtns.unshift(...[
            {
                title: TranslateKey.addGroupBasedBlockEntry,
                icon: 'add',
                callback: this._openAddTraderAccessMatrixDialog.bind(this),
                permissions: this.permissions.CREATE_TRADER_ACCESS_MATRIX_PERMISSION
            },
            {
                title: TranslateKey.copy,
                icon: 'content_copy',
                callback: this._openCopyAccessMatrixDialog.bind(this),
                permissions: this.permissions.COPY_ACCESS_MATRIX_PERMISSION
            }
        ]);
    }

    deleteAccessMatrix(row: TraderAccessMatrix): void {
        this._accessMatrixDataService
            .deleteAccessMatrix({
                groupCode: row.group.code,
                traderId: row.traderId
            })
            .pipe(takeUntil(this._onDestroy))
            .subscribe(this._adminCommandCallback.bind(this))
    }

    updateTraderAccessMatrix(row: TraderAccessMatrix): void {
        this._accessMatrixDataService
            .updateAccessMatrix(row)
            .pipe(takeUntil(this._onDestroy))
            .subscribe({
                next: this._adminCommandCallback.bind(this),
                error: this._refreshPageData.bind(this)
            })
    }

    toTraderAccessMatrix(row: TraderAccessMatrix): TraderAccessMatrix {
        return row;
    }

    isBlockedBuySideDisabled(row: BrokerAccessMatrix) {
        return row.isBuyBlocked && !row.isSellBlocked;
    }

    isBlockedSellSideDisabled(row: BrokerAccessMatrix) {
        return row.isSellBlocked && !row.isBuyBlocked
    }

    override fetchExcelData(): Observable<TraderAccessMatrix[]> {
        const requestOptions: RequestOptions = {
            params: this.getFilterQueryParams()
        }

        return this._accessMatrixDataService
            .getTraderAccessMatrix(requestOptions)
            .pipe(
                takeUntil(this._onDestroy),
                map(resp => resp.content)
            );
    }

    override _fetchPageData(): void {
        const requestOptions: RequestOptions = {
            hasLocalErrorHandler: true,
            params: this.getFilterQueryParams()
        }

        this._accessMatrixDataService
            .getTraderAccessMatrix(requestOptions)
            .pipe(takeUntil(this._onDestroy))
            .subscribe(resp => {
                this._pageData = resp;
                this._changeDetectorRef.detectChanges();
            });
    }

    private _openAddTraderAccessMatrixDialog(): void {
        this._dynamicFormDialogService.open({
            formTitle: TranslateKey.addGroupBasedBlockEntry,
            formFields: traderAccessMatrixFormFields(),
            hasKeepDialogOpen: true,
            onSubmit: this._createTraderAccessMatrix.bind(this)
        }, {class: 'non-scrollable-modal-body'});
    }

    private _createTraderAccessMatrix(formGroup: FormGroup, keepDialogOpen: boolean): void {
        this._accessMatrixDataService
            .createTraderGroupAccessMatrix(formGroup.getRawValue())
            .pipe(takeUntil(this._onDestroy))
            .subscribe(resp => {
                if (!keepDialogOpen) {
                    this._modalService.hide();
                }
                this._adminCommandCallback(resp);
            })
    }

    private _openCopyAccessMatrixDialog(): void {
        this._dynamicFormDialogService.open({
            formTitle: TranslateKey.copyAccessMatrix,
            formFields: copyAccessMatrixFormFields(AccessMatrix.EntryType.TRADER),
            hasKeepDialogOpen: true,
            onSubmit: this._copyAccessMatrix.bind(this)
        }, {class: 'non-scrollable-modal-body'});
    }

    private _copyAccessMatrix(formGroup: FormGroup, keepDialogOpen: boolean): void {
        this._accessMatrixDataService
            .copyAccessMatrix(formGroup.getRawValue())
            .pipe(takeUntil(this._onDestroy))
            .subscribe(resp => {
                if (!keepDialogOpen) {
                    this._modalService.hide();
                }
                this._adminCommandCallback(resp);
            })
    }

    private _adminCommandCallback(resp: AdminCommand): void {
        const adminCommandCallBack = new AdminCommandCallback(resp.commandId, this._refreshPageData.bind(this));
        StoreService.adminCommandCallBacks.push(adminCommandCallBack);
    }
}
