import {ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit, signal} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {AramisFile, AramisFilesList, OpenOrdersFile} from './aramis-files-list';
import {StoreService} from '@shared/services/store.service';
import {SystemState} from '@models/system-state';
import {InstrumentDataService} from '@dataServices/instrument-data.service';
import {interval, Observable} from 'rxjs';
import {expand, filter, switchMap, take} from 'rxjs/operators';
import {AramisDataService} from '@dataServices/aramis-data.service';
import {SnackBarService} from '@services/snack-bar.service';
import {UtilConstants} from '@constants/util-constants';

@Component({
    selector: 'app-get-aramis-files-dialog',
    templateUrl: './get-aramis-files-dialog.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class GetAramisFilesDialogComponent implements OnInit {
    readonly translateKeys = TranslateKey;

    readonly aramisFiles: AramisFile[] = AramisFilesList;

    readonly openOrdersFile: AramisFile = OpenOrdersFile;

    private _systemState: SystemState.State;

    readonly isSavingToDisk = signal(false);

    readonly isDownloadingOrderFiles = signal(false);

    get isPostSession(): boolean { return this._systemState === SystemState.State.POST_SESSION; }

    constructor(
        public modalRef: BsModalRef,
        private _snackBarService: SnackBarService,
        private _aramisDataService: AramisDataService,
        private _changeDetectorRef: ChangeDetectorRef,
        private _instrumentDataService: InstrumentDataService
    ) { }

    ngOnInit() {
        this._subscribeOnSystemState();
    }

    declareRemainingOrders(): void {
        this.isDownloadingOrderFiles.set(true);

        this._instrumentDataService.declareRemainingOrders()
            .pipe(
                // If the response is falsy, repeat the call
                expand(this._fetchRemainingOrderStatus.bind(this)),
                // Let only the `true` pass
                filter(resp => resp === true),
                // Take the first true result
                take(1)
            )
            .subscribe({
                next: () => {
                    this._downloadOpenOrdersFile();
                    this._onDownloadOpenOrdersSuccess();
                },
                error: this._onDownloadOpenOrdersError.bind(this)
            });
    }

    saveToDisk(): void {
        this.isSavingToDisk.set(true);

        this._instrumentDataService
            .declareRemainingOrders()
            .pipe(
                // If the response is falsy, repeat the call
                expand(this._fetchRemainingOrderStatus.bind(this)),
                // Let only the `true` pass
                filter(resp => resp === true),
                // Take the first true result
                take(1),
                // Now call switchMap
                switchMap(this._aramisDataService.saveToDisk)
            )
            .subscribe({
                next: this._onSaveToDiskSuccess.bind(this),
                error: this._onSaveToDiskError.bind(this)
            });
    }

    private _downloadOpenOrdersFile(): void {
        const openOrdersFileLink = document.createElement('a');
        openOrdersFileLink.style['display'] = 'hidden';
        openOrdersFileLink.setAttribute('href', this.openOrdersFile.downloadLink);
        openOrdersFileLink.click();
        openOrdersFileLink.remove();
    }

    private _subscribeOnSystemState(): void {
        StoreService.systemState.subscribe(resp => {
            this._systemState = resp;
            this._changeDetectorRef.detectChanges();
        });
    }

    private _onDownloadOpenOrdersSuccess(): void {
        this._snackBarService.showSuccess(TranslateKey.successfullyOpenOrdersDownload);
        this.isDownloadingOrderFiles.set(false);
    }

    private _onDownloadOpenOrdersError(): void {
        this._snackBarService.showError(TranslateKey.failedOnDownloadingOpenOrders);
        this.isDownloadingOrderFiles.set(false);
    }

    private _onSaveToDiskSuccess(): void {
        this._snackBarService.showSuccess(TranslateKey.successfullySavedToDisk);
        this.isSavingToDisk.set(false);
    }

    private _onSaveToDiskError(): void {
        this._snackBarService.showError(TranslateKey.failedOnSavingToDisk);
        this.isSavingToDisk.set(false);
    }

    private _fetchRemainingOrderStatus(): Observable<boolean> {
        return interval(UtilConstants.GET_ARAMIS_STATUS_INTERVAL)
            .pipe(switchMap(() => this._instrumentDataService.getDeclareRemainingOrdersStatus()));
    }
}
