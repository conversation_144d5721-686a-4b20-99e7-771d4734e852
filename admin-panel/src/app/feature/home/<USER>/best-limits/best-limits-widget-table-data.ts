import {Instrument} from '@homeModels/instrument';
import {CustomDecimalPipe} from '@shared/pipes/custom-decimal.pipe';
import {TranslateKey} from '@enums/translate-key';
import {TranslatePipe} from '@ngx-translate/core';
import {TableGroupData} from '@models/table-representation/table-group-data';

export function getBestLimitsWidgetTableData(instrument: Instrument.Single, instrumentDynamic: Instrument.DynamicData): TableGroupData[] {
    return [
        {
            title: TranslateKey.instrumentState,
            value: instrumentDynamic.state.toLowerCase(),
            pipeToken: TranslatePipe
        },
        {
            title: TranslateKey.closingPrice,
            value: instrumentDynamic.closingPrice,
            pipeToken: CustomDecimalPipe
        },
        {
            title: TranslateKey.lastTradedPrice,
            value: instrumentDynamic.lastTradedPrice,
            pipeToken: CustomDecimalPipe
        },
        {
            title: 'TOP' as any,
            value: instrumentDynamic.iop,
            pipeToken: CustomDecimalPipe
        },
        {
            title: TranslateKey.staticPriceBandLowerBound,
            value: instrument.staticPriceBandLowerBound,
            pipeToken: CustomDecimalPipe
        },
        {
            title: TranslateKey.staticPriceBandUpperBound,
            value: instrument.staticPriceBandUpperBound,
            pipeToken: CustomDecimalPipe
        }
    ]
}
