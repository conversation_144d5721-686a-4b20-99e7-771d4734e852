<app-page-panel [filterProperty]="filterProperty">
    <ng-container *breadcrumb>{{translateKeys.bestLimits | translate}}</ng-container>
    <ng-container *breadcrumb>{{instrument.mnemonic | selectByLanguage}}</ng-container>

    <app-panel-title>
        <span class="d-flex">
            <span class="text-nowrap">{{ translateKeys.bestLimits | translate }}</span>

            <app-market-data-widget [widgetData]="marketWidgetData()"></app-market-data-widget>
        </span>
    </app-panel-title>

    <button
        mat-button
        *actionBtn
        #openInstrumentInfoDialog="openInstrumentInfoDialog"
        [openInstrumentInfoDialog]="instrument"
        (shortKeypress)="openInstrumentInfoDialog.onClick()"
        shortKeyHint="i">
        <mat-icon color="primary">feed</mat-icon>
        {{translateKeys.instrumentInfo | translate}}
    </button>

    <app-panel-content>
        <div class="w-100 h-100 position-absolute" #tableContainer>
            <app-data-table
                [isPageDataLoaded]="isPageDataLoaded()"
                [dataSource]="dataSource"
                [filterProperty]="filterProperty"
                [noDataRowText]="noDataRowText"
                [columns]="columns"
                [dataTableColumns]="getDataTableColumns()">
            </app-data-table>
        </div>
    </app-panel-content>
</app-page-panel>
