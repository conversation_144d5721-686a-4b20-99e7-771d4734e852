import {DataTableColumn} from '@models/data-table';
import {TranslateKey} from '@enums/translate-key';
import {RouteNodeCustom} from './route-node-custom';
import {InputFilter} from '@modules/filter/input/input-filter';

export function routeTreeDataTableColumns(): DataTableColumn<RouteNodeCustom>[] {
    return [
        {
            title: TranslateKey.name,
            hasTemplate: true,
            class: getRowClass,
            value: getNameValue,
            filter: new InputFilter<string>({
                queryParam: 'name'
            })
        },
        {
            title: TranslateKey.incomingPredicate,
            class: getRowClass,
            value: getIncomingPredicateValue,
            filter: new InputFilter<string>({
                queryParam: 'incomingPredicate'
            })
        },
        {
            title: TranslateKey.value,
            class: getRowClass,
            value: getValueValue,
            filter: new InputFilter<string>({
                queryParam: 'value'
            })
        },
        {
            title: TranslateKey.outgoingPredicate,
            class: getRowClass,
            value: getOutgoingPredicateValue,
            filter: new InputFilter<string>({
                queryParam: 'outgoingPredicate'
            })
        },
        {
            title: TranslateKey.routeNodeCode,
            class: getRowClass,
            value: getNodeCodeValue,
            filter: new InputFilter<string>({
                queryParam: 'nodeCode'
            })
        }
    ];
}


export function getNodeCodeValue(data: RouteNodeCustom): string {
    return data.nodeCode;
}

export function getOutgoingPredicateValue(data: RouteNodeCustom): string {
    return data.predicateAppliedOnChildren?.title;
}

export function getIncomingPredicateValue(data: RouteNodeCustom): string {
    return data.parent?.predicateAppliedOnChildren?.title;
}

export function getValueValue(data: RouteNodeCustom): string {
    const {from, to, items, isDefault} = data.value || {};
    if (isDefault) { return 'Default'; }
    const fromTo = from !== undefined ? ` [${from}-${to}]` : '';
    return items ? items.join(', ') + fromTo : '';
}

export function getNameValue(data: RouteNodeCustom): string {
    return data.name;
}

export function getRowClass(data: RouteNodeCustom): string {
    return data.blockedByMe ? 'green-cell' : '';
}
