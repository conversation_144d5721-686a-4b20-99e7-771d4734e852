import {Injectable} from '@angular/core';
import {BsModalService} from 'ngx-bootstrap/modal';
import {DynamicFormDialogService} from '../../dialogs/dynamic-form-dialog/dynamic-form-dialog.service';
import {FormFields} from '@modules/dynamic-form/form-field';
import {childFormFields, nodeFormFields, VALUE} from './route-tree-form.fields';
import {FormGroup} from '@angular/forms';
import {CapGroup} from '@homeModels/cap-group';
import {RouteTree} from '@homeModels/route-tree';
import {TableGroupDialogComponent} from '../../dialogs/table-group-dialog/table-group-dialog.component';
import {TableGroupDialog} from '../../dialogs/table-group-dialog/table-group-dialog';
import {routeTreeDetailTableGroupData} from './route-tree-detail-table-group-data';
import {TranslateKey} from '@enums/translate-key';
import {RoutePredicate} from '@homeModels/route-predicate';
import {RoutePredicateService} from '../../shared/data-services/route-predicate.service';
import {map} from 'rxjs/operators';
import {validRootFormFields} from './valid-root-form.fields';
import {RouteNodeCustom} from './route-node-custom';
import {Mutable} from '@models/form-group-raw-value';
import {CapGroupDataService} from '../../shared/data-services/cap-group-data.service';
import {Page} from '@models/page';

type CapGroupNameList = Pick<CapGroup.Simple, 'capGroupName'>[];

@Injectable()
export class RouteTreeDialogService {
    private _pageData: Page<RouteNodeCustom>;

    private _forceDataTableRefresh: () => void;

    constructor(
        private _modalService: BsModalService,
        private _capGroupDataService: CapGroupDataService,
        private _routePredicateService: RoutePredicateService,
        private _dynamicFormDialogService: DynamicFormDialogService
    ) {}

    setPageData(pageData: Page<RouteNodeCustom>): void {
        this._pageData = pageData;
    }

    setForceDataTableRefresh(forceDataTableRefresh: () => void): void {
        this._forceDataTableRefresh = forceDataTableRefresh;
    }

    openRouteTreeDetailsDialog(node: RouteTree.Node): void {
        this._modalService.show(TableGroupDialogComponent, {
            initialState: new TableGroupDialog(node.name, routeTreeDetailTableGroupData(node)),
            class: 'modal-lg'
        });
    }

    async openUpdateNodeDialog(nodeValue: RouteNodeCustom): Promise<void> {
        const capGroupNameList = await this._fetchCapGroupsData();
        const routePredicates = await this._fetchRoutePredicates();
        const formFields: FormFields = nodeFormFields(nodeValue, capGroupNameList, routePredicates);

        this._dynamicFormDialogService.open({
            formTitle: TranslateKey.editRouteTreeNode,
            formFields,
            formFieldSets: [
                {title: TranslateKey.value, id: VALUE}
            ],
            onSubmit: this._onUpdateNode.bind(this, nodeValue, routePredicates)
        }, {class: 'non-scrollable-modal-body'});
    }

    async openAddChildDialog(node: RouteTree.Node, rowIndex: number) {
        const capGroupNameList = await this._fetchCapGroupsData();
        const formFields: FormFields = childFormFields(capGroupNameList);

        this._dynamicFormDialogService.open({
            formTitle: TranslateKey.newRouteNode,
            formFields,
            formFieldSets: [
                {title: TranslateKey.value, id: VALUE}
            ],
            onSubmit: this._onAddChildNode.bind(this, node, rowIndex)
        }, {class: 'non-scrollable-modal-body'});
    }

    createNewRoot(existedNodeTypes: string[]): void {
        const formFields: FormFields = validRootFormFields(existedNodeTypes);

        this._dynamicFormDialogService.open({
            formTitle: TranslateKey.addTree,
            formFields,
            onSubmit: this._onCreateNewRoot.bind(this)
        });
    }

    removeNode(node: RouteNodeCustom, rowIndex: number): void {
        this._pageData.content.splice(rowIndex, 1);
        const childIndex = node.parent?.children?.findIndex(child => child === node);
        node.parent.children.splice(childIndex, 1);

        this._modalService.hide();
        this._forceDataTableRefresh();
    }

    private async _fetchRoutePredicates(): Promise<RoutePredicate[]> {
        return this._routePredicateService
            .getRoutePredicates()
            .pipe(map(resp => resp.content))
            .toPromise();
    }

    private async _fetchCapGroupsData(): Promise<CapGroupNameList> {
        return this._capGroupDataService
            .getCapGroups()
            .pipe(map(resp => resp.reduce((list, parent) => {
                this._extractCapGroupNameList(parent, list);
                return list;
            }, [])))
            .toPromise();
    }

    private _extractCapGroupNameList(parent: CapGroup.Simple, list: CapGroupNameList): void {
        list.push({capGroupName: parent.capGroupName});
        parent.subGroups?.forEach(item => this._extractCapGroupNameList(item, list));
    }

    private _onAddChildNode(routeNode: RouteNodeCustom, rowIndex: number, formGroup: FormGroup<RouteTree.CreateNodeForm>): void {
        const childNode = formGroup.getRawValue() as unknown as Mutable<RouteNodeCustom>;
        childNode.nodeType = routeNode.nodeType;
        childNode.parent = routeNode;
        routeNode.children.push(childNode);

        if (routeNode.isOpen) {
            childNode.padding = (routeNode.padding || 0) + 20;
            this._pageData.content.splice(rowIndex + routeNode.children.length, 0, childNode);
        }

        this._modalService.hide();
        this._forceDataTableRefresh();
    }

    private _onUpdateNode(nodeValue: Mutable<RouteTree.Node>, routePredicates: RoutePredicate[], formGroup: FormGroup<RouteTree.CreateNodeForm>): void {
        const childNode = formGroup.getRawValue();
        nodeValue.value = childNode.value;
        nodeValue.children = childNode.children;
        nodeValue.name = childNode.name;
        nodeValue.description = childNode.description;
        nodeValue.affectedCapGroups = childNode.affectedCapGroups;
        nodeValue.predicateAppliedOnChildren = routePredicates.find(predicate => predicate.title === childNode.predicateTitle);

        if (!nodeValue.predicateAppliedOnChildren?.title) {
            delete nodeValue.predicateAppliedOnChildren;
        }

        this._modalService.hide();
        this._forceDataTableRefresh();
    }

    private _onCreateNewRoot(formGroup: FormGroup): void {
        const nodeType = formGroup.getRawValue().nodeType;

        this._pageData.content.push({
            value: null,
            children: [],
            name: nodeType,
            nodeType,
            description: null,
            affectedCapGroups: [],
            predicateAppliedOnChildren: null,
            isRoot: true
        });

        this._modalService.hide();
        this._forceDataTableRefresh();
    }
}
