<app-page-panel [filterProperty]="filterProperty">
    <ng-container *breadcrumb>{{ translateKeys.routeTree | translate }}</ng-container>

    <app-panel-content>
        <div #tableContainer class="w-100 h-100 position-absolute">
            <app-data-table
                [isPageDataLoaded]="isPageDataLoaded()"
                [columns]="columns"
                [dataSource]="dataSource | filterRouteTree:filterProperty"
                [dataTableColumns]="getDataTableColumns()"
                [filterProperty]="filterProperty"
                [noDataRowText]="noDataRowText">

                <ng-container customColumn="name">
                    <ng-container *customCellDef="let row; let i = index">
                        <div (click)="toggleOpenChildRow(row, i)"
                             class="d-flex align-items-end">
                            <div
                                *ngIf="!isFiltered"
                                [style.width]="toRouteTree(row).padding + 'px'"></div>
                            <mat-icon
                                [class.filled-symbol]="toRouteTree(row).children?.length"
                                [ngClass]="getColor(row)">
                                @if (toRouteTree(row).isOpen) {
                                    folder_open
                                } @else {
                                    folder
                                }
                            </mat-icon>
                            &nbsp;&nbsp;
                            {{ toRouteTree(row).name }}
                            &nbsp;&nbsp;
                            @if (toRouteTree(row).isRoot) {
                                @if (!isLocked(row)) {
                                    @if (!isLockedByMe(row)) {
                                        <div
                                            *hasPermission="permissions.UPDATE_ROUTE_TREE_PERMISSION"
                                            class="text-success cursor-pointer"
                                            (click)="lockRouteTree($event, row)">
                                            {{ translateKeys.edit | translate }}
                                        </div>
                                    }
                                    @if (isLockedByMe(row)) {
                                        <div *hasPermission="permissions.SAVE_ROUTE_TREE_PERMISSION"
                                             class="text-primary cursor-pointer"
                                             (click)="updateRouteTree($event, row)">
                                            {{ translateKeys.save | translate }}
                                        </div>
                                    }
                                }
                            }
                        </div>
                    </ng-container>
                </ng-container>

                <ng-container customColumn="isDefault">
                    <ng-container *customCellDef="let row">
                        <div class="h-100 d-flex align-items-baseline">
                            <mat-icon *ngIf="toRouteTree(row).value?.isDefault">check</mat-icon>
                        </div>
                    </ng-container>
                </ng-container>

                <ng-container *rowActionBtns="let row; let i = index">
                    <button
                        (click)="openRouteTreeDetailsDialog(row)"
                        mat-menu-item>
                        <mat-icon>list</mat-icon>
                        <span>{{ translateKeys.detail | translate }}</span>
                    </button>

                    <button
                        [disabled]="!isLockedByMe(row)"
                        (click)="openUpdateNodeDialog(row)"
                        mat-menu-item>
                        <mat-icon>edit</mat-icon>
                        {{ translateKeys.edit | translate }}
                    </button>

                    <button
                        (confirm)="removeNode(row, i)"
                        [disabled]="toRouteTree(row).isRoot || !isLockedByMe(row)"
                        mat-menu-item>
                        <mat-icon>delete</mat-icon>
                        {{ translateKeys.delete | translate }}
                    </button>

                    <button
                        (click)="openAddChildDialog(row, i)"
                        [disabled]="!toRouteTree(row).predicateAppliedOnChildren || !isLockedByMe(row)"
                        mat-menu-item>
                        <mat-icon>add</mat-icon>
                        {{ translateKeys.addChildNode | translate }}
                    </button>
                </ng-container>
            </app-data-table>

            <mat-paginator
                *paginator
                [length]="dataSourceLength"
                [ngClass]="paginatorClass"
                [pageSize]="dataSourcePageSize"></mat-paginator>
        </div>
    </app-panel-content>
</app-page-panel>
