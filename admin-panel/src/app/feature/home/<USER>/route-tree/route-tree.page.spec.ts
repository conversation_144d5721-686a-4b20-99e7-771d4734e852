import {RouteTreePage} from './route-tree.page';
import {ComponentHarness} from '@test/harness/component-harness';
import {BsModalService} from 'ngx-bootstrap/modal';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {RouterTestingModule} from '@angular/router/testing';
import {PageTemplateModule} from '@modules/page-template/page-template.module';
import {SharedModule} from '../../../../shared/shared.module';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {DataTableModule} from '@modules/data-table/data-table.module';
import {RouteTreeDataService} from '../../shared/data-services/route-tree-data.service';
import {RouteTreeDataServiceHarness} from '../../shared/data-services/route-tree-data.service.harness';
import {CapGroupDataService} from '../../shared/data-services/cap-group-data.service';
import {CapGroupDataServiceHarness} from '../../shared/data-services/cap-group-data.service.harness';
import {of} from 'rxjs';
import {RouteTree} from '@homeModels/route-tree';
import {RouteTreeSimpleBuilder} from '@test/test-builders';

describe('RouteTreePage', () => {
    let ha: ComponentHarness<RouteTreePage>;

    beforeEach(() => {
        ha = new ComponentHarness(RouteTreePage, {
            declarations: [
                SelectByLanguagePipe
            ],
            imports: [
                RouteTreePage,
                TranslateTestingModule,
                RouterTestingModule,
                PageTemplateModule,
                SharedModule,
                DataTableModule
            ],
            providers: [
                BsModalService,
                {provide: CapGroupDataService, useValue: CapGroupDataServiceHarness},
                {provide: RouteTreeDataService, useValue: RouteTreeDataServiceHarness},
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
            detectChanges: false
        });

        const routeNodes = [
            new RouteTreeSimpleBuilder({
                nodeCode: 'node1',
                name: 'Node 1',
            }),
            new RouteTreeSimpleBuilder({
                nodeCode: 'node2',
                name: 'Node 2'
            }),
            new RouteTreeSimpleBuilder({
                nodeCode: 'node3',
                name: 'Node 3'
            })
        ];

        spyOn(RouteTreeDataServiceHarness, 'getRouteNodes').and.returnValue(of(routeNodes));
    });

    it('should create', () => {
        ha.detectChanges();

        expect(ha.component).toBeTruthy();
    });

    it('should fetch route tree by type and update locks state', () => {
        const mockRouteType = RouteTree.Type.RLC;
        const mockRouteTree = {
            nodeType: mockRouteType,
            name: 'RLC'
        } as any;
        spyOn(RouteTreeDataServiceHarness, 'getRouteTreeLock').and.returnValue(of({}));
        spyOn(RouteTreeDataServiceHarness, 'getRouteTreeByType').and.returnValue(of(mockRouteTree));

        ha.component['_fetchLatestRouteTreeAndLocks'](mockRouteType);

        expect(RouteTreeDataServiceHarness.getRouteTreeByType).toHaveBeenCalledWith(mockRouteType);
        expect(ha.component['_pageData'].content[mockRouteType]).toEqual(mockRouteTree);
    });
});
