<app-page-panel [filterProperty]="filterProperty">
    <ng-container *breadcrumb>{{ translateKeys.products | translate }}</ng-container>

    <app-panel-content>
        <div class="w-100 h-100 position-absolute" #tableContainer>
            <app-data-table
                [isPageDataLoaded]="isPageDataLoaded()"
                [dataSource]="dataSource"
                [filterProperty]="filterProperty"
                [noDataRowText]="noDataRowText"
                [columns]="columns"
                [dataTableColumns]="getDataTableColumns()">

                <ng-container *rowActionBtns="let row">
                    <button
                        *hasPermission="permissions.PRODUCT_DETAILS_PERMISSION"
                        mat-menu-item
                        (click)="getProductDetails(row)">
                        <mat-icon>feed</mat-icon>
                        <span>{{ translateKeys.detail | translate }}</span>
                    </button>
                    @if (isNotCreated(row) && isNotDetailed(row)) {
                        <button
                            *hasPermission="permissions.UPDATE_PRODUCT_PERMISSION"
                            mat-menu-item
                            enableIfTradingSession
                            [disabled]="isNotPersisted(row)"
                            (click)="openUpdateProductDialog(row)">
                            <mat-icon>edit</mat-icon>
                            <span>{{ translateKeys.edit | translate }}</span>
                        </button>
                        <button
                            *hasPermission="permissions.DELETE_PRODUCT_PERMISSION"
                            mat-menu-item
                            enableIfTradingSession
                            [disabled]="isNotPersisted(row)"
                            (confirm)="deleteProduct(row)">
                            <mat-icon>delete</mat-icon>
                            <span>{{ translateKeys.delete | translate }}</span>
                        </button>
                        <button
                            *hasPermission="permissions.PRODUCT_PURGE_ORDERS_PERMISSION"
                            mat-menu-item
                            enableIfTradingSession
                            (confirm)="purgeOrderByProductId(row)">
                            <mat-icon>auto_delete</mat-icon>
                            <span>{{ translateKeys.purgeOrders | translate }}</span>
                        </button>
                        <button
                            *hasPermission="permissions.CORPORATE_ACTIONS_PERMISSION"
                            mat-menu-item
                            enableIfTradingSession
                            [disabled]="isNotPersisted(row) && !isUpdated(row)"
                            (click)="openCorporateActionDialog(row)">
                            <mat-icon>corporate_fare</mat-icon>
                            <span>{{ translateKeys.corporateAction | translate }}</span>
                        </button>

                        <ng-container *hasPermission="permissions.UPDATE_PRODUCT_SELL_AVAILABILITY">
                            <hr/>
                            <div
                                mat-menu-item
                                (click)="$event.stopPropagation()">
                                <mat-slide-toggle
                                    [(ngModel)]="toProduct(row).availableForSell"
                                    (ngModelChange)="toggleAvailableForSell(row)">
                                    {{ translateKeys.availableForSell | translate }}
                                </mat-slide-toggle>
                            </div>
                        </ng-container>
                    }
                </ng-container>

                <ng-container customColumn="futureChanges">
                    <ng-container *customCellDef="let row; let i = index">
                        @if (isUpdated(row)) {
                            <div class="expand-arrow" (click)="toggleOpenDetailRow(row, i)">
                                <mat-icon
                                    class="expand-arrow__icon"
                                    [class.expand-arrow__icon--expanded]="row.isOpen">chevron_right
                                </mat-icon>
                            </div>
                        }
                    </ng-container>
                </ng-container>
            </app-data-table>

            <mat-paginator
                *paginator
                [ngClass]="paginatorClass"
                [length]="dataSourceLength"
                [pageSize]="dataSourcePageSize"
                [showFirstLastButtons]="true">
            </mat-paginator>
        </div>
    </app-panel-content>
</app-page-panel>
