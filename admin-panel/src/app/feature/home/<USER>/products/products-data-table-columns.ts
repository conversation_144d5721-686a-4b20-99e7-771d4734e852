import {DataTableColumn} from '@models/data-table';
import {TranslateKey} from '@shared/enums/translate-key';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {Product} from '@homeModels/product';
import {MbDatePipe} from '@modules/datepicker/mb-date.pipe';
import {UtilConstants} from '@constants/util-constants';
import {InputFilter} from '@modules/filter/input/input-filter';
import {RangeFilter} from '@modules/filter/range/range-filter';
import {GhostStatusColumnClass} from '../../shared/classes/ghost-status-data-table/ghost-status-column-class';
import {CustomDecimalPipe} from '@shared/pipes/custom-decimal.pipe';
import {BilingualPipe} from '@shared/modules/shared-declarations/select-by-language/bilingual.pipe';
import {GhostStatus} from '@models/ghost-status';
import {RangeDatepickerFilter} from '@modules/filter/range-datepicker-filter/range-datepicker-filter';
import {AutoCompleteFilter} from '@modules/filter/auto-complete-filter/auto-complete-filter';
import {TranslatePipe} from '@ngx-translate/core';
import {SelectOptionFilter} from '@modules/filter/select-option/select-option-filter';
import {enumToKeyValue, toCamelCase} from '@core/utils';
import {InputMasks} from '@constants/input-masks';

export function productsDataTableColumns(): DataTableColumn<Product.Simple>[] {
    return [
        {
            title: TranslateKey.futureChanges,
            minWidth: 120,
            hasTemplate: true,
            class: GhostStatusColumnClass,
            value() { },
            filter: new AutoCompleteFilter({
                options: enumToKeyValue(GhostStatus),
                queryParam: 'ghostStatus',
                filterField: 'key',
                formatOptionTitle(data) { return toCamelCase(data.key); }
            })
        },
        {
            title: TranslateKey.productId,
            class: [GhostStatusColumnClass, 'monospace'],
            value(data) { return data.productId },
            filter: new InputFilter<string>({
                queryParam: 'productId'
            })
        },
        {
            title: TranslateKey.mnemonic,
            pipeToken: BilingualPipe,
            isSticky: true,
            class: GhostStatusColumnClass,
            value(data) { return data.mnemonic },
            filter: new InputFilter<string>({
                queryParam: 'mnemonic'
            })
        },
        {
            title: TranslateKey.name,
            pipeToken: SelectByLanguagePipe,
            class: GhostStatusColumnClass,
            value(data) { return data.name },
            filter: new InputFilter<string>({
                queryParam: 'name'
            })
        },
        {
            title: TranslateKey.company,
            class: [GhostStatusColumnClass, 'monospace'],
            value(data) { return data.company?.code },
            filter: new InputFilter<string>({
                queryParam: 'companyCode'
            })
        },
        {
            title: TranslateKey.productCode,
            class: [GhostStatusColumnClass, 'monospace'],
            value(data) { return data.productCode },
            filter: new InputFilter<string>({
                queryParam: 'productCode'
            })
        },
        {
            title: TranslateKey.board,
            value(data) { return data.boardCode + (data?.board ? ' - ' + data.board?.name : '') },
            class: GhostStatusColumnClass,
            filter: new InputFilter<string>({
                queryParam: 'boardCode'
            })
        },
        {
            title: TranslateKey.productType,
            class: GhostStatusColumnClass,
            value(data) {
                return data.productTypeCode
                    ? data.productTypeCode + (data.productType ? ' - ' + data.productType?.name : '')
                    : ''
            },
            filter: new InputFilter<string>({
                queryParam: 'productTypeCode',
                imask: InputMasks.PRODUCT_TYPE_CODE
            })
        },
        {
            title: TranslateKey.productSubType,
            class: GhostStatusColumnClass,
            value(data) {
                return data.productSubTypeCode
                    ? data.productSubTypeCode + (data.productSubType ? ' - ' + data.productSubType?.name : '')
                    : ''
            },
            filter: new InputFilter<string>({
                queryParam: 'productSubTypeCode'
            })
        },
        {
            title: TranslateKey.marketFlowCode,
            class: GhostStatusColumnClass,
            value(data) { return data.marketFlowCode },
            filter: new InputFilter<string>({
                queryParam: 'marketFlowCode'
            })
        },
        {
            title: TranslateKey.totalShares,
            minWidth: 200,
            pipeToken: CustomDecimalPipe,
            class: GhostStatusColumnClass,
            value(data) { return data.totalShares },
            filter: new RangeFilter({
                queryParam: 'totalShares'
            })
        },
        {
            title: TranslateKey.referencePrice,
            minWidth: 200,
            pipeToken: CustomDecimalPipe,
            class: GhostStatusColumnClass,
            value(data) { return data.referencePrice },
            filter: new RangeFilter({
                queryParam: 'referencePrice'
            })
        },
        {
            title: TranslateKey.availableForSell,
            pipeToken: TranslatePipe,
            class: GhostStatusColumnClass,
            value(data) { return data.availableForSell?.toString() },
            filter: new SelectOptionFilter({
                data: [
                    {key: 'true', value: TranslateKey.yes},
                    {key: 'false', value: TranslateKey.no}
                ],
                queryParam: 'availableForSell',
                queryParamValueField: 'key'
            })
        },
        {
            title: TranslateKey.percentagePrice,
            pipeToken: TranslatePipe,
            class: GhostStatusColumnClass,
            value(data) { return data.isPricePercentage?.toString() },
            filter: new SelectOptionFilter({
                data: [
                    {key: 'true', value: TranslateKey.yes},
                    {key: 'false', value: TranslateKey.no}
                ],
                queryParam: 'isPricePercentage',
                queryParamValueField: 'key'
            })
        },
        {
            title: TranslateKey.creationDate,
            minWidth: 180,
            pipeToken: MbDatePipe,
            class: GhostStatusColumnClass,
            pipeArgs: [UtilConstants.DATE_FORMAT],
            value(data) { return data.creationDate },
            filter: new RangeDatepickerFilter({
                queryParam: 'creationDate'
            })
        },
        {
            title: TranslateKey.updateDate,
            minWidth: 180,
            class: GhostStatusColumnClass,
            pipeToken: MbDatePipe,
            pipeArgs: [UtilConstants.DATE_FORMAT],
            value(data) { return data.updateDate },
            filter: new RangeDatepickerFilter({
                queryParam: 'updateDate'
            })
        }
    ];
}
