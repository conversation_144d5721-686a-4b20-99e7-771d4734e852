import {SystemSessionSchedulesPage} from './system-session-schedules.page';
import {ComponentHarness} from '@test/harness/component-harness';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {RouterTestingModule} from '@angular/router/testing';
import {SharedModule} from '../../../../shared/shared.module';
import {BsModalService} from 'ngx-bootstrap/modal';
import {ScheduleDataService} from '@dataServices/schedule-data.service';
import {scheduleDataServiceHarness} from '@dataServices/schedule-data.service.harness';

describe('SystemSessionSchedulesPage', () => {
    let ha: ComponentHarness<SystemSessionSchedulesPage>;

    beforeEach(() => {
        ha = new ComponentHarness(SystemSessionSchedulesPage, {
            imports: [
                SystemSessionSchedulesPage,
                TranslateTestingModule,
                RouterTestingModule,
                SharedModule
            ],
            providers: [
                BsModalService,
                { provide: ScheduleDataService, useValue: scheduleDataServiceHarness }
            ],
            detectChanges: false
        });

        ha.detectChanges();
    });


    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });

    it('should call _refreshPage and not _refreshPageData in ngAfterViewInit', () => {
        const refreshPageSpy = spyOn<any>(ha.component, '_refreshPage');
        const refreshPageDataSpy = spyOn<any>(ha.component, '_refreshPageData');

        ha.component.ngAfterViewInit();

        expect(refreshPageSpy).toHaveBeenCalledTimes(1);
        expect(refreshPageDataSpy).not.toHaveBeenCalled();
    });
});
