<app-page-panel [filterProperty]="filterProperty">
    <ng-container *breadcrumb>{{ translateKeys.systemSessionSchedules | translate }}</ng-container>

    <app-panel-content>
        <div class="w-100 h-100 position-absolute" #tableContainer>
            <app-data-table
                [isPageDataLoaded]="isPageDataLoaded()"
                [dataSource]="dataSource"
                [filterProperty]="filterProperty"
                [noDataRowText]="noDataRowText"
                [columns]="columns"
                [dataTableColumns]="getDataTableColumns()">

                <ng-container *rowActionBtns="let row">
                    <button
                        *hasPermission="permissions.UPDATE_GROUP_SESSION_SCHEDULE_PERMISSION"
                        mat-menu-item
                        [disabled]="!isDateToday(row)"
                        (click)="editSessionSchedule(row)">
                        <mat-icon> edit </mat-icon>
                        {{ translateKeys.edit | translate }}
                    </button>

                    <button
                        *hasPermission="permissions.UPDATE_GROUP_SESSION_SCHEDULE_PERMISSION"
                        mat-menu-item
                        [disabled]="!isDateToday(row)"
                        (click)="deleteSessionSchedule()">
                        <mat-icon> delete </mat-icon>
                        {{ translateKeys.delete | translate }}
                    </button>
                </ng-container>

                <ng-container customColumn="sessionSchedules">
                    <ng-container *customCellDef="let row">
                        @for (action of row.actions; track action) {
                            <span>
                                [
                                {{action.runAt }} =>
                                {{action.targetState | camelCase | translate}} -
                                ]
                        </span>
                        }
                    </ng-container>
                </ng-container>
            </app-data-table>

            <mat-paginator
                *paginator
                [ngClass]="paginatorClass"
                [length]="dataSourceLength"
                [pageSize]="dataSourcePageSize"
                [showFirstLastButtons]="true">
            </mat-paginator>
        </div>
    </app-panel-content>
</app-page-panel>
