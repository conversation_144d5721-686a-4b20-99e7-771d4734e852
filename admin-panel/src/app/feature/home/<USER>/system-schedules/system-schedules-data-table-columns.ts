import {DataTableColumn} from "@models/data-table";
import {SystemSchedule} from "@models/schedule";
import {TranslateKey} from "@enums/translate-key";
import {MbDatePipe} from "@modules/datepicker/mb-date.pipe";
import {UtilConstants} from "@constants/util-constants";
import {RangeDatepickerFilter} from "@modules/filter/range-datepicker-filter/range-datepicker-filter";

export function SystemSchedulesDataTableColumns(): DataTableColumn<SystemSchedule>[] {
    return [
        {
            title: TranslateKey.from,
            minWidth: 180,
            pipeToken: MbDatePipe,
            pipeArgs: [UtilConstants.DATE_FORMAT],
            value(data) { return data.from },
            filter: new RangeDatepickerFilter({
                queryParam: 'from'
            })
        }
    ];
}
