<app-page-panel [filterProperty]="filterProperty">
    <ng-container *breadcrumb>{{ translateKeys.shareholderPositions | translate }}</ng-container>
    <ng-container *breadcrumb>{{ shareholderId }}</ng-container>
    <app-panel-title>{{ translateKeys.shareholderPositions | translate }}</app-panel-title>

    <button
        mat-button
        *actionBtn
        blockShareholderDialog
        [shareholder]="shareholder"
        [shareholderPositions]="dataSource">
        <mat-icon color="primary">block</mat-icon>
        {{ translateKeys.block | translate }}
    </button>

    <button
        mat-button
        *actionBtn
        transferShareDialog
        [shareholder]="shareholder">
        <mat-icon color="primary">swap_vert</mat-icon>
        {{ translateKeys.transferShare | translate }}
    </button>

    <app-panel-content>
        <div class="w-100 h-100 position-absolute" #tableContainer>
            <app-data-table
                [isPageDataLoaded]="isPageDataLoaded()"
                [dataSource]="dataSource"
                [filterProperty]="filterProperty"
                [noDataRowText]="noDataRowText"
                [columns]="columns"
                [dataTableColumns]="getDataTableColumns()">

                <!-- Blocked Ownership -->
                <ng-container customColumn="blockedOwnership">
                    <ng-container *customCellDef="let row">
                        @if (row.isEditBlockedOwnership) {
                            <input
                                class="form-control"
                                [(ngModel)]="toShareholderPosition(row).blockedOwnership"
                                confirmAction="blur"
                                type="number"
                                (confirm)="row.isEditBlockedOwnership = false; updateBlockedOwnership(row)">
                        } @else {
                            <div class="d-flex justify-content-between">
                                {{ toShareholderPosition(row).blockedOwnership | number }}
                                <mat-icon (click)="row.isEditBlockedOwnership = true;">edit</mat-icon>
                            </div>
                        }
                    </ng-container>
                </ng-container>

                <!-- Is Buy Blocked -->
                <ng-container customColumn="blockedBuySide">
                    <ng-container *customCellDef="let row">
                        <mat-checkbox
                            (confirm)="updateBlockedBuySide(row)"
                            [(ngModel)]="toShareholderPosition(row).isBuyBlocked"></mat-checkbox>
                    </ng-container>
                </ng-container>

                <!-- Is Sell Blocked -->
                <ng-container customColumn="blockedSellSide">
                    <ng-container *customCellDef="let row">
                        <mat-checkbox
                            (confirm)="updateBlockedSellSide(row)"
                            [(ngModel)]="toShareholderPosition(row).isSellBlocked"></mat-checkbox>
                    </ng-container>
                </ng-container>
            </app-data-table>

            <mat-paginator
                *paginator
                [ngClass]="paginatorClass"
                [length]="dataSourceLength"
                [pageSize]="dataSourcePageSize"
                [showFirstLastButtons]="true">
            </mat-paginator>
        </div>
    </app-panel-content>
</app-page-panel>
