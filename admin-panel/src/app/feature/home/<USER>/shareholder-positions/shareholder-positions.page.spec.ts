import {TestBed} from '@angular/core/testing';
import {ShareholderPositionsPage} from './shareholder-positions.page';
import {Http} from '@http';
import {HttpClient} from '@angular/common/http';
import {RouterTestingModule} from '@angular/router/testing';
import {HttpClientTestingModule} from '@angular/common/http/testing';
import {BlockShareholderDialogDirective} from '@directives/block-shareholder-dialog.directive';
import {TransferShareDialogDirective} from '@directives/transfer-share-dialog.directive';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {SharedModule} from '../../../../shared/shared.module';
import {PageTemplateModule} from '@modules/page-template/page-template.module';
import {TestUtils} from '@test/test-utils';
import {StoreService} from '@services/store.service';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {SharedDeclarations} from '@modules/shared-declarations/shared-declarations';
import {ComponentHarness} from '@test/harness/component-harness';
import {BsModalService, ModalModule} from 'ngx-bootstrap/modal';
import {TranslateKey} from '@enums/translate-key';
import {ShareholderPositionsDataTableColumns} from './shareholder-positions-data-table-columns';
import {DataTableModule} from '@modules/data-table/data-table.module';

describe('ShareholderPositionsPage', () => {
    let ha: ComponentHarness<ShareholderPositionsPage>;

    beforeEach(() => {
        ha = new ComponentHarness(ShareholderPositionsPage, {
            declarations: [
                BlockShareholderDialogDirective,
                TransferShareDialogDirective,
                SelectByLanguagePipe
            ],
            imports: [
                ShareholderPositionsPage,
                TranslateTestingModule,
                HttpClientTestingModule,
                RouterTestingModule,
                ModalModule.forRoot(),
                SharedModule,
                PageTemplateModule,
                SharedDeclarations,
                DataTableModule
            ],
            providers: [BsModalService],
            detectChanges: false
        });

        Http.httpClient = TestBed.inject(HttpClient);
        StoreService.instruments = TestUtils.getInstruments().content;

        (ha.component as any)._pageData = TestUtils.getPositions();
        ha.fixture.detectChanges();
    });

    it('should create', () => {
        ha.detectChanges();
        expect(ha.component).toBeTruthy();
    });

    it('should "productId" column has "monospace" class', () => {
        ha.detectChanges();
        const column = ShareholderPositionsDataTableColumns()
            .find(column => column.title === TranslateKey.productId);

        expect(column.class).toContain('monospace');
    });
});
