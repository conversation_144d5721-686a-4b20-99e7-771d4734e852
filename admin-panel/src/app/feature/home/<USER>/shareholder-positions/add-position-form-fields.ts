import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {Valida<PERSON>} from '@angular/forms';
import {FormFields} from '@modules/dynamic-form/form-field';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';
import {InputMasks} from '@constants/input-masks';
import {ReadOnlyFormField} from '@modules/dynamic-form/form-fields/read-only-field/read-only-form-field';
import {Order} from '@models/order/order';
import {FieldsPlaceholder} from '../../../../shared/constants/fields-placeholder';

export function addPositionFormFields(): FormFields {
    return [
        new InputFormField({
            formControlName: 'productId',
            label: TranslateKey.product,
            width: 12,
            validations: [Validators.required],
            placeholder: FieldsPlaceholder.ISIN,
            mask: InputMasks.ISIN
        }),

        new ReadOnlyFormField({
            formControlName: 'blockedOwnership',
            value: '0'
        }),

        {
            formArrayName: 'blockedSides',
            formFields: [
                [
                    new ReadOnlyFormField({
                        formControlName: 'blocked',
                        value: false
                    }),
                    new ReadOnlyFormField({
                        formControlName: 'side',
                        value: Order.Side.BUY
                    })
                ],
                [
                    new ReadOnlyFormField({
                        formControlName: 'blocked',
                        value: false
                    }),
                    new ReadOnlyFormField({
                        formControlName: 'side',
                        value: Order.Side.SELL
                    })
                ]
            ]
        }
    ];
}
