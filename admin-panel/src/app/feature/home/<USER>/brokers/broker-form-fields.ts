import {Translate<PERSON><PERSON>} from '@shared/enums/translate-key';
import {Validators} from '@angular/forms';
import {InputMasks} from '@constants/input-masks';
import {Town} from '@models/town';
import {FieldsPlaceholder} from '../../../../shared/constants/fields-placeholder';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';
import {ReadOnlyFormField} from '@modules/dynamic-form/form-fields/read-only-field/read-only-form-field';
import {AutoCompleteFormField} from '@modules/dynamic-form/form-fields/auto-complete-field/auto-complete-form-field';
import {CustomValidators} from '@constants/custom-validators';
import {<PERSON>roke<PERSON>} from '@homeModels/broker';
import {FormFields} from '@modules/dynamic-form/form-field';

export function brokerFormFields(broker?: Broker.Simple): FormFields {
    return [
        new InputFormField({
            formControlName: 'brokerId',
            label: TranslateKey.brokerId,
            validations: [Validators.required, CustomValidators.fixedLength(3)],
            width: 12,
            mask: InputMasks.BROKER_ID,
            value: broker?.id,
            disable: !!broker,
            placeholder: FieldsPlaceholder.BROKER_ID
        }),

        new InputFormField({
            formControlName: 'name',
            label: TranslateKey.name,
            validations: [Validators.required],
            width: 12,
            value: broker?.name?.fa,
            placeholder: FieldsPlaceholder.BROKER_NAME
        }),

        new InputFormField({
            formControlName: 'credit',
            label: TranslateKey.credit,
            width: 12,
            validations: [Validators.required],
            mask: InputMasks.MAX_FLOAT_NUMBER(13),
            value: broker?.credit,
            placeholder: FieldsPlaceholder.SEPERATED_NUMBER,
            disable: !!broker
        }),

        new InputFormField({
            formControlName: 'address',
            label: TranslateKey.address,
            validations: [],
            width: 12,
            mask: InputMasks.ADDRESS,
            value: broker?.address
        }),

        new InputFormField({
            formControlName: 'billingAddress',
            label: TranslateKey.billingAddress,
            width: 12,
            mask: InputMasks.ADDRESS,
            value: broker?.billingAddress
        }),

        new InputFormField({
            formControlName: 'contactName',
            label: TranslateKey.contactName,
            validations: [],
            width: 12,
            value: broker?.contactName,
            placeholder: FieldsPlaceholder.CONTACT_NAME
        }),

        new InputFormField({
            formControlName: 'bankCode',
            label: TranslateKey.bankCode,
            validations: [Validators.required],
            width: 12,
            mask: InputMasks.BANK_CODE,
            value: broker?.bankCode,
            disable: true,
            placeholder: FieldsPlaceholder.BANK_CODE
        }),

        new ReadOnlyFormField({
            formControlName: 'countryCode',
            value: 'IR'
        }),

        new AutoCompleteFormField<Town>({
            formControlName: 'townCode',
            label: TranslateKey.townCode,
            validations: [Validators.required],
            width: 12,
            value: broker?.townCode,
            placeholder: FieldsPlaceholder.TOWN_CODE,
            searchExpField: 'searchExpression',
            options: [],
            getTitle(option): string { return option ? `${option.code} - ${option.name.en} - ${option.name.fa}` : ''; },
            optionFieldName: 'code',
            panelWidth: 'auto'
        }),

        new InputFormField({
            formControlName: 'branchCode',
            label: TranslateKey.branchCode,
            validations: [Validators.required],
            width: 12,
            mask: InputMasks.BRANCH_CODE,
            value: broker?.branchCode,
            placeholder: FieldsPlaceholder.BRANCH_CODE
        })
    ];
}
