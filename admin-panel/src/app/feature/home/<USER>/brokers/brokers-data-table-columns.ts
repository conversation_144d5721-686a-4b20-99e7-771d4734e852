import {DataTableColumn} from '@models/data-table';
import {TranslateKey} from '@shared/enums/translate-key';
import {TranslatePipe} from '@ngx-translate/core';
import {InputFilter} from '@modules/filter/input/input-filter';
import {SelectOptionFilter} from '@modules/filter/select-option/select-option-filter';
import {RangeFilter} from '@modules/filter/range/range-filter';
import {CustomDecimalPipe} from '@shared/pipes/custom-decimal.pipe';
import {Broker} from '@homeModels/broker';
import {InputMasks} from '@constants/input-masks';

export function brokersDataTableColumns(): DataTableColumn<Broker.Simple>[] {
    return [
        {
            title: TranslateKey.code,
            isSticky: true,
            value(data) { return data.id },
            filter: new InputFilter({
                queryParam: 'id',
                imask: InputMasks.BROKER_ID
            })
        },
        {
            title: TranslateKey.name,
            minWidth: 250,
            isSticky: true,
            value(data) { return data.name.fa },
            filter: new InputFilter({
                queryParam: 'name'
            })
        },
        {
            title: TranslateKey.buySideIsBlocked,
            pipeToken: TranslatePipe,
            value(data) { return data.isBuyBlocked.toString() },
            filter: new SelectOptionFilter({
                data: [
                    {key: 'true', value: TranslateKey.yes},
                    {key: 'false', value: TranslateKey.no}
                ],
                queryParam: 'isBuyBlocked',
                queryParamValueField: 'key'
            })
        },
        {
            title: TranslateKey.sellSideIsBlocked,
            pipeToken: TranslatePipe,
            value(data) { return data.isSellBlocked.toString() },
            filter: new SelectOptionFilter({
                data: [
                    {key: 'true', value: TranslateKey.yes},
                    {key: 'false', value: TranslateKey.no}
                ],
                queryParam: 'isSellBlocked',
                queryParamValueField: 'key'
            })
        },
        {
            title: TranslateKey.creditCheckingStatus,
            pipeToken: TranslatePipe,
            value(data) { return data.creditCheckingStatus.toString() },
            filter: new SelectOptionFilter({
                data: [
                    {key: 'true', value: TranslateKey.yes},
                    {key: 'false', value: TranslateKey.no}
                ],
                queryParam: 'creditCheckingStatus',
                queryParamValueField: 'key'
            })
        },
        {
            title: TranslateKey.credit,
            minWidth: 200,
            pipeToken: CustomDecimalPipe,
            value(data) { return data.credit },
            filter: new RangeFilter({
                queryParam: 'credit'
            })
        },
        {
            title: TranslateKey.contactName,
            value(data) { return data.contactName },
            filter: new InputFilter<string>({
                queryParam: 'contactName'
            })
        },
        {
            title: TranslateKey.countryCode,
            value(data) { return data.countryCode },
            filter: new InputFilter<string>({
                queryParam: 'countryCode',
                imask: InputMasks.COUNTER_CODE
            })
        },
        {
            title: TranslateKey.townCode,
            value(data) { return data.townCode },
            filter: new InputFilter<string>({
                queryParam: 'townCode',
                imask: InputMasks.TOWN_CODE
            })
        },
        {
            title: TranslateKey.bankCode,
            value(data) { return data.bankCode },
            filter: new InputFilter<string>({
                queryParam: 'bankCode',
                imask: InputMasks.BANK_CODE
            })
        },
        {
            title: TranslateKey.branchCode,
            value(data) { return data.branchCode },
            filter: new InputFilter<string>({
                queryParam: 'branchCode',
                imask: InputMasks.BRANCH_CODE
            })
        },
        {
            title: TranslateKey.address,
            value(data) { return data.address },
            filter: new InputFilter<string>({
                queryParam: 'address'
            })
        },
        {
            title: TranslateKey.billingAddress,
            value(data) { return data.billingAddress },
            filter: new InputFilter<string>({
                queryParam: 'billingAddress'
            })
        }
    ];
}
