import {UpdateGroupStaticThresholdDialogComponent} from './update-group-static-threshold-dialog.component';
import {ComponentHarness} from '@test/harness/component-harness';
import {ModalHeaderComponent} from '@modules/shared-declarations/modal-header/modal-header.component';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {RequiredErrComponent} from '../../components/required-err/required-err.component';
import {FormControl, FormGroup, ReactiveFormsModule, Validators} from '@angular/forms';
import {HttpClientTestingModule} from '@angular/common/http/testing';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {SharedDeclarations} from '@modules/shared-declarations/shared-declarations';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {GroupDataService} from '@dataServices/group-data.service';
import {groupDataServiceHarness} from '@dataServices/group-data.service.harness';
import {TestUtils} from '@test/test-utils';
import {StaticThreshold} from '@models/static-threshold';
import {CustomValidators} from '@constants/custom-validators';
import {of} from 'rxjs';

describe('UpdateGroupStaticThresholdDialogComponent', () => {
    let ha: ComponentHarness<UpdateGroupStaticThresholdDialogComponent>;
    let bsModalRefMock = { hide() {} };

    beforeEach(() => {
        ha = new ComponentHarness(UpdateGroupStaticThresholdDialogComponent, {
            declarations: [
                UpdateGroupStaticThresholdDialogComponent,
                ModalHeaderComponent,
                SelectByLanguagePipe,
                RequiredErrComponent
            ],
            imports: [
                ReactiveFormsModule,
                HttpClientTestingModule,
                TranslateTestingModule,
                SharedDeclarations
            ],
            providers: [
                {provide: BsModalRef, useValue: bsModalRefMock},
                {provide: GroupDataService, useValue: groupDataServiceHarness}
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
            detectChanges: false
        });

        ha.component.group = TestUtils.getGroup();

        ha.component['_formGroup'] = new FormGroup<StaticThreshold.PercentageForm>({
            priceBandPercentage: new FormGroup<any>({
                upperBoundPercentage: new FormControl('', [Validators.required, CustomValidators.exclusiveRange(0, 100)]),
                lowerBoundPercentage: new FormControl('', [Validators.required, CustomValidators.exclusiveRange(0, 100)])
            })
        });
    });

    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });

    it('should call #updateStaticThreshold function when clicking "Submit" button', async () => {
        spyOn(bsModalRefMock, 'hide');
        spyOn(groupDataServiceHarness, 'updateStaticThreshold').and.returnValue(of(null));

        await ha.detectChanges();

        ha.component.formGroup.get('priceBandPercentage').get('upperBoundPercentage').setValue(10);
        ha.component.formGroup.get('priceBandPercentage').get('lowerBoundPercentage').setValue(30);
        ha.component.formGroup.markAsTouched();
        await ha.detectChanges();

        ha.get('button').thatContains('Submit').click();

        expect(groupDataServiceHarness.updateStaticThreshold).toHaveBeenCalled();
        expect(bsModalRefMock.hide).toHaveBeenCalled();
    });
});
