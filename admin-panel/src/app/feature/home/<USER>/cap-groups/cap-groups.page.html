<app-page-panel [filterProperty]="filterProperty">
    <ng-container *breadcrumb>{{ translateKeys.capGroups | translate }}</ng-container>

    <app-panel-content>
        <div class="w-100 h-100 position-absolute pt-3 border-top overflow-auto" (click)="closeNodeDetail()">
            <mat-tree [dataSource]="dataSource" [treeControl]="treeControl" class="route-tree">
                <!-- This is the tree node template for leaf nodes -->
                <!-- There is inline padding applied to this node using styles.
                  This padding value depends on the mat-icon-button width. -->
                <mat-tree-node
                    *matTreeNodeDef="let node"
                    matTreeNodeToggle
                    (contextmenu)="openNodeDetail($event, node)">
                    <mat-icon class="mat-icon-rtl-mirror mr-2" color="primary">folder</mat-icon>
                    {{ toRouteTree(node).capGroupName }}
                </mat-tree-node>
                <!-- This is the tree node template for expandable nodes -->
                <mat-nested-tree-node *matTreeNodeDef="let node; when: hasChild">
                    <div class="mat-tree-node"
                         (contextmenu)="openNodeDetail($event, node)">
                        <button
                            (click)="closeNodeDetail()"
                            mat-icon-button
                            matTreeNodeToggle>
                            <mat-icon class="mat-icon-rtl-mirror" color="primary">
                                {{ treeControl.isExpanded(node) ? 'folder_open' : 'topic' }}
                            </mat-icon>
                        </button>
                        {{ toRouteTree(node).capGroupName }}
                    </div>
                    <!-- There is inline padding applied to this div using styles.
                        This padding value depends on the mat-icon-button width.  -->
                    <div [class.example-tree-invisible]="!treeControl.isExpanded(node)"
                         role="group">
                        <ng-container matTreeNodeOutlet></ng-container>
                    </div>
                </mat-nested-tree-node>
            </mat-tree>

        </div>
    </app-panel-content>
</app-page-panel>


<div class="card node-menu bg-white" dir="ltr" *ngIf="nodeValue()" [ngStyle]="nodeStyle()">
    <div class="card-body">
        <small><strong>Subscriber Group Name: </strong>{{ nodeValue().capGroupName }}</small><br/>
    </div>

    <div class="card-footer">
        <button
            *hasPermission="permissions.ADD_SUB_GROUP_PERMISSION"
            mat-button
            class="w-auto"
            (click)="openAddCapDialog()">Add Subscriber
        </button>

        <button
            *hasPermission="permissions.ADD_SUB_GROUP_PERMISSION"
            mat-button
            class="w-auto"
            (click)="openAddChildDialog()">Add Sub Group
        </button>

        <button
            mat-button
            style="width: 36px; min-width: unset"
            [matMenuTriggerFor]="menu">
            <mat-icon>more_vert</mat-icon>
        </button>
        <mat-menu #menu="matMenu">
            <button
                *hasPermission="permissions.BIND_ROUTE_NODE_PERMISSION"
                mat-menu-item
                (click)="openBindRouteNodeDialog()">
                <mat-icon>commit</mat-icon>
                <span>Bind Route Node</span>
            </button>
            <button
                *hasPermission="permissions.UNBIND_ROUTE_NODE_PERMISSION"
                mat-menu-item
                (click)="openUnbindRouteNodeDialog()">
                <mat-icon>line_start_circle</mat-icon>
                <span>Unbind Route Node</span>
            </button>
            <button
                *hasPermission="permissions.CAP_GROUP_DETAILS_PERMISSION"
                mat-menu-item
                (click)="openCapGroupDetailsDialog()">
                <mat-icon>list</mat-icon>
                <span>Details</span>
            </button>
            <button
                *hasPermission="permissions.DELETE_CAP_GROUP_PERMISSION"
                mat-menu-item
                (confirm)="removeNode()">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
            </button>
        </mat-menu>
    </div>
</div>
