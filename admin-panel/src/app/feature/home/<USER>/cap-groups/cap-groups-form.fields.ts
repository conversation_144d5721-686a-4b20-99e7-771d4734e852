import {Validators} from '@angular/forms';
import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';
import {FormFields} from '@modules/dynamic-form/form-field';
import {FieldsPlaceholder} from '../../../../shared/constants/fields-placeholder';
import {SelectOptionFormField} from '@modules/dynamic-form/form-fields/select-option-field/select-option-form-field';
import {RouteTree} from '@homeModels/route-tree';
import {enumToKeyValue} from '@core/utils';
import {MultiPickField} from '@modules/dynamic-form/form-fields/multi-pick-field/multi-pick-field';
import {Cap} from '@homeModels/cap';

export function capGroupFormFields(): FormFields {
    return [
        new InputFormField({
            formControlName: 'capGroupName',
            label: TranslateKey.name,
            width: 12,
            validations: [Validators.required],
            placeholder: FieldsPlaceholder.CAP_GROUP_NAME
        })
    ];
}

export function capFormFields(capGroupName: string, caps: Cap.Simple[]): FormFields {
    return [
        new MultiPickField<Cap.Simple>({
            formControlName: 'accountIds',
            label: TranslateKey.name,
            width: 12,
            searchExpField: 'capName',
            optionFieldName: 'accountId',
            list: caps,
            value: caps.filter(cap => cap.capGroupNames.includes(capGroupName)),
            getTitle(item): any { return item.capName },
            placeholder: FieldsPlaceholder.CAP_NAME
        }),
    ];
}

export function bindRouteNodeFormFields(): FormFields {
    return [
        new SelectOptionFormField({
            formControlName: 'nodeType',
            label: TranslateKey.type,
            width: 12,
            validations: [Validators.required],
            placeholder: FieldsPlaceholder.ROUTE_TREE_TYPE,
            options: enumToKeyValue(RouteTree.Type),
            optionFieldName: 'key',
            getTitle(option): string { return option.key; }
        }),

        new SelectOptionFormField<RouteTree.Simple>({
            formControlName: 'routeNodeCode',
            label: TranslateKey.routeNodeCode,
            width: 12,
            validations: [Validators.required],
            placeholder: FieldsPlaceholder.BINDING_CODE,
            options: [],
            disable: true,
            optionFieldName: 'nodeCode',
            getTitle(option): string { return `${option.name} (${option.nodeCode})`; }
        })
    ];
}

export function unbindRouteNodeFormFields(routeNodeBindings: Cap.RouteBindingEntry[]): FormFields {
    return [
        new SelectOptionFormField<Cap.RouteBindingEntry>({
            formControlName: 'routeNodeCode',
            label: TranslateKey.routeNodeCode,
            width: 12,
            validations: [Validators.required],
            placeholder: FieldsPlaceholder.BINDING_CODE,
            optionFieldName: 'routeNodeCode',
            options: routeNodeBindings,
            getTitle(data): string { return data.bindingTitle; }
        })
    ];
}
