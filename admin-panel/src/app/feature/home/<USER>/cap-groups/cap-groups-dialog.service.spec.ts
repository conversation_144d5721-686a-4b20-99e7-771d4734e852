import {CapGroupsDialogService} from './cap-groups-dialog.service';
import {BsModalService} from 'ngx-bootstrap/modal';
import {DynamicFormDialogService} from '../../dialogs/dynamic-form-dialog/dynamic-form-dialog.service';
import {CapGroupDataService} from '../../shared/data-services/cap-group-data.service';
import {of} from 'rxjs';
import {CapGroup} from '@homeModels/cap-group';
import {Cap} from '@homeModels/cap';
import {CapBuilder, CapGroupSimpleBuilder, RouteTreeSimpleBuilder} from '@test/test-builders';
import {ServiceHarness} from '@test/harness/service-harness';
import {CapGroupDataServiceHarness} from '../../shared/data-services/cap-group-data.service.harness';
import {TranslateKey} from '@enums/translate-key';
import {TableGroupDialogComponent} from '../../dialogs/table-group-dialog/table-group-dialog.component';
import {TableGroupDialog} from '../../dialogs/table-group-dialog/table-group-dialog';
import {CapRouteBindingEntryBuilder} from '@test/test-builders/cap-route-binding-entry.builder';

describe('CapGroupsDialogService', () => {
    let ha: ServiceHarness<CapGroupsDialogService>;
    let modalService: jasmine.SpyObj<BsModalService>;
    let dynamicFormDialogService: jasmine.SpyObj<DynamicFormDialogService>;

    const mockCapGroup: CapGroup.Simple = new CapGroupSimpleBuilder({
        capGroupName: 'testGroup',
        caps: [new CapBuilder()],
        routeNodeBindings: {
            type1: [
                new CapRouteBindingEntryBuilder({routeNodeCode: 'node1', isInherited: false}),
                new CapRouteBindingEntryBuilder({routeNodeCode: 'node2', isInherited: true})
            ],
            type2: [
                new CapRouteBindingEntryBuilder({routeNodeCode: 'node3', isInherited: false})
            ]
        }
    });

    beforeEach(() => {
        modalService = jasmine.createSpyObj('BsModalService', ['show', 'hide']);
        dynamicFormDialogService = jasmine.createSpyObj('DynamicFormDialogService', ['open']);

        ha = new ServiceHarness(CapGroupsDialogService, {
            providers: [
                CapGroupsDialogService,
                {provide: BsModalService, useValue: modalService},
                {provide: DynamicFormDialogService, useValue: dynamicFormDialogService},
                {provide: CapGroupDataService, useValue: CapGroupDataServiceHarness}
            ]
        });

        spyOn(CapGroupDataServiceHarness, 'getCapGroup').and.returnValue(of(mockCapGroup));
    });

    it('should be created', () => {
        expect(ha.service).toBeTruthy();
    });

    it('should set route nodes', () => {
        const newRouteNodes = [new RouteTreeSimpleBuilder()];

        ha.service.setRouteNodes(newRouteNodes);

        expect(ha.service['_routeNodes']).toBe(newRouteNodes);
    });

    it('should set caps', () => {
        const newCaps: Cap.Simple[] = [new CapBuilder()];

        ha.service.setCaps(newCaps);

        expect(ha.service['_caps']).toBe(newCaps);
    });

    it('should open bind route node dialog', () => {
        const mockCallback = jasmine.createSpy('onBindRouteNode');
        const mockModalRef = {
            content: {
                formGroup: {
                    controls: {
                        nodeType: {valueChanges: of('type1')},
                        routeNodeCode: {enable: jasmine.createSpy()}
                    }
                }, formFields: {rawValue: {routeNodeCode: {options: {set: jasmine.createSpy()}}}}
            }
        };

        dynamicFormDialogService.open.and.returnValue(mockModalRef as any);

        ha.service.openBindRouteNodeDialog(mockCapGroup, mockCallback);

        expect(dynamicFormDialogService.open).toHaveBeenCalled();
        expect(mockModalRef.content.formGroup.controls.routeNodeCode.enable).toHaveBeenCalled();
        expect(mockModalRef.content.formFields.rawValue.routeNodeCode.options.set).toHaveBeenCalled();
    });

    it('should open unbind route node dialog', () => {
        const mockCallback = jasmine.createSpy('onUnbindRouteNode');

        ha.service.openUnbindRouteNodeDialog(mockCapGroup, mockCallback);

        expect(CapGroupDataServiceHarness.getCapGroup).toHaveBeenCalledWith(mockCapGroup.capGroupName);
        expect(dynamicFormDialogService.open).toHaveBeenCalled();
    });

    it('should extract non-inherited route node codes', () => {
        ha.service.openUnbindRouteNodeDialog(mockCapGroup, jasmine.createSpy());

        expect(CapGroupDataServiceHarness.getCapGroup).toHaveBeenCalledWith(mockCapGroup.capGroupName);
        expect(dynamicFormDialogService.open).toHaveBeenCalled();

        // The extracted non-inherited codes should be 'node1' and 'node3'
        const callArgs = dynamicFormDialogService.open.calls.mostRecent().args[0];
        expect(callArgs.formFields).toBeDefined();
    });

    it('should open add cap dialog', () => {
        const mockCallback = jasmine.createSpy('onAddCap');

        ha.service.openAddCapDialog(mockCapGroup, mockCallback);

        expect(dynamicFormDialogService.open).toHaveBeenCalled();
        const callArgs = dynamicFormDialogService.open.calls.mostRecent().args[0];
        expect(callArgs.formTitle).toBe('addCapToGroup');
        expect(callArgs.onSubmit).toBeDefined();
    });

    it('should open add child dialog', () => {
        const mockCallback = jasmine.createSpy('onAddChild');

        ha.service.openAddChildDialog(mockCapGroup, mockCallback);

        expect(dynamicFormDialogService.open).toHaveBeenCalled();
        const callArgs = dynamicFormDialogService.open.calls.mostRecent().args[0];
        expect(callArgs.formTitle).toBe('new');
        expect(callArgs.onSubmit).toBeDefined();
    });

    it('should create new cap group', () => {
        const mockCallback = jasmine.createSpy('onCreateNewCapGroup');

        ha.service.createNewCapGroup(mockCallback);

        expect(dynamicFormDialogService.open).toHaveBeenCalled();
        const callArgs = dynamicFormDialogService.open.calls.mostRecent().args[0];
        expect(callArgs.formTitle).toBe('createGroup');
        expect(callArgs.onSubmit).toBe(mockCallback);
    });

    it('should hide modals', () => {
        ha.service.hideModals();

        expect(modalService.hide).toHaveBeenCalled();
    });

    describe('_getTableGroupData', () => {
        // Test: Returns correct data structure
        it('should return an array with correct structure containing caps and route node bindings', () => {
            // Arrange
            const routeNodes = [
                new RouteTreeSimpleBuilder({nodeCode: 'node1'}),
                new RouteTreeSimpleBuilder({nodeCode: 'node2'}),
                new RouteTreeSimpleBuilder({nodeCode: 'node3'})
            ];
            ha.service.setRouteNodes(routeNodes);

            // Act
            const result = ha.service['_getTableGroupData'](mockCapGroup);

            // Assert
            expect(result).toBeDefined();
            expect(Array.isArray(result)).toBe(true);
            expect(result.length).toBe(3); // 1 for caps + 2 for route node binding types
            expect(result[0].groupName).toBe(TranslateKey.caps);
            expect(result[1].groupName).toBe('type1 Bindings');
            expect(result[2].groupName).toBe('type2 Bindings');
        });

        // Test: Handles cap group with all fields
        it('should correctly map all cap group fields to table data', () => {
            // Arrange
            const routeNodes = [
                new RouteTreeSimpleBuilder({
                    nodeCode: 'node1',
                    name: 'Node 1'
                }),
                new RouteTreeSimpleBuilder({
                    nodeCode: 'node2',
                    name: 'Node 2'
                }),
                new RouteTreeSimpleBuilder({
                    nodeCode: 'node3',
                    name: 'Node 3'
                })
            ];
            ha.service.setRouteNodes(routeNodes);

            const capGroup = new CapGroupSimpleBuilder({
                capGroupName: 'testGroup',
                caps: [
                    new CapBuilder({capName: 'cap1', accountId: 'account1'}),
                    new CapBuilder({capName: 'cap2', accountId: 'account2'})
                ],
                routeNodeBindings: {
                    type1: [
                        new CapRouteBindingEntryBuilder({routeNodeCode: 'node1', isInherited: false}),
                        new CapRouteBindingEntryBuilder({routeNodeCode: 'node2', isInherited: true}),
                    ],
                    type2: [
                        new CapRouteBindingEntryBuilder({routeNodeCode: 'node3', isInherited: false}),
                    ]
                }
            });

            // Act
            const result = ha.service['_getTableGroupData'](capGroup);

            // Assert
            // Caps group
            expect(result[0].groupName).toBe(TranslateKey.caps);
            expect(result[0].data.length).toBe(2);
            expect(result[0].data[0].title).toBe('cap1');
            expect(result[0].data[0].value).toBe('account1');
            expect(result[0].data[1].title).toBe('cap2');
            expect(result[0].data[1].value).toBe('account2');

            // type1 Bindings group
            expect(result[1].groupName).toBe('type1 Bindings');
            expect(result[1].data.length).toBe(2);
            expect(result[1].data[0].title).toBe('node2 (node2)'); // Inherited node should be first
            expect(result[1].data[0].value).toBe('node1/node2');
            expect(result[1].data[1].title).toBe('node2 (node1)'); // Non-inherited node should be second
            expect(result[1].data[1].value).toBe('node1/node2');

            // type2 Bindings group
            expect(result[2].groupName).toBe('type2 Bindings');
            expect(result[2].data.length).toBe(1);
            expect(result[2].data[0].title).toBe('node2 (node3)');
            expect(result[2].data[0].value).toBe('node1/node2');
        });

        // Test: Handles cap group with missing fields
        it('should handle cap group with missing fields', () => {
            // Arrange
            const routeNodes = [
                new RouteTreeSimpleBuilder({
                    nodeCode: 'node1',
                    name: 'Node 1'
                })
            ];
            ha.service.setRouteNodes(routeNodes);

            const capGroup = new CapGroupSimpleBuilder({
                capGroupName: 'testGroup',
                caps: [],
                routeNodeBindings: {}
            });

            // Act
            const result = ha.service['_getTableGroupData'](capGroup);

            // Assert
            expect(result.length).toBe(1); // Only caps group, no bindings
            expect(result[0].groupName).toBe(TranslateKey.caps);
            expect(result[0].data.length).toBe(0); // No caps
        });

        // Test: Applies correct pipe tokens
        it('should display "nodeHierarchyNames" values', () => {
            // Arrange
            const routeNodes = [
                new RouteTreeSimpleBuilder({
                    nodeCode: 'node1',
                    name: 'Node 1'
                }),
                new RouteTreeSimpleBuilder({
                    nodeCode: 'node2',
                    name: 'Node 2'
                })
            ];
            ha.service.setRouteNodes(routeNodes);

            const capGroup = new CapGroupSimpleBuilder({
                capGroupName: 'testGroup',
                caps: [],
                routeNodeBindings: {
                    type1: [
                        new CapRouteBindingEntryBuilder({routeNodeCode: 'node1', isInherited: false}),
                        new CapRouteBindingEntryBuilder({routeNodeCode: 'node2', isInherited: true})
                    ]
                }
            });

            // Act
            const result = ha.service['_getTableGroupData'](capGroup);

            // Assert
            expect(result[1].data[0].value).toBe('node1/node2');
            expect(result[1].data[1].value).toBe('node1/node2');
        });

        // Test: Handles null values
        it('should handle null or undefined route nodes', () => {
            // Arrange
            ha.service.setRouteNodes([]); // Empty route nodes

            const capGroup = new CapGroupSimpleBuilder({
                capGroupName: 'testGroup',
                caps: [],
                routeNodeBindings: {
                    type1: [new CapRouteBindingEntryBuilder()]
                }
            });

            // Act
            const result = ha.service['_getTableGroupData'](capGroup);

            // Assert
            expect(result.length).toBe(2); // Caps group + type1 bindings
            expect(result[1].data[0].title).toBe('node2 (node1)'); // No node name found
        });

        // Test: Correct group names and float values
        it('should set correct group names and float values', () => {
            // Arrange
            const routeNodes = [
                new RouteTreeSimpleBuilder({
                    nodeCode: 'node1',
                    name: 'Node 1'
                })
            ];
            ha.service.setRouteNodes(routeNodes);

            const capGroup = new CapGroupSimpleBuilder({
                capGroupName: 'testGroup',
                caps: [
                    new CapBuilder({capName: 'cap1', accountId: 'account1'})
                ],
                routeNodeBindings: {
                    type1: [new CapRouteBindingEntryBuilder()]
                }
            });

            // Act
            const result = ha.service['_getTableGroupData'](capGroup);

            // Assert
            expect(result[0].groupName).toBe(TranslateKey.caps);
            expect(result[1].groupName).toBe('type1 Bindings');
        });

        // Test: openCapGroupDetailsDialog calls the service and shows modal
        it('should open cap group details dialog with correct data', () => {
            // Arrange
            const routeNodes = [
                new RouteTreeSimpleBuilder()
            ];
            ha.service.setRouteNodes(routeNodes);

            // Act
            ha.service.openCapGroupDetailsDialog(mockCapGroup);

            // Assert
            expect(CapGroupDataServiceHarness.getCapGroup).toHaveBeenCalledWith(mockCapGroup.capGroupName);
            expect(modalService.show).toHaveBeenCalledWith(TableGroupDialogComponent, {
                initialState: jasmine.any(TableGroupDialog),
                class: 'modal-lg'
            });

            // Check that the initialState has the correct properties
            const showArgs = modalService.show.calls.mostRecent().args;
            const initialState = showArgs[1].initialState as any;
            expect(initialState.dialogTitle).toBe(mockCapGroup.capGroupName);
            expect(initialState.tableGroupData).toBeDefined();
        });
    });
});
