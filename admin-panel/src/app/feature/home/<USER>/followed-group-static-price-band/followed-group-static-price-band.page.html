<app-page-panel [filterProperty]="filterProperty">
    <ng-container *breadcrumb><a routerLink="../../">{{ translateKeys.groups | translate }}</a></ng-container>
    <ng-container *breadcrumb>{{ this.groupCode }}</ng-container>
    <ng-container *breadcrumb>{{ translateKeys.followedGroupStaticPriceBand | translate}}</ng-container>

    <app-panel-content>
        <div class="w-100 h-100 position-absolute" #tableContainer>
            <app-data-table
                [isPageDataLoaded]="isPageDataLoaded()"
                [dataSource]="dataSource"
                [filterProperty]="filterProperty"
                [noDataRowText]="noDataRowText"
                [columns]="columns"
                [dataTableColumns]="getDataTableColumns()">
            </app-data-table>

            <mat-paginator
                *paginator
                [ngClass]="paginatorClass"
                [length]="dataSourceLength"
                [pageSize]="dataSourcePageSize"
                [showFirstLastButtons]="true">
            </mat-paginator>
        </div>
    </app-panel-content>
</app-page-panel>
