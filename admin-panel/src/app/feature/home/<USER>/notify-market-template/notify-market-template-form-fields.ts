import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {Validators} from '@angular/forms';
import {NotifyMarket} from '@homeModels/notify-market';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';
import {TextAreaFormField} from '@modules/dynamic-form/form-fields/text-area-field/text-area-form-field';
import {SelectOptionFormField} from '@modules/dynamic-form/form-fields/select-option-field/select-option-form-field';
import {enumToKeyValue} from '@core/utils';
import {FormFields} from '@modules/dynamic-form/form-field';
import {FieldsPlaceholder} from '../../../../shared/constants/fields-placeholder';

export function notifyMarketTemplateFormFields(template?: NotifyMarket.Template): FormFields {
    return [
        new InputFormField({
            formControlName: 'title',
            label: TranslateKey.title,
            width: 12,
            validations: [Validators.required, Validators.maxLength(73)],
            placeholder: FieldsPlaceholder.MARKET_TEMPLATE_TITLE,
            value: template?.title,
            disable: !!template
        }),

        new TextAreaFormField({
            formControlName: 'message',
            label: TranslateKey.message,
            width: 12,
            validations: [Validators.required],
            placeholder: FieldsPlaceholder.MARKET_TEMPLATE_MESSAGE,
            value: template?.message
        }),

        new SelectOptionFormField({
            formControlName: 'messageNature',
            label: TranslateKey.messageNature,
            width: 6,
            validations: [Validators.required],
            options: enumToKeyValue(NotifyMarket.MessageNature),
            getTitle(item): string { return item.value + ' - ' + item.key; },
            optionFieldName: 'key',
            value: template?.messageNature
        }),

        new SelectOptionFormField({
            formControlName: 'addressType',
            label: TranslateKey.addressType,
            width: 6,
            validations: [Validators.required],
            options: enumToKeyValue(NotifyMarket.AddressType),
            getTitle(item): string { return item.value + ' - ' + item.key; },
            optionFieldName: 'key',
            value: template?.addressType
        })
    ];
}
