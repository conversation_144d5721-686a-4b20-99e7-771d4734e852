import {Component, OnInit} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {FormGroup} from '@angular/forms';
import {Company} from '@models/company';
import {CompanyFormFieldSets} from '../../pages/companies/company-form-field-sets';
import {Sector} from '@models/sector';
import {SubSector} from '@models/sub-sector';
import {Subject} from 'rxjs';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {CompanyDataService} from '@dataServices/company-data.service';
import {SectorDataService} from '@dataServices/sector-data.service';
import {takeUntil} from 'rxjs/operators';
import {ChangeDetectionService} from '@services/change-detection.service';
import {CompanyDialog} from '@models/company-dialog';
import {setupUpdateCompanyFormFields} from './setup-update-company-form-fields';
import {FormFields} from '@modules/dynamic-form/form-field';
import {FormFieldSet} from '@modules/dynamic-form/form-field-sets';

@Component({
    selector: 'app-update-company-dialog',
    templateUrl: './update-company-dialog.component.html',
    standalone: false
})
export class UpdateCompanyDialogComponent implements OnInit, CompanyDialog {

    readonly translateKeys = TranslateKey;

    readonly formFieldSets: FormFieldSet[] = [
        {title: TranslateKey.shortName, id: CompanyFormFieldSets.SHORT_NAME},
        {title: TranslateKey.fullName, id: CompanyFormFieldSets.FULL_NAME}
    ];

    company: Company;

    private _formGroup = new FormGroup<Company.UpdateForm>({} as any);
    get formGroup(): FormGroup<Company.UpdateForm> { return this._formGroup; }

    private _formFields: FormFields;
    get formFields(): FormFields { return this._formFields; };

    private _sectors: Sector[] = [];

    private _subSectors: SubSector[] = [];

    private readonly _onDestroy = new Subject();

    constructor(
        private _modalRef: BsModalRef,
        private _productDataService: CompanyDataService,
        private _sectorDataService: SectorDataService
    ) { }

    ngOnInit(): void {
        this._formFields = setupUpdateCompanyFormFields(this.company, this._sectors, this._subSectors);
        this._setAllSectors();
    }

    ngAfterViewInit(): void {
        this.formGroup.controls.sectorCode?.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(resp => {
            if (resp) {
                this.formGroup.controls.subSectorCode.enable();
                this._setAllSubSectors(resp);
            }
        });
    }

    ngOnDestroy(): void {
        this._onDestroy.next();
        this._onDestroy.complete();
    }

    updateCompany(): void {
        this._productDataService
            .updateCompany(this.company.code, this.formGroup.getRawValue())
            .subscribe(() => this._modalRef.hide());
    }

    close(): void {
        this._modalRef.hide();
    }

    private _setAllSectors(): void {
        this._sectorDataService.getAllSectors().subscribe(resp => {
            resp.content.forEach(item => {
                this._sectors.push(item);
            });
            this.formGroup.controls.sectorCode
                .setValue(this._sectors.find(sector => sector.code === this.company.sectorCode)?.code);
            ChangeDetectionService.onChange.next({type: 'FORM'});
        });
    }

    private _setAllSubSectors(sectorCode: string): void {
        this._sectorDataService
            .getSubSectors(sectorCode)
            .subscribe(resp => {
                this._subSectors.splice(0);
                resp.content.forEach(item => {
                    this._subSectors.push(item);
                });
                this.formGroup.controls.subSectorCode
                    .setValue(this._subSectors.find(sector => sector.code === this.company.subSectorCode)?.code);
                ChangeDetectionService.onChange.next({type: 'FORM'});
            });
    }

}
