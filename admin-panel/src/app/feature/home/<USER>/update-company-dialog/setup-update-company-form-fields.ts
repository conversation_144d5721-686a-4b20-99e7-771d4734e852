import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {Validators} from '@angular/forms';
import {Sector} from '@models/sector';
import {SubSector} from '@models/sub-sector';
import {CompanyFormFieldSets} from '../../pages/companies/company-form-field-sets';
import {Company} from '@models/company';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';
import {SelectOptionFormField} from '@modules/dynamic-form/form-fields/select-option-field/select-option-form-field';
import {FieldsPlaceholder} from '@shared/constants/fields-placeholder';

export function setupUpdateCompanyFormFields(company: Company, sectors: Sector[], subSectors: SubSector[]) {
    return [
        {
            formGroupName: 'shortName',
            formFields: [
                new InputFormField({
                    formControlName: 'fa',
                    label: TranslateKey.persianName,
                    width: 6,
                    value: company?.shortName?.fa,
                    validations: [Validators.required, Validators.maxLength(30)],
                    placeholder: FieldsPlaceholder.NAME_FA,
                    fieldSetId: CompanyFormFieldSets.SHORT_NAME
                }),

                new InputFormField({
                    formControlName: 'en',
                    label: TranslateKey.englishName,
                    width: 6,
                    value: company?.shortName?.en,
                    validations: [Validators.required, Validators.maxLength(255)],
                    placeholder: FieldsPlaceholder.NAME_EN,
                    fieldSetId: CompanyFormFieldSets.SHORT_NAME
                }),
            ]
        },

        {
            formGroupName: 'fullName',
            formFields: [
                new InputFormField({
                    formControlName: 'fa',
                    label: TranslateKey.persianName,
                    width: 6,
                    value: company?.fullName?.fa,
                    validations: [Validators.required, Validators.maxLength(255)],
                    placeholder: FieldsPlaceholder.NAME_FA,
                    fieldSetId: CompanyFormFieldSets.FULL_NAME
                }),

                new InputFormField({
                    formControlName: 'en',
                    label: TranslateKey.englishName,
                    width: 6,
                    value: company?.fullName?.en,
                    validations: [Validators.required, Validators.maxLength(255)],
                    placeholder: FieldsPlaceholder.NAME_EN,
                    fieldSetId: CompanyFormFieldSets.FULL_NAME
                })
            ]
        },

        new SelectOptionFormField({
            formControlName: 'sectorCode',
            label: TranslateKey.sectorCode,
            width: 6,
            value: company?.sectorCode,
            validations: [Validators.required],
            placeholder: 'زراعت و خدمات وابسته - 01',
            options: sectors,
            getTitle(sector: Sector): string { return sector ? sector.code + ' - ' + sector.name : ''; },
            optionFieldName: 'code'
        }),

        new SelectOptionFormField({
            formControlName: 'subSectorCode',
            label: TranslateKey.subSectorCode,
            width: 6,
            value: company?.subSectorCode,
            validations: [Validators.required],
            placeholder: 'پرورش طيور - 0141',
            options: subSectors,
            getTitle(subSector: SubSector): string { return subSector ? subSector.code + ' - ' + subSector.name : ''; },
            optionFieldName: 'code',
            disable: true
        })
    ]
}
