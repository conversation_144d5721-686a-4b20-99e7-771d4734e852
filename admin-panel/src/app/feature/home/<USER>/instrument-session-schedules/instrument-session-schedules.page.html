<app-page-panel [filterProperty]="filterProperty">
    <ng-container *breadcrumb>{{ translateKeys.instrumentSessionSchedules | translate }}</ng-container>

    <app-panel-content>
        <div class="w-100 h-100 position-absolute" #tableContainer>
            <app-data-table
                [isPageDataLoaded]="isPageDataLoaded()"
                [dataSource]="dataSource"
                [filterProperty]="filterProperty"
                [noDataRowText]="noDataRowText"
                [columns]="columns"
                [dataTableColumns]="getDataTableColumns()">

                <ng-container *rowActionBtns="let row">
                    <button
                        *hasPermission="permissions.DELETE_DEFERRED_SCHEDULE_PERMISSION"
                        mat-menu-item
                        [disabled]="!isDateToday(row)"
                        (click)="deleteSessionSchedule(row)">
                        <mat-icon> delete </mat-icon>
                        {{ translateKeys.delete | translate }}
                    </button>
                </ng-container>

                <ng-container customColumn="sessionSchedules">
                    <ng-container *customCellDef="let row">
                        @for (action of row.actions; track action) {
                            <span>
                                {{action.runAt }} =>
                                {{action.targetState | camelCase | translate}}
                        </span>
                        }
                    </ng-container>
                </ng-container>
            </app-data-table>

            <mat-paginator
                *paginator
                [ngClass]="paginatorClass"
                [length]="dataSourceLength"
                [pageSize]="dataSourcePageSize"
                [showFirstLastButtons]="true">
            </mat-paginator>
        </div>
    </app-panel-content>
</app-page-panel>
