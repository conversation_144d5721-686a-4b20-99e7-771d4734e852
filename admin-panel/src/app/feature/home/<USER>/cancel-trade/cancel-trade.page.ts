import {AfterViewInit, ChangeDetectionStrategy, Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {TradeDataService} from '@dataServices/trade-data.service';
import {Trade} from '@models/trade';
import {ActivatedRoute} from '@angular/router';
import {TranslatePipe} from '@ngx-translate/core';
import {BsModalService} from 'ngx-bootstrap/modal';
import {TranslateKey} from '@enums/translate-key';
import {MatPaginator} from '@angular/material/paginator';
import {UtilConstants} from '@constants/util-constants';
import {map, takeUntil} from 'rxjs/operators';
import {TableGroupDialog} from '../../dialogs/table-group-dialog/table-group-dialog';
import {DecimalPipe} from '@angular/common';
import {MbDatePipe} from '@modules/datepicker/mb-date.pipe';
import {TableGroupDialogComponent} from '../../dialogs/table-group-dialog/table-group-dialog.component';
import {PaginatedDataTable} from '@models/paginated-data-table';
import {CancelTradeDataTableColumns} from './cancel-trade-data-table-columns';
import {RequestOptions} from '@models/request-options';
import {CustomDecimalPipe} from '@shared/pipes/custom-decimal.pipe';
import {Observable} from 'rxjs';
import {BilingualPipe} from '@shared/modules/shared-declarations/select-by-language/bilingual.pipe';
import {AdvancedTableData} from '@models/table-representation/advanced-table-representation';
import {PagesSharedModule} from '../../shared/modules/pages-shared.module';
import {getInstrumentById, toCamelCase} from '@core/utils';
import {Instrument} from '@homeModels/instrument';

@Component({
    selector: 'app-cancel-trade',
    templateUrl: './cancel-trade.page.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [PagesSharedModule]
})
export class CancelTradePage extends PaginatedDataTable<Trade.CancelDetail> implements OnInit, AfterViewInit {
    readonly translateKeys = TranslateKey;

    readonly columns = CancelTradeDataTableColumns();

    @ViewChild('tableContainer')
    _tableContainer: ElementRef<HTMLDivElement>;

    @ViewChild(MatPaginator)
    _paginator: MatPaginator;

    private _securityId: string;

    get instrument(): Instrument.Single { return getInstrumentById(this._securityId); }

    constructor(
        private _tradeDataService: TradeDataService,
        private _activatedRoute: ActivatedRoute,
        private _modalService: BsModalService
    ) {
        super();
    }

    ngOnInit(): void {
        this._subscribeSecurityId();

        this.filterProperty.refreshPage = this._refreshPageData.bind(this);
        this.filterProperty.callback = this._filterPageData.bind(this);

        this._modalService.onHide.subscribe(this._refreshPageData.bind(this));
    }

    override ngAfterViewInit(): void {
        this._changeDetectorRef.detectChanges();
        this._calcPageSize();
        this._subscribeOnPageChange();
    }

    openTradeDetailsDialog(row: Trade.Cancel): void {
        this._tradeDataService
            .getCancelTradesReportDetail(this._securityId, row.sequenceId, row.adminCommandId)
            .pipe(takeUntil(this._onDestroy))
            .subscribe(resp => {
                this._modalService.show(TableGroupDialogComponent, {
                    initialState: new TableGroupDialog(TranslateKey.tradeDetails, this._tableGroupsData(resp)),
                    class: 'modal-lg'
                })
            })
    }

    override fetchExcelData(): Observable<Trade.CancelDetail[]> {
        const requestOptions: RequestOptions = {
            params: this.getFilterQueryParams()
        }

        return this._tradeDataService
            .getCancelTradesReport(this._securityId, requestOptions)
            .pipe(map(resp => resp.content));
    }

    override _fetchPageData(): void {
        const requestOptions: RequestOptions = {
            hasLocalErrorHandler: true,
            params: this.getFilterQueryParams()
        }

        this._tradeDataService
            .getCancelTradesReport(this._securityId, requestOptions)
            .subscribe((resp) => {
                this._pageData = resp;
                this._changeDetectorRef.detectChanges();
            });
    }

    private _subscribeSecurityId(): void {
        this._activatedRoute.params
            .pipe(takeUntil(this._onDestroy))
            .subscribe(params => {
                this._securityId = params.securityId;
                this._fetchPageData();
                this._changeDetectorRef.detectChanges();
            });
    }

    private _tableGroupsData(trade: Trade.CancelDetail): AdvancedTableData[] {
        return [
            {
                groupName: TranslateKey.buyOrder,
                float: 'start',
                data: [
                    {
                        title: TranslateKey.orderId,
                        value: trade.buyOrder.sequenceId
                    },
                    {
                        title: TranslateKey.broker,
                        value: trade.buyOrder.brokerId
                    },
                    {
                        title: TranslateKey.priorityDateTime,
                        value: trade.buyOrder.priorityDateTime,
                        pipeToken: MbDatePipe,
                        pipeArgs: [UtilConstants.EXACT_DATE_TIME_FORMAT]
                    },
                    {
                        title: TranslateKey.displayedQuantity,
                        value: trade.buyOrder.displayedQuantity,
                        pipeToken: CustomDecimalPipe
                    },
                    {
                        title: TranslateKey.initialQuantity,
                        value: trade.buyOrder.initialQuantity,
                        pipeToken: DecimalPipe
                    },
                    {
                        title: TranslateKey.quantity,
                        value: trade.buyOrder.quantity,
                        pipeToken: DecimalPipe
                    },
                    {
                        title: TranslateKey.origin,
                        value: toCamelCase(trade.buyOrder.origin),
                        pipeToken: TranslatePipe
                    },
                    {
                        title: TranslateKey.technicalOrigin,
                        value: trade.buyOrder.technicalOrigin?.toLowerCase(),
                        pipeToken: TranslatePipe
                    },
                    {
                        title: TranslateKey.price,
                        value: trade.buyOrder.price,
                        pipeToken: DecimalPipe
                    },
                    {
                        title: TranslateKey.mnemonic,
                        value: trade.buyOrder.instrument.mnemonic,
                        pipeToken: BilingualPipe,
                        pipeArgs: [true]
                    },
                    {
                        title: TranslateKey.shareholder,
                        value: trade.buyOrder.shareholderId
                    },
                    {
                        title: TranslateKey.orderType,
                        value: toCamelCase(trade.buyOrder.type),
                        pipeToken: TranslatePipe
                    },
                    {
                        title: TranslateKey.validityQualifierType,
                        value: trade.buyOrder.validityQualifierType.toLowerCase(),
                        pipeToken: TranslatePipe
                    }
                ]
            },
            {
                groupName: TranslateKey.sellOrder,
                float: 'end',
                data: [
                    {
                        title: TranslateKey.orderId,
                        value: trade.sellOrder.sequenceId
                    },
                    {
                        title: TranslateKey.broker,
                        value: trade.sellOrder.brokerId
                    },
                    {
                        title: TranslateKey.priorityDateTime,
                        value: trade.sellOrder.priorityDateTime,
                        pipeToken: MbDatePipe,
                        pipeArgs: [UtilConstants.EXACT_DATE_TIME_FORMAT]
                    },
                    {
                        title: TranslateKey.displayedQuantity,
                        value: trade.sellOrder.displayedQuantity,
                        pipeToken: CustomDecimalPipe
                    },
                    {
                        title: TranslateKey.initialQuantity,
                        value: trade.sellOrder.initialQuantity,
                        pipeToken: DecimalPipe
                    },
                    {
                        title: TranslateKey.quantity,
                        value: trade.sellOrder.quantity,
                        pipeToken: DecimalPipe
                    },
                    {
                        title: TranslateKey.origin,
                        value: toCamelCase(trade.sellOrder.origin),
                        pipeToken: TranslatePipe
                    },
                    {
                        title: TranslateKey.technicalOrigin,
                        value: trade.sellOrder.technicalOrigin?.toLowerCase(),
                        pipeToken: TranslatePipe
                    },
                    {
                        title: TranslateKey.price,
                        value: trade.sellOrder.price,
                        pipeToken: DecimalPipe
                    },
                    {
                        title: TranslateKey.mnemonic,
                        value: trade.sellOrder.instrument.mnemonic,
                        pipeToken: BilingualPipe,
                        pipeArgs: [true]
                    },
                    {
                        title: TranslateKey.shareholder,
                        value: trade.sellOrder.shareholderId
                    },
                    {
                        title: TranslateKey.orderType,
                        value: toCamelCase(trade.sellOrder.type),
                        pipeToken: TranslatePipe
                    },
                    {
                        title: TranslateKey.validityQualifierType,
                        value: trade.sellOrder.validityQualifierType.toLowerCase(),
                        pipeToken: TranslatePipe
                    }
                ]
            },
            {
                groupName: TranslateKey.basicInfo,
                float: 'start',
                data: [
                    {
                        title: TranslateKey.tradeId,
                        value: trade.sequenceId
                    },
                    {
                        title: TranslateKey.date,
                        value: trade.dateTime,
                        pipeToken: MbDatePipe,
                        pipeArgs: [UtilConstants.DATE_TIME_FORMAT]
                    },
                    {
                        title: TranslateKey.price,
                        value: trade.price,
                        pipeToken: DecimalPipe
                    },
                    {
                        title: TranslateKey.quantity,
                        value: trade.quantity,
                        pipeToken: DecimalPipe
                    }
                ]
            }
        ]
    }
}
