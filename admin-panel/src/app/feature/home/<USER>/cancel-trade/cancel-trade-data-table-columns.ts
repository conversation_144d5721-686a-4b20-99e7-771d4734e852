import {DataTableColumn} from '@models/data-table';
import {TranslateKey} from '@enums/translate-key';
import {Trade} from '@models/trade';
import {InputFilter} from '@modules/filter/input/input-filter';
import {InputMasks} from '@constants/input-masks';
import {MbDatePipe} from '@modules/datepicker/mb-date.pipe';
import {UtilConstants} from '@constants/util-constants';
import {DecimalPipe} from '@angular/common';
import {RangeFilter} from '@modules/filter/range/range-filter';

export function CancelTradeDataTableColumns(): DataTableColumn<Trade.CancelDetail>[] {
    return [
        {
            title: TranslateKey.tradeId,
            isSticky: true,
            value(data) { return data.sequenceId },
            filter: new InputFilter<string>({
                queryParam: 'sequenceId',
                imask: InputMasks.TRADE_ID
            })
        },
        {
            title: TranslateKey.time,
            pipeToken: MbDatePipe,
            pipeArgs: [UtilConstants.TIME_FORMAT],
            value(data) { return data.dateTime },
        },
        {
            title: TranslateKey.quantity,
            pipeToken: DecimalPipe,
            minWidth: 200,
            value(data) { return data.quantity },
            filter: new RangeFilter({
                queryParam: 'quantity'
            })
        },
        {
            title: TranslateKey.price,
            pipeToken: DecimalPipe,
            minWidth: 200,
            value(data) { return data.price },
            filter: new RangeFilter({
                queryParam: 'price'
            })
        },
        {
            title: TranslateKey.buyBroker,
            value(data) { return data.buyOrder.brokerId }
        },
        {
            title: TranslateKey.sellBroker,
            value(data) { return data.sellOrder.brokerId }
        }
    ];
}
