import {TestBed} from '@angular/core/testing';
import {GroupDetailDialogComponent} from './group-detail-dialog.component';
import {UntypedFormBuilder} from '@angular/forms';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {FilterPipe} from '@pipes/filter.pipe';
import {ConfirmDirective} from '@directives/confirm.directive';
import {ConfirmDialogComponent} from '../../../../shared/components/confirm-dialog/confirm-dialog.component';
import {Http} from '@http';
import {HttpClient} from '@angular/common/http';
import {ModalHeaderComponent} from '@modules/shared-declarations/modal-header/modal-header.component';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {SharedModule} from '../../../../shared/shared.module';
import {HttpClientTestingModule} from '@angular/common/http/testing';
import {SharedDeclarations} from '@modules/shared-declarations/shared-declarations';
import {ComponentHarness} from '@test/harness/component-harness';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {TestUtils} from '@test/test-utils';

describe('GroupDetailDialogComponent', () => {
    let ha: ComponentHarness<GroupDetailDialogComponent>;

    beforeEach(() => {
        ha = new ComponentHarness(GroupDetailDialogComponent, {
            declarations: [
                GroupDetailDialogComponent,
                ConfirmDialogComponent,
                ModalHeaderComponent,
                ConfirmDirective,
                FilterPipe
            ],
            imports: [
                HttpClientTestingModule,
                TranslateTestingModule,
                SharedModule,
                SharedDeclarations
            ],
            providers: [UntypedFormBuilder, BsModalRef],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
            detectChanges: false
        });

        Http.httpClient = TestBed.inject(HttpClient);
    });

    it('should create', () => {
        ha.detectChanges();

        expect(ha.component).toBeTruthy();
    });

    it('should submit button and modal title have new group label', () => {
        ha.detectChanges();

        expect(ha.get('.modal-title').thatContains('Add Group')).toBeExists();
        expect(ha.get('button').thatContains('Submit')).toBeExists();
    });

    it('should submit button and modal title have edit group label when has group',  async () => {
        ha.component.initialValue = TestUtils.getGroup();
        await ha.detectChanges();

        expect(ha.get('.modal-title').thatContains('Edit Group 31')).toBeExists();
        expect(ha.get('button').thatContains('Submit')).toBeExists();
    });
});
