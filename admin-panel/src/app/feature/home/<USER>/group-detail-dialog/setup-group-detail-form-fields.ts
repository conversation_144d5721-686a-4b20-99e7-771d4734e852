import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {Valida<PERSON>} from '@angular/forms';
import {Schedulable} from '@models/schedule-template/schedulable';
import {Group} from '@models/group';
import {Market} from '@models/markets';
import {CustomValidators} from '@constants/custom-validators';
import {FieldsPlaceholder} from '../../../../shared/constants/fields-placeholder';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';
import {SelectOptionFormField} from '@modules/dynamic-form/form-fields/select-option-field/select-option-form-field';
import {ReadOnlyFormField} from '@modules/dynamic-form/form-fields/read-only-field/read-only-form-field';
import {enumToKeyValue, toCamelCase} from '@core/utils';
import {InputMasks} from '@constants/input-masks';

export function setupGroupDetailFormFields(group: Group) {
    const width = 12;
    const groupCodeMask = {mask: /^[0-9A-Z]{1,2}?$/};
    return [
        {
            formGroupName: 'name',
            formFields: [
                new InputFormField({
                    formControlName: 'fa',
                    label: TranslateKey.persianName,
                    width,
                    validations: [Validators.required, Validators.maxLength(255)],
                    placeholder: FieldsPlaceholder.GROUP_CODE,
                    value: group?.name.fa,
                    appendIndex: 1
                }),

                new InputFormField({
                    formControlName: 'en',
                    label: TranslateKey.englishName,
                    width,
                    validations: [Validators.required, Validators.maxLength(255)],
                    placeholder: FieldsPlaceholder.GROUP_CODE,
                    value: group?.name.en,
                    appendIndex: 2
                })
            ]
        },

        new InputFormField({
            formControlName: 'code',
            label: TranslateKey.code,
            width,
            validations: [Validators.required, CustomValidators.fixedLength(2)],
            placeholder: FieldsPlaceholder.GROUP_CODE,
            value: group?.code,
            mask: groupCodeMask,
            hint: TranslateKey.groupCodeMask,
            disable: !!group
        }),

        {
            formGroupName: 'priceBandPercentage',
            formFields: [
                new InputFormField({
                    formControlName: 'lowerBoundPercentage',
                    label: TranslateKey.lowerPriceBoundPercentage,
                    width,
                    validations: [Validators.required, Validators.min(0), Validators.max(100)],
                    placeholder: FieldsPlaceholder.PERCENTAGE,
                    value: group?.priceBandPercentage.lowerBoundPercentage,
                    disable: !!group,
                    mask: InputMasks.PERCENT
                }),

                new InputFormField({
                    formControlName: 'upperBoundPercentage',
                    label: TranslateKey.upperPriceBoundPercentage,
                    width,
                    validations: [Validators.required, Validators.min(0), Validators.max(100)],
                    placeholder: FieldsPlaceholder.PERCENTAGE,
                    value: group?.priceBandPercentage.upperBoundPercentage,
                    disable: !!group,
                    mask: InputMasks.PERCENT
                })
            ]
        },

        new InputFormField({
            formControlName: 'description',
            label: TranslateKey.description,
            width,
            placeholder: TranslateKey.description,
            value: group?.description
        }),

        new SelectOptionFormField({
            formControlName: 'market',
            label: TranslateKey.market,
            width,
            validations: [Validators.required],
            value: group?.market,
            options: enumToKeyValue(Market),
            disable: !!group,
            optionFieldName: 'key',
            getTitle(market): string { return toCamelCase(market.value); },
            placeholder: 'normalMarket'
        }),

        new ReadOnlyFormField({
            formControlName: 'type',
            value: Schedulable.Type.GROUP
        })
    ]
}
