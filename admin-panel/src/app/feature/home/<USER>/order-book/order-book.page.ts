import {ChangeDetectionStrategy, Component, ElementRef, OnInit, signal, ViewChild} from '@angular/core';
import {OrderDataService} from '@dataServices/order-data.service';
import {ActivatedRoute} from '@angular/router';
import {InsertOrderComponent} from '../../dialogs/insert-order/insert-order.component';
import {BsModalService} from 'ngx-bootstrap/modal';
import {InitialOrderBook, OrderBook} from '@models/order/order-book';
import {InsertOrderDialog} from '@models/insert-order-dialog';
import {TranslateKey} from '@enums/translate-key';
import {Page} from '@models/page';
import {MatPaginator} from '@angular/material/paginator';
import {takeUntil} from 'rxjs/operators';
import {RoutingLayout} from '@constants/routing-layout';
import {TranslateService} from '@ngx-translate/core';
import {TableGroupDialogComponent} from '../../dialogs/table-group-dialog/table-group-dialog.component';
import {TableGroupDialog} from '../../dialogs/table-group-dialog/table-group-dialog';
import {PaginatedDataTable} from '@models/paginated-data-table';
import {InstrumentDataService} from '@dataServices/instrument-data.service';
import {
    BUY_QUERY_PARAM_PREFIX,
    orderBookDataTableColumns,
    SELL_QUERY_PARAM_PREFIX
} from './order-book-data-table-columns';
import {Order} from '@models/order/order';
import {forkJoin} from 'rxjs';
import {TableGroupData} from '@models/table-representation/table-group-data';
import {getOrderBookWidgetTableData} from './order-book-widget-table-data';
import {getOrderBookTableGroupsData} from './order-book-table-groups-data';
import {QueryParams} from '@models/query-params';
import {MarketDataWidgetComponent} from '../../components/market-data-widget/market-data-widget.component';
import {PagesSharedModule} from '../../shared/modules/pages-shared.module';
import {getInstrumentById} from '@core/utils';
import {Instrument} from '@homeModels/instrument';
import {MarketDepth, OrderSideParams} from '@homeModels/market-depth';
import {HomeRoutes} from '../../shared/constants/home-routing.constants';
import {EXCEL_FILE_NAME_KEY, ExcelService, getExcelColumnsPropName} from '@shared/to-excel-columns';

@Component({
    selector: 'app-order-book',
    templateUrl: './order-book.page.html',
    styleUrl: './order-book.page.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        PagesSharedModule,
        MarketDataWidgetComponent
    ]
})
export class OrderBookPage extends PaginatedDataTable<MarketDepth> implements OnInit {
    readonly translateKeys = TranslateKey;

    readonly columns = orderBookDataTableColumns();

    marketWidgetData = signal<TableGroupData[]>([]);

    private _instrument: Instrument.Single;
    get instrument(): Instrument.Single { return this._instrument; }

    @ViewChild('tableContainer')
    _tableContainer: ElementRef<HTMLDivElement>;

    @ViewChild(MatPaginator)
    _paginator: MatPaginator;

    private get tradesInquiryRoute(): string {
        return `/${RoutingLayout.HOME}/${HomeRoutes.TRADES_INQUIRY}/${this._instrument?.securityId}`
    }

    private get bestLimitRoute(): string {
        return `/${RoutingLayout.HOME}/${HomeRoutes.BEST_LIMITS}/${this._instrument?.securityId}`
    }

    constructor(
        protected modalService: BsModalService,
        private _instrumentDataService: InstrumentDataService,
        private _orderDataService: OrderDataService,
        private _translateService: TranslateService,
        private _activatedRoute: ActivatedRoute
    ) {
        super();
    }

    ngOnInit(): void {
        this._subscribeRouteParams();

        this.modalService.onHide
            .pipe(takeUntil(this._onDestroy))
            .subscribe(this._fetchPageData.bind(this));
    }

    setFilterProperty(): void {
        this.filterProperty.actionBtns.unshift(...[
            {
                title: TranslateKey.trades,
                icon: 'archive',
                navigationUrl: this.tradesInquiryRoute
            },
            {
                title: TranslateKey.bestLimits,
                icon: 'view_list',
                navigationUrl: this.bestLimitRoute
            },
            {
                title: TranslateKey.purgeOrders,
                icon: 'delete',
                confirm: this._purgeOrderBook.bind(this),
            },
            {
                title: TranslateKey.downloadFile,
                icon: 'export_notes',
                callback: this.fetchExcelData.bind(this)
            }
        ]);
        this.filterProperty.refreshPage = this.refreshPage.bind(this);
        this.filterProperty.callback = this._filterPageData.bind(this);
    }

    removeOrder(order: OrderBook): void {
        this._orderDataService
            .removeOrder(this._instrument.securityId, order)
            .pipe(takeUntil(this._onDestroy))
            .subscribe({
                next: this._onCancelOrderSuccess.bind(this),
                error: this._onCancelOrderFailure.bind(this)
            });
    }

    editOrder(order: OrderBook): void {
        this.modalService.show(InsertOrderComponent, {
            initialState: this._insertOrderInitialState(order)
        })
    }

    openOrderDetailDialog(row: OrderBook | InitialOrderBook): void {
        const dialogTitle = this._translateService.instant(TranslateKey.detail);

        const tableGroupData = getOrderBookTableGroupsData(row as OrderBook);

        this.modalService.show(TableGroupDialogComponent, {
            initialState: new TableGroupDialog(dialogTitle, tableGroupData),
            class: 'modal-lg'
        })

    }

    toOrderBook(orderBook: OrderBook): OrderBook {
        return orderBook;
    }

    refreshPage(): void {
        this._calcPageSize();
        this._pageData.page.number = 0;
        this.filterProperty.filterParams.page = '0';
        this._fetchPageData();
    }

    private _getRequestQueryParams(params: QueryParams): OrderSideParams {
        const buyParams: QueryParams = {};
        const sellParams: QueryParams = {};
        for (let prop in params) {
            if (prop.search(BUY_QUERY_PARAM_PREFIX) === 0) {
                const param = prop.split('.')[1];
                buyParams[param] = params[prop];
            } else if (prop.search(SELL_QUERY_PARAM_PREFIX) === 0) {
                const param = prop.split('.')[1];
                sellParams[param] = params[prop];
            } else {
                buyParams[prop] = params[prop];
                sellParams[prop] = params[prop];
            }
        }

        return {
            buyParams,
            sellParams
        }
    }

    override fetchExcelData(): any {
        // Create Buy and Sell requests query params.
        const params = this.filterProperty.filterParams.toQueryParams();
        params.size = null;
        const {buyParams, sellParams} = this._getRequestQueryParams(params);

        forkJoin([
            this._orderDataService.getCurrentStateOfOrders(this._instrument.securityId, {
                ...buyParams,
                side: Order.Side.BUY
            }),
            this._orderDataService.getCurrentStateOfOrders(this._instrument.securityId, {
                ...sellParams,
                side: Order.Side.SELL
            })
        ])
            .pipe(takeUntil(this._onDestroy))
            .subscribe(([buy, sell]) => {

                const buyRow = buy.content[0];
                const sellRow = sell.content[0];

                const fileName = (buyRow || sellRow)
                    ? buyRow[EXCEL_FILE_NAME_KEY]
                    : 'unnamed_report_file';

                const buyTargetName = getExcelColumnsPropName(buyRow);
                const buyHeaders = buyRow
                    ? buyRow[buyTargetName]
                    : [];

                const sellTargetName = getExcelColumnsPropName(sellRow);
                const sellHeaders = sellRow
                    ? sellRow[sellTargetName]
                    : [];

                ExcelService.exportAsExcelFile(buy.content, buyHeaders, 'Buy_' + fileName);
                ExcelService.exportAsExcelFile(sell.content, sellHeaders, 'Sell_' + fileName);
            });
    }

    override getDataTableColumns(): string[] {
        return ['preActions', ...super.getDataTableColumns()];
    }

    override _fetchPageData(): void {
        this._pageData = new Page<MarketDepth>();
        this.isPageDataLoaded.set(false);

        // Create Buy and Sell requests query params.
        const params = this.filterProperty.filterParams.toQueryParams();
        const {buyParams, sellParams} = this._getRequestQueryParams(params);

        forkJoin([
            this._orderDataService.getCurrentStateOfOrders(this._instrument.securityId, {
                ...buyParams,
                side: Order.Side.BUY
            }),
            this._orderDataService.getCurrentStateOfOrders(this._instrument.securityId, {
                ...sellParams,
                side: Order.Side.SELL
            }),
            this._instrumentDataService.getDynamicData(this._instrument.securityId),
            this._orderDataService.getStatistics(this._instrument.securityId)
        ])
            .pipe(takeUntil(this._onDestroy))
            .subscribe(([buy, sell, instrumentDynamicData, orderBookStatistics]) => {
                const isBuyTotalGreater = buy.element.total > sell.element.total
                this._pageData.element = isBuyTotalGreater ? buy.element : sell.element;
                this._pageData.page = isBuyTotalGreater ? buy.page : sell.page;
                this._pageData.content = [];

                const buyLength = buy.content.length;
                const sellLength = sell.content.length;
                const n = isBuyTotalGreater ? buyLength : sellLength;

                for (let i = 0; i < n; i++) {
                    const marketDepth: MarketDepth = {
                        buy: buy.content[i],
                        sell: sell.content[i]
                    };

                    this._pageData.content.push(marketDepth);
                }

                this.isPageDataLoaded.set(true);
                this._changeDetectorRef.detectChanges();

                const marketWidgetData = getOrderBookWidgetTableData(this._instrument, instrumentDynamicData, orderBookStatistics);
                this.marketWidgetData.set(marketWidgetData);
            });
    }

    private _purgeOrderBook(): void {
        this._instrumentDataService
            .purgeOrders(this._instrument.securityId, {})
            .subscribe();
    }

    private _onCancelOrderSuccess(): void {
        setTimeout(this.refreshPage.bind(this), 1000);
    }

    private _onCancelOrderFailure(): void { }

    private _insertOrderInitialState(order): InsertOrderDialog {
        return {
            order
        }
    }

    private _subscribeRouteParams(): void {
        this._activatedRoute.params
            .pipe(takeUntil(this._onDestroy))
            .subscribe(params => {
                this._instrument = getInstrumentById(params.securityId);
                this.filterProperty.actionBtns = [];
                this.setFilterProperty();
                if (this.filterProperty.filterParams.size) {
                    this._fetchPageData();
                }
            });
    }

}
