import {TestBed} from '@angular/core/testing';
import {OrderBookPage} from './order-book.page';
import {RouterTestingModule} from '@angular/router/testing';
import {OpenInsertOrderDialogDirective} from '@directives/open-insert-order-dialog.directive';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {Http} from '@http';
import {HttpClient} from '@angular/common/http';
import {ActivatedRoute} from '@angular/router';
import {of, throwError} from 'rxjs';
import {StoreService} from '@shared/services/store.service';
import {TestUtils} from '@test/test-utils';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {HttpClientTestingModule} from '@angular/common/http/testing';
import {SharedModule} from '../../../../shared/shared.module';
import {PageTemplateModule} from '@modules/page-template/page-template.module';
import {DataTableFunctionality} from '@test/data-table-functionality';
import {OpenInstrumentInfoDialogDirective} from '@directives/open-instrument-info-dialog.directive';
import {ComponentHarness} from '@test/harness/component-harness';
import {OrderDataService} from '@dataServices/order-data.service';
import {orderDataServiceHarness} from '@dataServices/order-data.service.harness';
import {OrderBook} from '@models/order/order-book';
import {Order} from '@models/order/order';
import {CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA} from '@angular/core';
import {BsModalService} from 'ngx-bootstrap/modal';
import {InsertOrderComponent} from '../../dialogs/insert-order/insert-order.component';
import {TranslateService} from '@ngx-translate/core';
import {DecimalPipe} from '@angular/common';
import {DynamicPipe} from '@pipes/dynamic.pipe';
import {DataTableModule} from '@modules/data-table/data-table.module';
import {DataTableColumn} from '@models/data-table';
import {orderBookDataTableColumns} from './order-book-data-table-columns';
import {TranslateKey} from '@enums/translate-key';
import {UtilConstants} from '@constants/util-constants';
import {Page} from '@models/page';
import {InstrumentDataService} from '@dataServices/instrument-data.service';
import {instrumentDataServiceHarness} from '@dataServices/instrument-data.service.harness';
import {TableGroupDialogComponent} from '../../dialogs/table-group-dialog/table-group-dialog.component';
import {QueryParams} from '@models/query-params';
import {MbDatePipe} from '@modules/datepicker/mb-date.pipe';
import {HomeRoutes} from '../../shared/constants/home-routing.constants';
import {MarketDepth} from '@homeModels/market-depth';
import {EXCEL_COLUMNS_KEY, EXCEL_FILE_NAME_KEY, ExcelService} from '@shared/to-excel-columns';


describe('OrderBookPage', () => {
    let columns: DataTableColumn<MarketDepth>[];
    let ha: ComponentHarness<OrderBookPage>;
    let modalService: BsModalService;
    let orders: Page<OrderBook>;
    let singleOrder: OrderBook;
    let instrumentDataService: InstrumentDataService;
    let orderDataService: OrderDataService;
    let translateService: TranslateService;

    beforeEach(() => {
        StoreService.instruments = TestUtils.getInstruments().content;
        StoreService.instrumentsObj = TestUtils.getInstrumentsObj();
        StoreService.brokersObj = TestUtils.getBrokersObj();

        ha = new ComponentHarness(OrderBookPage, {
            declarations: [
                DynamicPipe,
                SelectByLanguagePipe,
                OpenInsertOrderDialogDirective,
                OpenInstrumentInfoDialogDirective
            ],
            imports: [
                SharedModule,
                OrderBookPage,
                DataTableModule,
                PageTemplateModule,
                RouterTestingModule,
                TranslateTestingModule,
                BrowserAnimationsModule,
                HttpClientTestingModule
            ],
            providers: [
                DecimalPipe,
                BsModalService,
                TranslateService,
                SelectByLanguagePipe,
                {
                    provide: ActivatedRoute,
                    useValue: {params: of({securityId: 'SPY'})}
                },
                {provide: InstrumentDataService, useValue: instrumentDataServiceHarness},
                {provide: OrderDataService, useValue: orderDataServiceHarness}
            ],
            schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
            detectChanges: false
        });

        Http.httpClient = TestBed.inject(HttpClient);
        modalService = TestBed.inject(BsModalService);
        instrumentDataService = TestBed.inject(InstrumentDataService);
        orderDataService = TestBed.inject(OrderDataService);
        translateService = TestBed.inject(TranslateService);
        StoreService.origins = TestUtils.getOrigins();
        ha.component['_instrument'] = StoreService.instruments[0] as any; // TODO: remove any
    });

    beforeEach(() => {
        StoreService.instrumentsObj = TestUtils.getInstrumentsObj();
        columns = orderBookDataTableColumns();
        orders = TestUtils.getOrderBook();
        singleOrder = orders.content[0];
    });

    it('should create', () => {
        ha.detectChanges();
        expect(ha.component).toBeTruthy();
    });

    describe('DataTable functionality', () => {
        it('when the total number of rows exceeds the page size, pagination should be displayed', () => {
            ha.detectChanges();
            DataTableFunctionality.when_the_total_number_of_rows_exceeds_the_page_size_pagination_should_be_displayed(ha);
        });

        it('pagination should be hidden when the total number of rows is less than the page size', () => {
            ha.detectChanges();
            DataTableFunctionality.pagination_should_be_hidden_when_the_total_number_of_rows_is_less_than_the_page_size(ha);
        });
    });

    it('should call #refreshPage function when clicking on reload button when data type is order book', () => {
        spyOn(orderDataServiceHarness, 'getCurrentStateOfOrders').and.returnValue(of(orders));
        ha.detectChanges();

        ha.get('button').thatContains('Reload');
        expect(orderDataServiceHarness.getCurrentStateOfOrders)
            .toHaveBeenCalledWith(ha.component.instrument.securityId, jasmine.anything());
    });

    it('should call #removeOrder function correctly', () => {
        spyOn(orderDataServiceHarness, 'removeOrder').and.returnValue(of(null));
        ha.detectChanges();

        ha.component.removeOrder(singleOrder);
        expect(orderDataServiceHarness.removeOrder).toHaveBeenCalledWith(ha.component.instrument.securityId, singleOrder);
    });

    it('should handle removeOrder error case', () => {
        spyOn(orderDataServiceHarness, 'removeOrder').and.returnValue(throwError(() => new Error('Test error')));
        spyOn(ha.component as any, '_onCancelOrderFailure');
        ha.detectChanges();

        ha.component.removeOrder(singleOrder);
        expect((ha.component as any)._onCancelOrderFailure).toHaveBeenCalled();
    });

    it('should show dialog when #editOrder function is called', () => {
        const order: OrderBook = TestUtils.getOrderBook().content[0];
        spyOn(modalService, 'show');
        ha.detectChanges();

        ha.component.editOrder(order);
        expect(modalService.show).toHaveBeenCalledWith(InsertOrderComponent, jasmine.anything());
    });

    it('should open order detail dialog', () => {
        spyOn(translateService, 'instant').and.returnValue('Detail');
        spyOn(modalService, 'show');
        ha.detectChanges();

        ha.component.openOrderDetailDialog(singleOrder);
        expect(translateService.instant).toHaveBeenCalledWith(TranslateKey.detail);
        expect(modalService.show).toHaveBeenCalledWith(TableGroupDialogComponent, jasmine.anything());
    });

    it('should return the same order in toOrderBook method', () => {
        const result = ha.component.toOrderBook(singleOrder);
        expect(result).toBe(singleOrder);
    });

    it('should refresh page correctly', () => {
        spyOn(ha.component as any, '_calcPageSize');
        spyOn(ha.component as any, '_fetchPageData');
        ha.detectChanges();

        ha.component.refreshPage();
        expect((ha.component as any)._calcPageSize).toHaveBeenCalled();
        expect(ha.component['_pageData'].page.number).toBe(0);
        expect(ha.component['filterProperty'].filterParams.page).toBe('0');
        expect((ha.component as any)._fetchPageData).toHaveBeenCalled();
    });

    it('should define the first 4 columns for buy data', () => {
        const buyData = {
            sequenceId: 123,
            quantity: 100,
            displayedQuantity: 50,
            price: '200',
            shareholderId: '18991361494115'
        } as OrderBook;

        const mockData: MarketDepth = {buy: buyData, sell: null};

        expect(columns[0].value(mockData)).toBe(buyData.sequenceId);
        expect(columns[1].value(mockData)).toBe(buyData.shareholderId);
        expect(columns[2].value(mockData)).toBe(buyData.quantity);
        expect(columns[3].value(mockData)).toBe(buyData.displayedQuantity);
        expect(columns[4].value(mockData)).toBe(buyData.price);
    });

    it('should define the next 4 columns for sell data', () => {
        const sellData = {
            sequenceId: 123,
            quantity: 150,
            displayedQuantity: 75,
            price: '250',
            shareholderId: '18991361494115'
        } as OrderBook;

        const mockData: MarketDepth = {buy: null, sell: sellData};

        expect(columns[5].value(mockData)).toBe(sellData.price);
        expect(columns[6].value(mockData)).toBe(sellData.displayedQuantity);
        expect(columns[7].value(mockData)).toBe(sellData.quantity);
        expect(columns[8].value(mockData)).toBe(sellData.shareholderId);
        expect(columns[9].value(mockData)).toBe(sellData.sequenceId);
    });

    it('should set filter property correctly', () => {
        ha.detectChanges();

        ha.component.setFilterProperty();

        expect(ha.component['filterProperty'].actionBtns.length).toBeGreaterThan(0);
        expect(ha.component['filterProperty'].refreshPage).toBeDefined();
        expect(ha.component['filterProperty'].callback).toBeDefined();
    });

    it('should purge order book when called', () => {
        spyOn(instrumentDataServiceHarness, 'purgeOrders').and.returnValue(of(null));
        ha.detectChanges();

        (ha.component as any)._purgeOrderBook();

        expect(instrumentDataServiceHarness.purgeOrders).toHaveBeenCalledWith(
            ha.component.instrument.securityId,
            {}
        );
    });

    it('should call refreshPage after successful order cancellation', () => {
        jasmine.clock().install();
        spyOn(ha.component, 'refreshPage');

        (ha.component as any)._onCancelOrderSuccess();

        jasmine.clock().tick(1000);
        expect(ha.component.refreshPage).toHaveBeenCalled();
        jasmine.clock().uninstall();
    });

    it('should create correct insert order initial state', () => {
        const result = (ha.component as any)._insertOrderInitialState(singleOrder);
        expect(result.order).toBe(singleOrder);
    });

    function createMockPage<T>(content: T[]): Page<T> {
        const page = new Page(content);
        page.element = {count: content.length, offset: 0, total: content.length};
        page.page = {size: 10, number: 0, total: 1};
        return page;
    }

    describe('fetchExcelData', () => {
        it('should fetch excel data correctly with valid content', () => {
            const buyContent = [{[EXCEL_FILE_NAME_KEY]: 'test_file', [EXCEL_COLUMNS_KEY]: ['col1', 'col2']}];
            const sellContent = [{[EXCEL_FILE_NAME_KEY]: 'test_file', [EXCEL_COLUMNS_KEY]: ['col3', 'col4']}];

            const buyPage = createMockPage(buyContent);
            const sellPage = createMockPage(sellContent);

            spyOn(orderDataServiceHarness, 'getCurrentStateOfOrders').and.returnValues(of(buyPage as any), of(sellPage as any));
            spyOn(ExcelService, 'exportAsExcelFile');
            spyOn(ha.component.filterProperty.filterParams, 'toQueryParams').and.returnValue({size: 10, page: 0});
            spyOn(ha.component as any, '_getRequestQueryParams').and.returnValue({
                buyParams: {},
                sellParams: {}
            });

            ha.component.fetchExcelData();

            expect(orderDataServiceHarness.getCurrentStateOfOrders).toHaveBeenCalledTimes(2);
            expect(orderDataServiceHarness.getCurrentStateOfOrders).toHaveBeenCalledWith(
                ha.component.instrument.securityId,
                {side: Order.Side.BUY}
            );
            expect(orderDataServiceHarness.getCurrentStateOfOrders).toHaveBeenCalledWith(
                ha.component.instrument.securityId,
                {side: Order.Side.SELL}
            );
            expect(ExcelService.exportAsExcelFile).toHaveBeenCalledWith(
                buyContent,
                undefined,
                'Buy_test_file'
            );
            expect(ExcelService.exportAsExcelFile).toHaveBeenCalledWith(
                sellContent,
                undefined,
                'Sell_test_file'
            );
        });

        it('should handle excel export with empty content', () => {
            const buyPage = createMockPage([]);
            const sellPage = createMockPage([]);

            spyOn(orderDataServiceHarness, 'getCurrentStateOfOrders').and.returnValues(of(buyPage), of(sellPage));
            spyOn(ExcelService, 'exportAsExcelFile');
            spyOn(ha.component.filterProperty.filterParams, 'toQueryParams').and.returnValue({size: 10});
            spyOn(ha.component as any, '_getRequestQueryParams').and.returnValue({
                buyParams: {},
                sellParams: {}
            });

            ha.component.fetchExcelData();

            expect(ExcelService.exportAsExcelFile).toHaveBeenCalledWith(
                [],
                [],
                'Buy_unnamed_report_file'
            );
            expect(ExcelService.exportAsExcelFile).toHaveBeenCalledWith(
                [],
                [],
                'Sell_unnamed_report_file'
            );
        });

        it('should handle missing Excel columns gracefully', () => {
            const buyContent = [{[EXCEL_FILE_NAME_KEY]: 'test_file'}]; // Missing EXCEL_COLUMNS_KEY
            const sellContent = [{[EXCEL_FILE_NAME_KEY]: 'test_file'}]; // Missing EXCEL_COLUMNS_KEY

            const buyPage = {content: buyContent} as any;
            const sellPage = {content: sellContent} as any;

            spyOn(orderDataServiceHarness, 'getCurrentStateOfOrders').and.returnValues(of(buyPage), of(sellPage));
            spyOn(ExcelService, 'exportAsExcelFile');
            spyOn(ha.component.filterProperty.filterParams, 'toQueryParams').and.returnValue({});
            spyOn(ha.component as any, '_getRequestQueryParams').and.returnValue({
                buyParams: {},
                sellParams: {}
            });

            ha.component.fetchExcelData();

            expect(ExcelService.exportAsExcelFile).toHaveBeenCalledWith(
                buyContent,
                undefined, // Headers will be undefined when property doesn't exist
                'Buy_test_file'
            );
            expect(ExcelService.exportAsExcelFile).toHaveBeenCalledWith(
                sellContent,
                undefined, // Headers will be undefined when property doesn't exist
                'Sell_test_file'
            );
        })

        it('should set params.size to null before making requests', () => {
            const mockParams = {size: 10, page: 0};
            const buyPage = {content: []} as Page<OrderBook>;
            const sellPage = {content: []} as Page<OrderBook>;

            spyOn(orderDataServiceHarness, 'getCurrentStateOfOrders').and.returnValues(of(buyPage), of(sellPage));
            spyOn(ExcelService, 'exportAsExcelFile');
            spyOn(ha.component.filterProperty.filterParams, 'toQueryParams').and.returnValue(mockParams);
            spyOn(ha.component as any, '_getRequestQueryParams').and.returnValue({
                buyParams: {},
                sellParams: {}
            });

            ha.component.fetchExcelData();

            expect(mockParams.size).toBeNull();
            expect((ha.component as any)._getRequestQueryParams).toHaveBeenCalledWith(mockParams);
        });

        it('should complete subscription when component is destroyed', () => {
            const buyPage = {content: []} as Page<OrderBook>;
            const sellPage = {content: []} as Page<OrderBook>;

            spyOn(orderDataServiceHarness, 'getCurrentStateOfOrders').and.returnValues(of(buyPage), of(sellPage));
            spyOn(ExcelService, 'exportAsExcelFile');

            // Ensure _onDestroy subject exists
            expect(ha.component['_onDestroy']).toBeDefined();

            ha.component.fetchExcelData();

            // Verify the subscription completes when _onDestroy is triggered
            const onDestroy = ha.component['_onDestroy'];
            expect(onDestroy).toBeDefined();

            // Test that Excel export was called (meaning subscription was active)
            expect(ExcelService.exportAsExcelFile).toHaveBeenCalledTimes(2);
        });

        it('should pass correct Order.Side.BUY and Order.Side.SELL to service calls', () => {
            const buyPage = {content: []} as Page<OrderBook>;
            const sellPage = {content: []} as Page<OrderBook>;

            spyOn(orderDataServiceHarness, 'getCurrentStateOfOrders').and.returnValues(of(buyPage), of(sellPage));
            spyOn(ExcelService, 'exportAsExcelFile');
            spyOn(ha.component.filterProperty.filterParams, 'toQueryParams').and.returnValue({});
            spyOn(ha.component as any, '_getRequestQueryParams').and.returnValue({
                buyParams: {customParam: 'buy'},
                sellParams: {customParam: 'sell'}
            });

            ha.component.fetchExcelData();

            expect(orderDataServiceHarness.getCurrentStateOfOrders).toHaveBeenCalledWith(
                ha.component.instrument.securityId,
                {customParam: 'buy', side: Order.Side.BUY}
            );
            expect(orderDataServiceHarness.getCurrentStateOfOrders).toHaveBeenCalledWith(
                ha.component.instrument.securityId,
                {customParam: 'sell', side: Order.Side.SELL}
            );
        });
    });

    it('should get data table columns correctly', () => {
        const result = ha.component.getDataTableColumns();
        expect(result[0]).toBe('preActions');
    });

    it('should fetch page data correctly when buy total is greater', () => {
        const buyPage = TestUtils.getOrderBook();
        const sellPage = TestUtils.getOrderBook();
        buyPage.element.total = 10;
        sellPage.element.total = 5;

        const mockDynamicData = TestUtils.getInstrumentDynamicData();

        const mockStatistics = {
            buyQueue: {
                bestPrice: '110',
                worstPrice: '100'
            },
            sellQueue: {
                bestPrice: '120',
                worstPrice: '130'
            }
        };

        spyOn(orderDataServiceHarness, 'getCurrentStateOfOrders').and.returnValues(of(buyPage), of(sellPage));
        spyOn(instrumentDataServiceHarness, 'getDynamicData').and.returnValue(of(mockDynamicData));
        spyOn(orderDataServiceHarness, 'getStatistics').and.returnValue(of(mockStatistics));
        spyOn(ha.component['marketWidgetData'], 'set');

        (ha.component as any)._fetchPageData();

        expect(ha.component['_pageData'].element).toBe(buyPage.element);
        expect(ha.component['_pageData'].page).toBe(buyPage.page);
        expect(ha.component['marketWidgetData'].set).toHaveBeenCalled();
    });

    it('should handle request query params correctly', () => {
        const params: QueryParams = {
            'buy.param1': 'value1',
            'sell.param2': 'value2',
            common: 'value3'
        };

        const result = (ha.component as any)._getRequestQueryParams(params);

        expect(result.buyParams.param1).toBe('value1');
        expect(result.buyParams.common).toBe('value3');
        expect(result.sellParams.param2).toBe('value2');
        expect(result.sellParams.common).toBe('value3');
    });

    it('should have pipeArgs set to EXACT_DATE_TIME_FORMAT for priorityDateTime field', () => {
        // Import the actual function to test it directly
        const {getOrderBookTableGroupsData} = require('./order-book-table-groups-data');

        // Create a mock order book with the necessary properties
        const mockOrderBook = {
            priorityDateTime: '2023-01-01T12:00:00Z',
            instrument: {mnemonic: 'TEST'},
            broker: {name: 'Test Broker'},
            side: 'BUY',
            origin: 'MARKET',
            technicalOrigin: 'WEB',
            validityQualifierType: 'DAY',
            clearingData: {
                brokerBusinessIdentificationCode: {
                    bankCode: '123',
                    branchCode: '456',
                    townCode: '789'
                }
            }
        };

        // Get the actual table groups data
        const tableGroups = getOrderBookTableGroupsData(mockOrderBook);

        // Find the priorityDateTime field in the basic info group
        const basicInfoGroup = tableGroups.find(group => group.groupName === TranslateKey.basicInfo);
        const priorityDateTimeField = basicInfoGroup.data.find(field => field.title === TranslateKey.priorityDateTime);

        // Verify the format is correct
        expect(priorityDateTimeField).toBeDefined();
        expect(priorityDateTimeField.pipeToken).toBe(MbDatePipe);
        expect(priorityDateTimeField.pipeArgs).toContain(UtilConstants.EXACT_DATE_TIME_FORMAT);
    });

    it('should return correct tradesInquiryRoute', () => {
        ha.detectChanges();

        const route = ha.component.filterProperty.actionBtns
            .find(item => item.title === TranslateKey.trades)
            .navigationUrl;

        expect(route).toContain('SPY');
        expect(route).toContain(HomeRoutes.TRADES_INQUIRY);
    });

    it('should return correct bestLimitRoute', () => {
        ha.detectChanges();

        const route = ha.component.filterProperty.actionBtns
            .find(item => item.title === TranslateKey.bestLimits)
            .navigationUrl;

        expect(route).toContain('SPY');
        expect(route).toContain(HomeRoutes.BEST_LIMITS);
    });

    it('should not call _fetchPageData when filterProperty.filterParams.size is not set', () => {
        spyOn(ha.component, 'setFilterProperty');
        spyOn(ha.component as any, '_fetchPageData');

        // Remove the size parameter
        ha.component['filterProperty'].filterParams.size = undefined;

        (ha.component as any)._subscribeRouteParams();

        expect(ha.component.setFilterProperty).toHaveBeenCalled();
        expect((ha.component as any)._fetchPageData).not.toHaveBeenCalled();
    });

    it('should set callback to _filterPageData in setFilterProperty', () => {
        // Setup spies
        spyOn(ha.component as any, '_filterPageData');

        // Call setFilterProperty
        ha.component.setFilterProperty();

        // Call the callback that should be set to _filterPageData
        ha.component['filterProperty'].callback();

        // Verify _filterPageData was called
        expect((ha.component as any)._filterPageData).toHaveBeenCalled();
    });

    it('should subscribe to route params and set up the component', () => {
        spyOn(ha.component, 'setFilterProperty');
        spyOn(ha.component as any, '_fetchPageData');

        ha.component['filterProperty'].filterParams.size = '10';
        (ha.component as any)._subscribeRouteParams();

        expect(ha.component.setFilterProperty).toHaveBeenCalled();
        expect((ha.component as any)._fetchPageData).toHaveBeenCalled();
    });

    it('should subscribe to modalService.onHide', () => {
        spyOn(ha.component as any, '_fetchPageData');
        spyOn(modalService.onHide, 'pipe').and.returnValue(of({}));

        ha.component.ngOnInit();

        expect((ha.component as any)._fetchPageData).toHaveBeenCalled();
    });
});
