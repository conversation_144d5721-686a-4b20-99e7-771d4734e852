import {Component, OnInit} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {BsModalRef, BsModalService} from 'ngx-bootstrap/modal';
import {UntypedFormBuilder, UntypedFormGroup, Validators} from '@angular/forms';
import {ChangePasswordDto, ChangePasswordFormGroup} from '@models/change-password';
import {ChangePasswordDataService} from '@dataServices/change-password-data.service';
import {
    ChangePasswordSuccessfullyDialogComponent
} from '../change-password-successfully-dialog/change-password-successfully-dialog.component';

@Component({
    selector: 'app-change-password-dialog',
    templateUrl: './change-password-dialog.component.html',
    standalone: false
})
export class ChangePasswordDialogComponent implements OnInit {
    readonly translateKeys = TranslateKey;
    formGroup: UntypedFormGroup;
    isNewPasswordStrengthLow: boolean;
    isNewPasswordStrengthMedium: boolean;
    isNewPasswordStrengthHigh: boolean;
    passwordsAreNotSame: boolean;

    constructor(
        public modalRef: BsModalRef,
        private _formBuilder: UntypedFormBuilder,
        private _changePasswordDataService: ChangePasswordDataService,
        private _modalService: BsModalService
    ) {
    }

    ngOnInit(): void {
        this.setupFormGroup();
    }

    onSubmit({valid, value}: { valid: boolean, value: ChangePasswordFormGroup }): void {
        if (!valid) {
            return;
        }

        const changePasswordBody = new ChangePasswordDto(value);

        this._changePasswordDataService
            .changePassword(changePasswordBody)
            .subscribe(() => {
                this.modalRef.hide();
                this._openChangePasswordSuccessfullyDialog();
            });
    }

    private setupFormGroup(): void {
        this.formGroup = this._formBuilder.group({
            oldPassword: [null, Validators.required],
            newPassword: [null, Validators.required],
            confirmPassword: [null, Validators.required]
        });

        this.subscribeInputs();
    }

    private subscribeInputs(): void {
        this.formGroup.get('newPassword').valueChanges.subscribe(password => {
            this.checkPasswordStrength(password);
            this.passwordConfirming();
        });

        this.formGroup.get('confirmPassword').valueChanges.subscribe(() => this.passwordConfirming());
    }

    private passwordConfirming() {
        const newPassword = this.formGroup.get('newPassword').value;
        const confirmPassword = this.formGroup.get('confirmPassword').value;

        this.passwordsAreNotSame = newPassword !== confirmPassword;
    }

    private checkPasswordStrength(password: string): void {
        /**
         * ^	            The password string will start this way
         * (?=.*[a-z])	    The string must contain at least 1 lowercase alphabetical character
         * (?=.*[A-Z])	    The string must contain at least 1 uppercase alphabetical character
         * (?=.*[0-9])	    The string must contain at least 1 numeric character
         * (?=.[!@#\$%\^&]) The string must contain at least one special character, but we are escaping reserved
         *                  RegEx characters to avoid conflict
         * (?=.{8,})	    The string must be eight characters or longer
         */
        const strongRegex = new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*_\-])(?=.{10,})');
        const mediumRegex = new RegExp('^(((?=.*[a-z])(?=.*[A-Z]))|((?=.*[a-z])(?=.*[0-9]))|((?=.*[A-Z])(?=.*[0-9])))(?=.{8,})');

        if (strongRegex.test(password)) {
            this.isNewPasswordStrengthLow = false;
            this.isNewPasswordStrengthMedium = false;
            this.isNewPasswordStrengthHigh = true;
        } else if (mediumRegex.test(password)) {
            this.isNewPasswordStrengthLow = false;
            this.isNewPasswordStrengthMedium = true;
            this.isNewPasswordStrengthHigh = false;
        } else {
            this.isNewPasswordStrengthLow = true;
            this.isNewPasswordStrengthMedium = false;
            this.isNewPasswordStrengthHigh = false;
        }
    }

    private _openChangePasswordSuccessfullyDialog(): void {
        this._modalService.show(ChangePasswordSuccessfullyDialogComponent);
    }
}
