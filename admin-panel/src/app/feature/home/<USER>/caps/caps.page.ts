import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {TranslateKey} from '@shared/enums/translate-key';
import {Observable} from 'rxjs';
import {MatPaginator} from '@angular/material/paginator';
import {PaginatedDataTable} from '@models/paginated-data-table';
import {BsModalService} from 'ngx-bootstrap/modal';
import {map, takeUntil} from 'rxjs/operators';
import {capsDataTableColumns} from './caps-data-table-columns';
import {RequestOptions} from '@models/request-options';
import {PagesSharedModule} from '../../shared/modules/pages-shared.module';
import {TableGroupDialogComponent} from '../../dialogs/table-group-dialog/table-group-dialog.component';
import {TableGroupDialog} from '../../dialogs/table-group-dialog/table-group-dialog';
import {CapDataService} from '../../shared/data-services/cap-data.service';
import {CapDetailTableGroupData} from './cap-detail-table-group-data';
import {FormGroup} from '@angular/forms';
import {DynamicFormDialogService} from '../../dialogs/dynamic-form-dialog/dynamic-form-dialog.service';
import {createCapFormFields, groupsToCapFormFields} from './create-cap-form-fields';
import {Permissions} from '../../../../shared/constants/permissions.constant';
import {FormFields} from '@modules/dynamic-form/form-field';
import {CapGroupDataService} from '../../shared/data-services/cap-group-data.service';
import {Cap} from '@homeModels/cap';


@Component({
    selector: 'app-caps',
    templateUrl: './caps.page.html',
    imports: [PagesSharedModule]
})
export class CapsPage extends PaginatedDataTable<Cap.Simple> implements OnInit {
    readonly translateKeys = TranslateKey;

    readonly permissions = Permissions;

    readonly columns = capsDataTableColumns();

    @ViewChild('tableContainer')
    override _tableContainer: ElementRef<HTMLDivElement>;

    @ViewChild(MatPaginator)
    override _paginator: MatPaginator;

    constructor(
        private _modalService: BsModalService,
        private _capDataService: CapDataService,
        private _capGroupDataService: CapGroupDataService,
        private _dynamicFormDialogService: DynamicFormDialogService
    ) {
        super();

        this.filterProperty.refreshPage = this._refreshPageData.bind(this);
        this.filterProperty.callback = this._filterPageData.bind(this);
        this.filterProperty.actionBtns.unshift(...[
            {
                title: TranslateKey.createCap,
                icon: 'add',
                callback: this._openCreateCapDialog.bind(this),
                isDisabled: () => false,
            }
        ]);
    }

    ngOnInit(): void {
        this._modalService.onHide
            .pipe(takeUntil(this._onDestroy))
            .subscribe(this._fetchPageData.bind(this));
    }

    openDetails(row: Cap.Simple): void {
        this._capDataService
            .getCapDetail(row.accountId)
            .subscribe(resp => {
                this._modalService.show(TableGroupDialogComponent, {
                    initialState: new TableGroupDialog(TranslateKey.detail, CapDetailTableGroupData(resp)),
                    class: 'modal-lg'
                });
            })
    }

    async openUpdateCapDialog(row: Cap.Simple) {
        const capDetails = await this._capDataService.getCapDetail(row.accountId).toPromise();

        this._dynamicFormDialogService.open({
            formTitle: TranslateKey.updateCap,
            formFields: createCapFormFields(capDetails),
            hasKeepDialogOpen: true,
            onSubmit: (formGroup, keepDialogOpen) =>
                this._onSubmitUpdateCapForm(row.accountId, formGroup, keepDialogOpen)
        }, {class: 'non-scrollable-modal-body'});
    }

    async openAddGroupsToCapDialog(cap: Cap.Simple) {
        const capGroupName = await this._getCapGroupsName();
        const formFields: FormFields = groupsToCapFormFields(capGroupName, cap.capGroupNames);

        this._dynamicFormDialogService.open<Cap.AddCapGroupsForm>({
            formTitle: TranslateKey.addGroupsToCap,
            formFields,
            onSubmit: this._onAddGroupsToCap.bind(this, cap)
        }, {class: 'non-scrollable-modal-body'});
    }

    deleteCap(row: Cap.Simple): void {
        this._capDataService.deleteCap(row.accountId).subscribe(() => {
            this._fetchPageData();
        });
    }

    override fetchExcelData(): Observable<Cap.Simple[]> {
        const requestOptions: RequestOptions = {
            params: this.getFilterQueryParams()
        }

        return this._capDataService
            .getAllCaps(requestOptions)
            .pipe(map(resp => resp.content));
    }

    override _fetchPageData(): void {
        const requestOptions: RequestOptions = {
            hasLocalErrorHandler: true,
            params: this.getFilterQueryParams()
        }

        this._capDataService
            .getAllCaps(requestOptions)
            .pipe(takeUntil(this._onDestroy))
            .subscribe(resp => {
                this._pageData = resp as any;
                this._changeDetectorRef.detectChanges();
            });
    }

    private _onAddGroupsToCap(cap: Cap.Simple, formGroup: FormGroup<Cap.AddCapGroupsForm>): void {
        this._capDataService
            .putCapGroups(cap.accountId, formGroup.getRawValue())
            .subscribe(() => {
                this._modalService.hide();
                this._fetchPageData();
            });
    }

    private _openCreateCapDialog(): void {
        this._dynamicFormDialogService.open({
            formTitle: TranslateKey.createCap,
            formFields: createCapFormFields(),
            hasKeepDialogOpen: true,
            onSubmit: this._onSubmitCreateCapForm.bind(this)
        }, {class: 'non-scrollable-modal-body'});
    }

    private _onSubmitCreateCapForm(formGroup: FormGroup, keepDialogOpen: boolean): void {
        this._capDataService
            .createCap(formGroup.getRawValue())
            .pipe(takeUntil(this._onDestroy))
            .subscribe(() => {
                if (!keepDialogOpen) {
                    this._modalService.hide();
                }
            });
    }

    private _onSubmitUpdateCapForm(accountId: string, formGroup: FormGroup, keepDialogOpen: boolean): void {
        this._capDataService
            .updateCap(accountId, formGroup.getRawValue())
            .pipe(takeUntil(this._onDestroy))
            .subscribe(() => {
                if (!keepDialogOpen) {
                    this._modalService.hide();
                }
            });
    }

    private async _getCapGroupsName(): Promise<string[]> {
        return this._capGroupDataService
            .getCapGroupsName()
            .pipe(takeUntil(this._onDestroy))
            .toPromise();
    }
}
