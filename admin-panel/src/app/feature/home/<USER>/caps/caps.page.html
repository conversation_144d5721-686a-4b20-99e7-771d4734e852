<app-page-panel [filterProperty]="filterProperty">
    <ng-container *breadcrumb>{{ translateKeys.caps | translate }}</ng-container>

    <app-panel-content>
        <div class="w-100 h-100 position-absolute" #tableContainer>
            <app-data-table
                [isPageDataLoaded]="isPageDataLoaded()"
                [dataSource]="dataSource"
                [filterProperty]="filterProperty"
                [noDataRowText]="noDataRowText"
                [columns]="columns"
                [dataTableColumns]="getDataTableColumns()">

                <ng-container *rowActionBtns="let row">
                    <button
                        *hasPermission="permissions.CAP_DETAILS_PERMISSION"
                        mat-menu-item
                        (click)="openDetails(row)">
                        <mat-icon>list</mat-icon>
                        <span>{{translateKeys.detail | translate}}</span>
                    </button>
                    <button
                        *hasPermission="permissions.UPDATE_CAP_PERMISSION"
                        mat-menu-item
                        (click)="openUpdateCapDialog(row)">
                        <mat-icon>edit</mat-icon>
                        <span>{{translateKeys.edit | translate}}</span>
                    </button>
                    <button
                        (click)="openAddGroupsToCapDialog(row)"
                        *hasPermission="permissions.UPDATE_CAP_PERMISSION"
                        mat-menu-item>
                        <mat-icon>add</mat-icon>
                        <span>{{ translateKeys.addGroupsToCap | translate }}</span>
                    </button>
                    <button
                        *hasPermission="permissions.DELETE_CAP_PERMISSION"
                        mat-menu-item
                        (confirm)="deleteCap(row)">
                        <mat-icon>delete</mat-icon>
                        <span>{{translateKeys.delete | translate}}</span>
                    </button>
                </ng-container>
            </app-data-table>

            <mat-paginator
                *paginator
                [ngClass]="paginatorClass"
                [length]="dataSourceLength"
                [pageSize]="dataSourcePageSize"
                [showFirstLastButtons]="true">
            </mat-paginator>
        </div>
    </app-panel-content>
</app-page-panel>
