import {DataTableColumn} from "@models/data-table";
import {GroupSchedule} from "@models/schedule";
import {TranslateKey} from "@enums/translate-key";
import {MbDatePipe} from "@modules/datepicker/mb-date.pipe";
import {UtilConstants} from "@constants/util-constants";
import {RangeDatepickerFilter} from "@modules/filter/range-datepicker-filter/range-datepicker-filter";
import {InputFilter} from "@modules/filter/input/input-filter";

export function GroupSchedulesDataTableColumns(): DataTableColumn<GroupSchedule>[] {
    return [
        {
            title: TranslateKey.from,
            minWidth: 180,
            pipeToken: MbDatePipe,
            pipeArgs: [UtilConstants.DATE_FORMAT],
            value(data) { return data.from },
            filter: new RangeDatepickerFilter({
                queryParam: 'from'
            })
        },
        {
            title: TranslateKey.code,
            value(data) { return data.group.code },
            filter: new InputFilter({
                queryParam: 'schedulableId'
            })
        }
    ];
}
