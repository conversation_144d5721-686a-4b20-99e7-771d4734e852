import {ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {PagesSharedModule} from '../../shared/modules/pages-shared.module';
import {PaginatedDataTable} from '@models/paginated-data-table';
import {GroupSchedule} from '@models/schedule';
import {TranslateKey} from '@enums/translate-key';
import {Permissions} from '../../../../shared/constants/permissions.constant';
import {GroupSchedulesDataTableColumns} from './group-schedules-data-table-columns';
import {MatPaginator} from '@angular/material/paginator';
import {ScheduleService} from '../../shared/services/schedule.service';
import {map, takeUntil} from 'rxjs/operators';
import {BsModalService} from 'ngx-bootstrap/modal';
import {Observable} from 'rxjs';
import {RequestOptions} from '@models/request-options';
import {ScheduleDataService} from '@dataServices/schedule-data.service';

@Component({
    selector: 'app-group-schedules',
    imports: [PagesSharedModule],
    templateUrl: './group-schedules.page.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class GroupSchedulesPage extends PaginatedDataTable<GroupSchedule> implements OnInit {

    readonly translateKeys = TranslateKey;

    readonly permissions = Permissions;

    readonly columns = GroupSchedulesDataTableColumns();

    @ViewChild('tableContainer')
    override _tableContainer: ElementRef<HTMLDivElement>;

    @ViewChild(MatPaginator)
    override _paginator: MatPaginator;

    constructor(
        protected _changeDetectorRef: ChangeDetectorRef,
        private _modalService: BsModalService,
        private _scheduleService: ScheduleService,
        private _scheduleDataService: ScheduleDataService
    ) {
        super();

        this._setFilterProperty();
    }

    ngOnInit(): void {
        this._modalService.onHide
            .pipe(takeUntil(this._onDestroy))
            .subscribe(this._fetchPageData.bind(this));
    }

    async editSchedule(schedule: GroupSchedule) {
        await this._scheduleService
            .associateGroupSchedule(schedule)
    }

    deleteSchedule(schedule: GroupSchedule): void {
        this._scheduleDataService
            .deleteGroupSchedule(schedule.group.code, schedule.from)
            .subscribe();
    }

    override fetchExcelData(): Observable<GroupSchedule[]> {
        const requestOptions: RequestOptions = {
            params: this.getFilterQueryParams()
        }

        return this._scheduleDataService
            .getGroupSchedules(requestOptions)
            .pipe(map(resp => resp.content));
    }

    override _fetchPageData(): void {
        const requestOptions: RequestOptions = {
            hasLocalErrorHandler: true,
            params: this.getFilterQueryParams()
        }

        this._scheduleDataService
            .getGroupSchedules(requestOptions)
            .pipe(takeUntil(this._onDestroy))
            .subscribe(resp => {
                this._pageData = resp;
                this._changeDetectorRef.detectChanges();
            });
    }

    private _setFilterProperty(): void {
        this.filterProperty.actionBtns = [
            {
                title: TranslateKey.addSchedule,
                icon: 'more_time',
                callback: this._scheduleService
                    .associateGroupSchedule,
                permissions: this.permissions.CREATE_GROUP_SCHEDULE_PERMISSION
            },
        ];
        this.filterProperty.refreshPage = this._refreshPageData.bind(this);
        this.filterProperty.callback = this._filterPageData.bind(this);
    }

}
