import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {Validators} from '@angular/forms';
import {InputMasks} from '@constants/input-masks';
import {FieldsPlaceholder} from '../../../../shared/constants/fields-placeholder';
import {CustomValidators} from '@constants/custom-validators';
import {ProductType} from '@models/product-type';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';
import {FormFields} from '@modules/dynamic-form/form-field';

export function productTypeFormFields(productType?: ProductType): FormFields {
    return [
        new InputFormField({
            formControlName: 'code',
            label: TranslateKey.code,
            validations: [Validators.required, CustomValidators.fixedLength(1)],
            width: 12,
            mask: InputMasks.ONE_CHAR_CODE,
            value: productType?.code,
            disable: !!productType,
            placeholder: FieldsPlaceholder.PRODUCT_TYPE_CODE
        }),
        new InputForm<PERSON>ield({
            formControlName: 'name',
            label: TranslateKey.name,
            validations: [Validators.required, Validators.maxLength(40)],
            width: 12,
            value: productType?.name,
            placeholder: FieldsPlaceholder.PRODUCT_TYPE_NAME
        })
    ];
}
