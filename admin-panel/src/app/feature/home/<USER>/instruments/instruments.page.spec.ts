import {InstrumentsPage} from './instruments.page';
import {ComponentHarness} from '@test/harness/component-harness';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {RouterTestingModule} from '@angular/router/testing';
import {PageTemplateModule} from '@modules/page-template/page-template.module';
import {SharedModule} from '../../../../shared/shared.module';
import {TestBed} from '@angular/core/testing';
import {BsModalService} from 'ngx-bootstrap/modal';
import {InstrumentDataService} from '@dataServices/instrument-data.service';
import {instrumentDataServiceHarness} from '@dataServices/instrument-data.service.harness';
import {Http} from '@http';
import {HttpClient} from '@angular/common/http';
import {HttpClientTestingModule} from '@angular/common/http/testing';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {
    CreateInstrumentDialogComponent
} from '../../dialogs/create-instrument-dialog/create-instrument-dialog.component';
import {OpenInstrumentInfoDialogDirective} from '@directives/open-instrument-info-dialog.directive';
import {SharedDeclarations} from '@modules/shared-declarations/shared-declarations';
import {TranslateKey} from '@shared/enums/translate-key';
import {instrumentsDataTableColumns} from './instruments-data-table-columns';
import {DataTableModule} from '@modules/data-table/data-table.module';
import {HasPermissionService} from '@directives/has-permission.service';
import {hasPermissionServiceHarness} from '@directives/has-permission.service.harness';

describe('InstrumentsPage', () => {
    let ha: ComponentHarness<InstrumentsPage>;
    let modalServiceSpy: BsModalService;

    beforeEach(() => {
        ha = new ComponentHarness(InstrumentsPage, {
            declarations: [
                OpenInstrumentInfoDialogDirective,
                SelectByLanguagePipe,
            ],
            imports: [
                HttpClientTestingModule,
                TranslateTestingModule,
                InstrumentsPage,
                RouterTestingModule,
                PageTemplateModule,
                SharedDeclarations,
                DataTableModule,
                SharedModule
            ],
            providers: [
                BsModalService,
                {provide: HasPermissionService, useValue: hasPermissionServiceHarness},
                {provide: InstrumentDataService, useValue: instrumentDataServiceHarness}
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
            detectChanges: false
        });

        Http.httpClient = TestBed.inject(HttpClient);
        modalServiceSpy = TestBed.inject(BsModalService);
    });

    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });

    it('should show new company dialog when click on New Company button', () => {
        spyOn(modalServiceSpy, 'show');
        ha.detectChanges();

        ha.get('button').thatContains('Add Instrument').click();
        expect(modalServiceSpy.show).toHaveBeenCalledWith(CreateInstrumentDialogComponent, {class: 'modal-lg'});
    });

    it('should "securityId" column has "monospace" class', () => {
        const column = instrumentsDataTableColumns()
            .find(column => column.title === TranslateKey.securityId);

        expect(column.class).toContain('monospace');
    });

    it('should "productId" column has "monospace" class', () => {
        const column = instrumentsDataTableColumns()
            .find(column => column.title === TranslateKey.productId);

        expect(column.class).toContain('monospace');
    });
});
