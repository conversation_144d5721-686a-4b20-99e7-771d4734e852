import {Trans<PERSON><PERSON><PERSON>} from '@enums/translate-key';
import {Validators} from '@angular/forms';
import {InputMasks} from '@constants/input-masks';
import {CustomValidators, isNotEmpty} from '@constants/custom-validators';
import {Board} from '@models/board';
import {OrderPreference} from '@enums/order-preference';
import {ProductType} from '@models/product-type';
import {ProductSubType} from '@models/product-sub-type';
import {MarketFlow} from '@models/market-flow';
import {Company} from '@models/company';
import {Market} from '@models/markets';
import {Group} from '@models/group';
import {StoreService} from '@shared/services/store.service';
import {FieldsPlaceholder} from '../../../../shared/constants/fields-placeholder';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';
import {AutoCompleteFormField} from '@modules/dynamic-form/form-fields/auto-complete-field/auto-complete-form-field';
import {SelectOptionFormField} from '@modules/dynamic-form/form-fields/select-option-field/select-option-form-field';
import {DatePickerFormField} from '@modules/dynamic-form/form-fields/date-picker-field/date-picker-form-field';
import {FormFields} from '@modules/dynamic-form/form-field';
import {ActionButtonField} from '@modules/dynamic-form/form-fields/action-button-field/action-button-field';
import {CheckboxFormField} from '@modules/dynamic-form/form-fields/checkbox-field/checkbox-form-field';
import {ProductFormFieldSets} from '../../shared/constants/product-form-field-sets';
import {enumToKeyValue, toCamelCase} from '@core/utils';

export function setupNewProductFormFields(companies: Company[], boards: Board[], types: ProductType[], subTypes: ProductSubType[], marketFlow: MarketFlow[]): FormFields {
    const width = 6;
    return [
        new AutoCompleteFormField<Board>({
            formControlName: 'boardCode',
            label: TranslateKey.boardCode,
            width,
            placeholder: FieldsPlaceholder.BOARD,
            validations: [Validators.required],
            options: boards,
            getTitle(board: Board): string {
                return board ? board.code + ' - ' + board.name : '';
            },
            optionFieldName: 'code',
            searchExpField: 'code',
            fieldSetId: ProductFormFieldSets.MARKET_SEC
        }),

        new AutoCompleteFormField<MarketFlow>({
            formControlName: 'marketFlowCode',
            label: TranslateKey.marketFlowCode,
            width,
            validations: [Validators.required],
            placeholder: FieldsPlaceholder.MARKET_FLOW_CODE,
            options: marketFlow,
            getTitle(marketFlow: MarketFlow): string {
                return marketFlow ? marketFlow.code + ' - ' + marketFlow.name : '';
            },
            optionFieldName: 'code',
            searchExpField: 'code',
            fieldSetId: ProductFormFieldSets.MARKET_SEC
        }),

        new AutoCompleteFormField<ProductType>({
            formControlName: 'productTypeCode',
            label: TranslateKey.productTypeCode,
            width,
            validations: [Validators.required, CustomValidators.fixedLength(1)],
            placeholder: FieldsPlaceholder.PRODUCT_TYPE,
            options: types,
            getTitle(type: ProductType): string {
                return type ? type.code + ' - ' + type.name : '';
            },
            optionFieldName: 'code',
            searchExpField: 'code',
            fieldSetId: ProductFormFieldSets.MARKET_SEC
        }),

        new AutoCompleteFormField<ProductSubType>({
            formControlName: 'productSubTypeCode',
            label: TranslateKey.productSubTypeCode,
            width,
            validations: [Validators.required],
            placeholder: FieldsPlaceholder.PRODUCT_SUB_TYPE,
            options: subTypes,
            getTitle(subType: ProductSubType): string {
                return subType ? subType.code + ' - ' + subType.name : '';
            },
            optionFieldName: 'code',
            searchExpField: 'code',
            disable: true,
            fieldSetId: ProductFormFieldSets.MARKET_SEC
        }),

        new AutoCompleteFormField<Company>({
            formControlName: 'companyCode',
            label: TranslateKey.company,
            width: 4,
            validations: [Validators.required, CustomValidators.fixedLength(4)],
            searchExpField: 'searchExpression',
            options: companies,
            getTitle(option): string {
                return option ? option.code + ' - ' + option.shortName.fa : '';
            },
            optionFieldName: 'code',
            placeholder: FieldsPlaceholder.COMPANY_CODE,
            fieldSetId: ProductFormFieldSets.BASIC_SPEC
        }),

        new InputFormField({
            formControlName: 'productCode',
            label: TranslateKey.productCode,
            width: 4,
            validations: [Validators.required, CustomValidators.fixedLength(6)],
            mask: InputMasks.FIXED_LENGTH(6),
            placeholder: FieldsPlaceholder.NAME_EN,
            fieldSetId: ProductFormFieldSets.BASIC_SPEC
        }),

        new InputFormField({
            formControlName: 'productId',
            label: TranslateKey.productId,
            width: 4,
            validations: [Validators.required, CustomValidators.fixedLength(12)],
            placeholder: FieldsPlaceholder.ISIN,
            mask: InputMasks.PRODUCT,
            disable: true,
            fieldSetId: ProductFormFieldSets.BASIC_SPEC,
            class: 'direction-ltr',
            hasRemoveBtn: false
        }),

        {
            formGroupName: 'name',
            formFields: [
                new InputFormField({
                    formControlName: 'fa',
                    label: TranslateKey.persianName,
                    width,
                    validations: [Validators.required, Validators.maxLength(30)],
                    placeholder: FieldsPlaceholder.NAME_FA,
                    fieldSetId: ProductFormFieldSets.PRODUCT_SPEC
                }),

                new InputFormField({
                    formControlName: 'en',
                    label: TranslateKey.englishName,
                    width,
                    validations: [Validators.required, Validators.maxLength(18)],
                    placeholder: FieldsPlaceholder.NAME_EN,
                    fieldSetId: ProductFormFieldSets.PRODUCT_SPEC
                }),
            ]
        },

        {
            formGroupName: 'mnemonic',
            formFields: [
                new InputFormField({
                    formControlName: 'fa',
                    label: TranslateKey.faMnemonic,
                    width,
                    validations: [Validators.required, Validators.maxLength(17)],
                    placeholder: FieldsPlaceholder.SHORT_NAME_FA,
                    fieldSetId: ProductFormFieldSets.PRODUCT_SPEC
                }),

                new InputFormField({
                    formControlName: 'en',
                    label: TranslateKey.enMnemonic,
                    width,
                    validations: [Validators.required, CustomValidators.fixedLength(4)],
                    placeholder: FieldsPlaceholder.SHORT_NAME_EN,
                    fieldSetId: ProductFormFieldSets.PRODUCT_SPEC
                })
            ]
        },

        new SelectOptionFormField({
            formControlName: 'matchingType',
            label: TranslateKey.matchingAlgo,
            width,
            validations: [Validators.required],
            options: Object.keys(OrderPreference),
            placeholder: FieldsPlaceholder.MATCHING_TYPE,
            fieldSetId: ProductFormFieldSets.PRODUCT_SPEC
        }),

        new InputFormField({
            formControlName: 'totalShares',
            label: TranslateKey.totalShares,
            width,
            validations: [Validators.required, Validators.maxLength(12), CustomValidators.greaterThan(0)],
            placeholder: FieldsPlaceholder.SHARES,
            mask: InputMasks.COMMA_SEPERATED_NUMBER,
            fieldSetId: ProductFormFieldSets.PRODUCT_SPEC
        }),

        new InputFormField({
            formControlName: 'normalBlockSize',
            label: TranslateKey.normalBlockSize,
            width,
            validations: [Validators.required, Validators.maxLength(12), CustomValidators.greaterThan(0)],
            placeholder: FieldsPlaceholder.NORMAL_BLOCK_SIZE,
            mask: InputMasks.MAX_INTEGER_NUMBER(12),
            fieldSetId: ProductFormFieldSets.PRODUCT_SPEC
        }),

        new InputFormField({
            formControlName: 'referencePrice',
            label: TranslateKey.referencePrice,
            width,
            validations: [Validators.required, Validators.pattern(InputMasks.PRICE.pattern)],
            placeholder: FieldsPlaceholder.DECIMAL_NUMBER,
            mask: InputMasks.MAX_FLOAT_NUMBER(11),
            fieldSetId: ProductFormFieldSets.PRODUCT_SPEC
        }),

        new InputFormField({
            formControlName: 'parValue',
            label: TranslateKey.parValue,
            width,
            validations: [Validators.required, Validators.pattern(InputMasks.PRICE.pattern)],
            placeholder: FieldsPlaceholder.DECIMAL_NUMBER,
            mask: InputMasks.MAX_FLOAT_NUMBER(11),
            fieldSetId: ProductFormFieldSets.PRODUCT_SPEC
        }),

        new SelectOptionFormField({
            formControlName: 'availableForSell',
            label: TranslateKey.availableForSell,
            validations: [Validators.required],
            value: true,
            width,
            options: [true, false],
            getTitle(option: boolean): string { return option.toString(); },
            fieldSetId: ProductFormFieldSets.PRODUCT_SPEC
        }),

        new CheckboxFormField({
            formControlName: 'isPricePercentage',
            label: TranslateKey.percentagePrice,
            width,
            fieldSetId: ProductFormFieldSets.PRODUCT_SPEC,
            style: {marginTop: '20px'}
        }),

        new InputFormField({
            formControlName: 'underlyingProductId',
            label: TranslateKey.underlyingProductId,
            width,
            validations: [CustomValidators.fixedLength(12)],
            mask: InputMasks.ISIN,
            placeholder: FieldsPlaceholder.ISIN,
            fieldSetId: ProductFormFieldSets.DERIVATIVE_SPEC
        }),

        new DatePickerFormField({
            formControlName: 'maturityDate',
            label: TranslateKey.maturityDate,
            width,
            fieldSetId: ProductFormFieldSets.DERIVATIVE_SPEC
        }),

        new DatePickerFormField({
            formControlName: 'tradingStartDate',
            label: TranslateKey.tradingStartDate,
            width,
            fieldSetId: ProductFormFieldSets.DERIVATIVE_SPEC
        }),

        new DatePickerFormField({
            formControlName: 'tradingEndDate',
            label: TranslateKey.tradingEndDate,
            width,
            fieldSetId: ProductFormFieldSets.DERIVATIVE_SPEC
        }),

        new InputFormField({
            formControlName: 'strikePrice',
            label: TranslateKey.strikePrice,
            width,
            validations: [Validators.pattern(InputMasks.PRICE.pattern)],
            mask: InputMasks.MAX_FLOAT_NUMBER(11),
            placeholder: FieldsPlaceholder.DECIMAL_NUMBER,
            fieldSetId: ProductFormFieldSets.DERIVATIVE_SPEC
        }),

        new InputFormField({
            formControlName: 'issuePrice',
            label: TranslateKey.issuePrice,
            width,
            validations: [Validators.pattern(InputMasks.PRICE.pattern)],
            mask: InputMasks.MAX_FLOAT_NUMBER(11),
            placeholder: FieldsPlaceholder.DECIMAL_NUMBER,
            fieldSetId: ProductFormFieldSets.DERIVATIVE_SPEC
        }),

        {
            formArrayName: 'securities',
            formFields: []
        }
    ];
}

export function productInstrumentsFields(onRemove: () => void): FormFields {
    const width = 6;

    const upperBoundPercentageField = new InputFormField({
        formControlName: 'upperBoundPercentage',
        label: TranslateKey.upperPriceBoundPercentage,
        width,
        validations: [],
        fieldSetId: ProductFormFieldSets.SECURITIES,
        mask: InputMasks.PERCENT,
        placeholder: FieldsPlaceholder.PERCENTAGE
    });

    const lowerBoundPercentageField = new InputFormField({
        formControlName: 'lowerBoundPercentage',
        label: TranslateKey.lowerPriceBoundPercentage,
        width,
        validations: [],
        fieldSetId: ProductFormFieldSets.SECURITIES,
        mask: InputMasks.PERCENT,
        placeholder: FieldsPlaceholder.PERCENTAGE
    });

    lowerBoundPercentageField.validations.push(
        CustomValidators
            .requiredIf(
                isNotEmpty(
                    upperBoundPercentageField),
                isNotEmpty(
                    lowerBoundPercentageField)
            )
    );

    upperBoundPercentageField.validations.push(
        CustomValidators
            .requiredIf(
                isNotEmpty(
                    upperBoundPercentageField),
                isNotEmpty(
                    lowerBoundPercentageField)
            )
    );

    return [
        new SelectOptionFormField({
            formControlName: 'market',
            label: TranslateKey.market,
            width,
            validations: [Validators.required],
            options: enumToKeyValue(Market),
            optionFieldName: 'key',
            getTitle(market): string { return toCamelCase(market.value); },
            placeholder: 'normalMarket',
            fieldSetId: ProductFormFieldSets.SECURITIES
        }),

        new AutoCompleteFormField<Group>({
            formControlName: 'groupId',
            label: TranslateKey.group,
            width,
            validations: [Validators.required],
            options: StoreService.groups,
            optionFieldName: 'code',
            searchExpField: 'searchExpression',
            getTitle(option): string { return option.code },
            fieldSetId: ProductFormFieldSets.SECURITIES,
            placeholder: FieldsPlaceholder.GROUP_CODE
        }),

        {
            formGroupName: 'priceBandPercentage',
            formFields: [
                lowerBoundPercentageField,
                upperBoundPercentageField
            ]
        },

        new ActionButtonField({
            title: TranslateKey.remove,
            fieldSetId: ProductFormFieldSets.SECURITIES,
            onClick: onRemove
        })
    ];
}
