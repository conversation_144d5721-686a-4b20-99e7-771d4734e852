import {TestBed} from '@angular/core/testing';
import {OrderRequestsReportPage} from './order-requests-report.page';
import {RouterTestingModule} from '@angular/router/testing';
import {OpenInsertOrderDialogDirective} from '@directives/open-insert-order-dialog.directive';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {Http} from '@http';
import {HttpClient} from '@angular/common/http';
import {ActivatedRoute} from '@angular/router';
import {of} from 'rxjs';
import {StoreService} from '@shared/services/store.service';
import {TestUtils} from '@test/test-utils';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {HttpClientTestingModule} from '@angular/common/http/testing';
import {SharedModule} from '@shared/shared.module';
import {PageTemplateModule} from '@modules/page-template/page-template.module';
import {DataTableFunctionality} from '@test/data-table-functionality';
import {OpenInstrumentInfoDialogDirective} from '@directives/open-instrument-info-dialog.directive';
import {ComponentHarness} from '@test/harness/component-harness';
import {NO_ERRORS_SCHEMA} from '@angular/core';
import {BsModalService} from 'ngx-bootstrap/modal';
import {TranslateService} from '@ngx-translate/core';
import {DynamicPipe} from '@pipes/dynamic.pipe';
import {DataTableModule} from '@modules/data-table/data-table.module';
import {OrderRequestReportDataService} from '@dataServices/order-request-report-data.service';
import {orderRequestReportDataServiceHarness} from '@dataServices/order-request-report-data.service.harness';
import {OrderRequestReport} from '@models/order/order-request-report';
import {Page} from '@models/page';


describe('OrderRequestsReportPage', () => {
    let ha: ComponentHarness<OrderRequestsReportPage>;

    let modalService: BsModalService;

    const invalidOrder = {
        id: '926452727930683393',
        internalSubscriberReference: '****************',
        accountId: 'API-GATEWAY',
        state: OrderRequestReport.State.REJECTED_BY_REQUEST_VIOLATION,
        previousSequenceId: '0',
        rejectionCause: 'UNKNOWN_MESSAGE_TYPE'
    }

    beforeEach(() => {
        StoreService.instruments = TestUtils.getInstruments().content;
        StoreService.brokersObj = TestUtils.getBrokersObj();

        ha = new ComponentHarness(OrderRequestsReportPage, {
            declarations: [
                OpenInsertOrderDialogDirective,
                OpenInstrumentInfoDialogDirective,
                SelectByLanguagePipe,
                DynamicPipe
            ],
            imports: [
                OrderRequestsReportPage,
                BrowserAnimationsModule,
                HttpClientTestingModule,
                TranslateTestingModule,
                RouterTestingModule,
                PageTemplateModule,
                DataTableModule,
                SharedModule
            ],
            providers: [
                SelectByLanguagePipe,
                BsModalService,
                TranslateService,
                {
                    provide: ActivatedRoute,
                    useValue: {params: of({securityId: 'SPY'})}
                },
                {provide: OrderRequestReportDataService, useValue: orderRequestReportDataServiceHarness}
            ],
            schemas: [NO_ERRORS_SCHEMA],
            detectChanges: false
        });

        Http.httpClient = TestBed.inject(HttpClient);
        modalService = TestBed.inject(BsModalService);
        ha.component['_securityId'] = StoreService.instruments[0].securityId;
    });

    it('should create', () => {
        ha.detectChanges();
        expect(ha.component).toBeTruthy();
    });

    it('should load data table correctly even when order request is not valid', () => {
        spyOn(orderRequestReportDataServiceHarness, 'getAllOrders').and.returnValue(of(new Page(([invalidOrder]) as any)));

        ha.detectChanges();

        expect(ha.get('td').thatContains('Rejected By Request Violation')).toBeExists();
    });

    describe('DataTable functionality', () => {
        it('when the total number of rows exceeds the page size, pagination should be displayed', () => {
            ha.detectChanges();
            DataTableFunctionality.when_the_total_number_of_rows_exceeds_the_page_size_pagination_should_be_displayed(ha);
        });

        it('pagination should be hidden when the total number of rows is less than the page size', () => {
            ha.detectChanges();
            DataTableFunctionality.pagination_should_be_hidden_when_the_total_number_of_rows_is_less_than_the_page_size(ha);
        });
    });
});
