import {ChangeDetectionStrategy, Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {BsModalService} from 'ngx-bootstrap/modal';
import {TranslateKey} from '@shared/enums/translate-key';
import {MatPaginator} from '@angular/material/paginator';
import {map, takeUntil} from 'rxjs/operators';
import {TranslateService} from '@ngx-translate/core';
import {TableGroupDialogComponent} from '../../dialogs/table-group-dialog/table-group-dialog.component';
import {TableGroupDialog} from '../../dialogs/table-group-dialog/table-group-dialog';
import {PaginatedDataTable} from '@models/paginated-data-table';
import {orderRequestsReportDataTableColumns} from './order-requests-report-data-table-columns';
import {RequestOptions} from '@models/request-options';
import {OrderRequestReportDataService} from '@dataServices/order-request-report-data.service';
import {OrderRequestReport} from '@models/order/order-request-report';
import {Observable} from 'rxjs';
import {RoutingLayout} from '@constants/routing-layout';
import {Permissions} from '../../../../shared/constants/permissions.constant';
import {OrderRequestsReportTableData} from '../../shared/constants/order-requests-report-table-data';
import {PagesSharedModule} from '../../shared/modules/pages-shared.module';
import {HomeRoutes} from '../../shared/constants/home-routing.constants';

@Component({
    selector: 'app-order-requests-report',
    templateUrl: './order-requests-report.page.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [PagesSharedModule]
})
export class OrderRequestsReportPage extends PaginatedDataTable<OrderRequestReport> implements OnInit {
    readonly translateKeys = TranslateKey;

    readonly permissions = Permissions;

    readonly columns = orderRequestsReportDataTableColumns();

    @ViewChild('tableContainer')
    _tableContainer: ElementRef<HTMLDivElement>;

    @ViewChild(MatPaginator)
    _paginator: MatPaginator;

    constructor(
        private _orderRequestReportDataService: OrderRequestReportDataService,
        private _translateService: TranslateService,
        protected modalService: BsModalService
    ) {
        super();
    }

    ngOnInit(): void {
        this.setFilterProperty();
    }

    setFilterProperty(): void {
        this.filterProperty.refreshPage = this.refreshPage.bind(this);
        this.filterProperty.callback = this._filterPageData.bind(this);
    }

    openOrderDetailDialog(row: OrderRequestReport): void {
        this._orderRequestReportDataService.getOrder(row.id).subscribe(resp => {
            const dialogTitle = this._translateService.instant(TranslateKey.orderRequestsReport);
            const tableGroupData = OrderRequestsReportTableData(resp);

            this.modalService.show(TableGroupDialogComponent, {
                initialState: new TableGroupDialog(dialogTitle, tableGroupData),
                class: 'modal-lg'
            });
        });
    }

    getOrderHistoryRoute(row: OrderRequestReport): string {
        return `/${RoutingLayout.HOME}/${HomeRoutes.ORDER_REQUESTS_REPORT}/${row.id}`;
    }

    refreshPage(): void {
        this._calcPageSize();
        this._pageData.page.number = 0;
        this.filterProperty.filterParams.page = '0';
        this._fetchPageData();
    }

    override fetchExcelData(): Observable<OrderRequestReport[]> {
        const requestOptions: RequestOptions = {
            params: this.getFilterQueryParams()
        }

        return this._orderRequestReportDataService
            .getAllOrders(requestOptions)
            .pipe(map(resp => resp.content));
    }

    override _fetchPageData(): void {
        const requestOptions: RequestOptions = {
            params: this.filterProperty.filterParams.toQueryParams(),
            hasLocalErrorHandler: true
        }

        this._orderRequestReportDataService
            .getAllOrders(requestOptions)
            .pipe(takeUntil(this._onDestroy))
            .subscribe(resp => {
                this._pageData = resp;
                this._changeDetectorRef.detectChanges();
            });
    }
}
