import {ChangeDetectionStrategy, Component, OnInit} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {ShareholderDataService} from '@dataServices/shareholder-data.service';
import {TranslateKey} from '@enums/translate-key';
import {Shareholder} from '@models/shareholder';
import {ShareholderBlockDialog} from '@models/shareholder-block-dialog';
import {AdminCommandCallback} from '@models/admin-command-callback';
import {AdminCommand} from '@models/admin-command';
import {StoreService} from '@shared/services/store.service';
import {Order} from '@models/order/order';
import {ShareholderAdminCommands} from '@models/shareholder-admin-commands';
import {FormGroup} from '@angular/forms';
import {BlockShareholderFormFields} from './block-shareholder-form-fields';
import {FormFields} from '@modules/dynamic-form/form-field';

@Component({
    selector: 'app-shareholder-block-dialog',
    templateUrl: './shareholder-block-dialog.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class ShareholderBlockDialogComponent implements OnInit, ShareholderBlockDialog {
    readonly translateKeys = TranslateKey;

    readonly shareholder: Shareholder;

    readonly formGroup = new FormGroup<Shareholder.BlockForm>({} as any);

    readonly shareholderPositions: Shareholder.Position[] = [];

    private _formFields: FormFields<Shareholder.BlockForm>;
    get formFields(): FormFields { return this._formFields; };

    constructor(
        public modalRef: BsModalRef,
        private _shareholderDataService: ShareholderDataService
    ) {}

    ngOnInit() {
        this._formFields = BlockShareholderFormFields(this.shareholder);
    }

    onSubmit(): void {
        const formValue = this.formGroup.getRawValue();

        this._shareholderDataService
            .blockShareholder(this.shareholder.shareholderId, formValue)
            .subscribe(this.onBlockShareholderSuccess.bind(this));
    }

    private onBlockShareholderSuccess(adminCommand: AdminCommand): void {
        const adminCommandCallBack = new AdminCommandCallback(adminCommand.commandId, this._updateShareholder.bind(this));
        StoreService.adminCommandCallBacks.push(adminCommandCallBack);
        this.modalRef.hide();
    }

    private _updateShareholder(adminCommand: AdminCommand<ShareholderAdminCommands.Block>) {
        const shareholderBlockedSides = adminCommand.request.shareholderBlockedSides;
        this.shareholder.isBuyBlocked = shareholderBlockedSides.find(item => item.side === Order.Side.BUY).blocked;
        this.shareholder.isSellBlocked = shareholderBlockedSides.find(item => item.side === Order.Side.SELL).blocked;
    }
}
