import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {Order} from '@models/order/order';
import {Shareholder} from '@models/shareholder';
import {FieldsPlaceholder} from '../../../../shared/constants/fields-placeholder';
import {CheckboxFormField} from '@modules/dynamic-form/form-fields/checkbox-field/checkbox-form-field';
import {ReadOnlyFormField} from '@modules/dynamic-form/form-fields/read-only-field/read-only-form-field';
import {FormFields} from '@modules/dynamic-form/form-field';

export function BlockShareholderFormFields(shareholder: Shareholder): FormFields {
    return [
        {
            formArrayName: 'shareholderBlockedSides',
            formFields: [
                [
                    new CheckboxFormField({
                        formControlName: 'blocked',
                        label: TranslateKey.blockShareholderBuySide,
                        width: 12,
                        value: shareholder.isBuyBlocked
                    }),
                    new ReadOnlyFormField({
                        formControlName: 'side',
                        value: Order.Side.BUY
                    })
                ],
                [
                    new CheckboxFormField({
                        formControlName: 'blocked',
                        label: TranslateKey.blockShareholderSellSide,
                        width: 12,
                        value: shareholder.isSellBlocked,
                        style: {paddingBottom: '20px'}
                    }),
                    new ReadOnlyFormField({
                        formControlName: 'side',
                        value: Order.Side.SELL
                    })
                ]
            ]
        },

        {
            formArrayName: 'blockedPositions',
            formFields: []
        }
    ];
}
