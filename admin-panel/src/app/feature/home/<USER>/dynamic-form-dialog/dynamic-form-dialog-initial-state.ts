import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {FormGroup} from '@angular/forms';
import {FormFields} from '@modules/dynamic-form/form-field';
import {FormFieldSet} from '@modules/dynamic-form/form-field-sets';

export class DynamicFormDialogInitialState<T> {
    formTitle: TranslateKey;

    formFields: FormFields<T>;

    formFieldSets?: FormFieldSet[];

    hasKeepDialogOpen?: boolean;

    confirmMsg?: string;

    messageParams?: {[prop: string]: any} | (() => {[prop: string]: any});

    onSubmit: (formGroup?: FormGroup, keepOpenDialog?: boolean) => void;

    constructor({formTitle, formFields, formFieldSets, onSubmit, confirmMsg, hasKeepDialogOpen, messageParams}: DynamicFormDialogInitialState<T>) {
        this.formTitle = formTitle;
        this.formFields = formFields;
        this.formFieldSets = formFieldSets;
        this.confirmMsg = confirmMsg;
        this.messageParams = messageParams;
        this.hasKeepDialogOpen = hasKeepDialogOpen;
        this.onSubmit = onSubmit;
    }
}
