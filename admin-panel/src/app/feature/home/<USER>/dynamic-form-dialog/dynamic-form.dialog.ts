import {ChangeDetectionStrategy, Component, ViewChild} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {AbstractControl, FormGroup} from '@angular/forms';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {DynamicFormDialogInitialState} from './dynamic-form-dialog-initial-state';
import {FormFields} from '@modules/dynamic-form/form-field';
import {FormFieldSet} from '@modules/dynamic-form/form-field-sets';
import {DynamicFormComponent} from '@modules/dynamic-form/dynamic-form.component';

@Component({
    selector: 'app-dynamic-form-dialog',
    templateUrl: './dynamic-form.dialog.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class DynamicFormDialog<T extends { [K in keyof T]: AbstractControl<any, any>; }> implements DynamicFormDialogInitialState<T> {
    readonly translateKeys = TranslateKey;

    readonly formGroup = new FormGroup<T>({} as any);

    formTitle: TranslateKey;

    formFields: FormFields<T>;

    formFieldSets: FormFieldSet[];

    onSubmit: (formGroup: FormGroup, keepOpenDialog: boolean) => void;

    keepDialogOpen: boolean;

    confirmMsg: string;

    messageParams: {[prop: string]: any} | (() => {[prop: string]: any});

    hasKeepDialogOpen: boolean;

    @ViewChild(DynamicFormComponent)
    dynamicFormComponent: DynamicFormComponent;

    constructor(public modalRef: BsModalRef) {}

    getMessageParams(): {[prop: string]: any} {
        // Check if formGroup does not initialized.
        if (!(this.formFields[0] as any).formControl) {
            return
        }

        return typeof this.messageParams === 'function'
            ? this.messageParams()
            : this.messageParams;
    }
}
