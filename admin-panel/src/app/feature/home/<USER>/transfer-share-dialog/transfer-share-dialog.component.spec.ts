import {TestBed} from '@angular/core/testing';
import {TransferShareDialogComponent} from './transfer-share-dialog.component';
import {ModalHeaderComponent} from '@modules/shared-declarations/modal-header/modal-header.component';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {Http} from '@http';
import {HttpClient} from '@angular/common/http';
import {HttpClientTestingModule} from '@angular/common/http/testing';
import {SearchSchedulableComponent} from '../../components/search-schedulable/search-schedulable.component';
import {of, throwError} from 'rxjs';
import {TestUtils} from '@test/test-utils';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {SharedModule} from '../../../../shared/shared.module';
import {SharedDeclarations} from '@modules/shared-declarations/shared-declarations';
import {ComponentHarness} from '@test/harness/component-harness';
import {ShareholderDataService} from '@dataServices/shareholder-data.service';
import {CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA} from '@angular/core';
import {DynamicFormComponent} from '@modules/dynamic-form/dynamic-form.component';
import {FormControlsComponent} from '@modules/dynamic-form/form-controls/form-controls.component';
import {InputFormFieldComponent} from '@modules/dynamic-form/form-fields/input-field/input-form-field.component';
import {
    SelectOptionFormFieldComponent
} from '@modules/dynamic-form/form-fields/select-option-field/select-option-form-field.component';
import {FormFieldContainerComponent} from '@modules/dynamic-form/form-field-container/form-field-container.component';

describe('TransferShareDialogComponent', () => {
    let ha: ComponentHarness<TransferShareDialogComponent>;
    let shareholderDataService: ShareholderDataService;
    let modalRef: BsModalRef;

    beforeEach(() => {
        ha = new ComponentHarness(TransferShareDialogComponent, {
            declarations: [
                TransferShareDialogComponent,
                SearchSchedulableComponent,
                ModalHeaderComponent,
                DynamicFormComponent,
                FormControlsComponent,
                InputFormFieldComponent,
                SelectOptionFormFieldComponent,
                FormFieldContainerComponent,
            ],
            imports: [
                TranslateTestingModule,
                HttpClientTestingModule,
                SharedModule,
                SharedDeclarations
            ],
            providers: [BsModalRef],
            schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
            detectChanges: false
        })
        Http.httpClient = TestBed.inject(HttpClient);
        shareholderDataService = TestBed.inject(ShareholderDataService);
        modalRef = TestBed.inject(BsModalRef);
        ha.component.shareholder = TestUtils.getShareholder();

    });

    it('should create', () => {
        ha.detectChanges();
        expect(ha.component).toBeTruthy();
    });

    it('should display an error message if API was failed', async () => {
        spyOn(shareholderDataService, 'transferShare').and.returnValue(throwError({
            error: {messages : [ `'Shareholder' not found` ]}
        }));

        await fillFormAndSubmit();
        expect(ha.get('.alert')).toHaveClass('alert-danger');
    });

    it('should display a success message if API was successes', async () => {
        // Given
        spyOn(shareholderDataService, 'transferShare').and.returnValue(of(TestUtils.getTransferShareCommand()));
        // When
        await fillFormAndSubmit();
        // Then
        expect(ha.get('.alert')).toHaveClass('alert-success');
    });

    it('should display a pending message if API was pending', async () => {
        // When
        await fillFormAndSubmit();
        // Then
        expect(ha.get('.alert')).toHaveClass('alert-info');
    });

    it('should keep the dialog box open if keepDialogOpened was checked', async () => {
        // Given
        spyOn(shareholderDataService, 'transferShare').and.returnValue(of(TestUtils.getTransferShareCommand()));
        spyOn(modalRef, 'hide')
        // When
        ha.component.keepDialogOpened = true;
        await fillFormAndSubmit();
        // Then
        expect(modalRef.hide).not.toHaveBeenCalled();

        // When
        ha.component.keepDialogOpened = false;
        await fillFormAndSubmit();
        // Then
        expect(modalRef.hide).toHaveBeenCalled();
    });

    async function fillFormAndSubmit() {
        ha.detectChanges();

        ha.get('#productId').type('IRO1KVIR0002');
        ha.get('#quantity').type(500);
        ha.get('#sourceId').type(12692295592798)
        ha.get('#destinationId').type(12692295592798)

        ha.get('button').thatContains('Submit').click();
        await ha.fixture.whenStable();
    }
});
