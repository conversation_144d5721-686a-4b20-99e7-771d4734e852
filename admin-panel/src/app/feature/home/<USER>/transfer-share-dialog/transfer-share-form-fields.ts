import {Validators} from '@angular/forms';
import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {InputMasks} from '@constants/input-masks';
import {FieldsPlaceholder} from '../../../../shared/constants/fields-placeholder';
import {InputForm<PERSON>ield} from '@modules/dynamic-form/form-fields/input-field/input-form-field';
import {SelectOptionFormField} from '@modules/dynamic-form/form-fields/select-option-field/select-option-form-field';

export function transferShareFormFields(sourceId: string) {
    return [
        new InputFormField({
            formControlName: 'productId',
            label: TranslateKey.productId,
            width: 12,
            validations: [Validators.required],
            placeholder: FieldsPlaceholder.ISIN,
            mask: InputMasks.ISIN
        }),
        new InputFormField({
            formControlName: 'quantity',
            label: TranslateKey.quantity,
            width: 12,
            validations: [Validators.required],
            mask: InputMasks.COMMA_SEPERATED_NUMBER,
            placeholder: FieldsPlaceholder.DECIMAL_NUMBER
        }),
        new InputForm<PERSON>ield({
            formControlName: 'sourceId',
            label: TranslateKey.sourceId,
            width: 12,
            validations: [Validators.required],
            placeholder: FieldsPlaceholder.SHAREHOLDER_ID,
            value: sourceId,
            mask: InputMasks.SHAREHOLDER
        }),
        new InputFormField({
            formControlName: 'destinationId',
            label: TranslateKey.destinationId,
            width: 12,
            validations: [Validators.required],
            placeholder: FieldsPlaceholder.SHAREHOLDER_ID,
            mask: InputMasks.SHAREHOLDER
        }),
        new SelectOptionFormField({
            formControlName: 'blockedStatusIgnored',
            label: TranslateKey.ignoreBlockedStatus,
            validations: [Validators.required],
            width: 12,
            options: [true, false],
            value: false,
            getTitle(option: boolean): string { return option.toString(); }
        })
    ];
}
