import {NewCompanyDialogComponent} from './new-company-dialog.component';
import {ComponentHarness} from '@test/harness/component-harness';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {HttpClientTestingModule} from '@angular/common/http/testing';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {CompanyDataService} from '@dataServices/company-data.service';
import {companyDataServiceHarness} from '@dataServices/company-data.service.harness';
import {TranslateKey} from '@enums/translate-key';
import {Validators} from '@angular/forms';
import {CompanyFormFieldSets} from '../../pages/companies/company-form-field-sets';
import {Sector} from '@models/sector';
import {SubSector} from '@models/sub-sector';
import {TestUtils} from '@test/test-utils';
import {of} from 'rxjs';
import {Http} from '@http';
import {TestBed} from '@angular/core/testing';
import {HttpClient} from '@angular/common/http';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';
import {SelectOptionFormField} from '@modules/dynamic-form/form-fields/select-option-field/select-option-form-field';
import {SharedModule} from '@shared/shared.module';
import {FieldsPlaceholder} from '@shared/constants/fields-placeholder';

describe('NewCompanyDialogComponent', () => {

    let ha: ComponentHarness<NewCompanyDialogComponent>;
    let bsModalRefMock = { hide() {} };

    beforeEach(() => {
        ha = new ComponentHarness(NewCompanyDialogComponent, {
            declarations: [
                NewCompanyDialogComponent
            ],
            imports: [
                SharedModule,
                TranslateTestingModule,
                HttpClientTestingModule
            ],
            providers: [
                {provide: BsModalRef, useValue: bsModalRefMock},
                {provide: CompanyDataService, useValue: companyDataServiceHarness}
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
            detectChanges: false
        });

        Http.httpClient = TestBed.inject(HttpClient);

        spyOn(bsModalRefMock, 'hide').and.returnValue(null);

        spyOnProperty(ha.component, 'formFields', 'get').and.returnValue([
            {
                formGroupName: 'shortName',
                formFields: [
                    new InputFormField({
                        formControlName: 'fa',
                        label: TranslateKey.persianName,
                        width: 6,
                        validations: [Validators.required],
                        placeholder: FieldsPlaceholder.NAME_FA,
                        fieldSetId: CompanyFormFieldSets.SHORT_NAME
                    }),

                    new InputFormField({
                        formControlName: 'en',
                        label: TranslateKey.englishName,
                        width: 6,
                        validations: [Validators.required],
                        placeholder: FieldsPlaceholder.NAME_EN,
                        fieldSetId: CompanyFormFieldSets.SHORT_NAME
                    }),
                ]
            },

            {
                formGroupName: 'fullName',
                formFields: [
                    new InputFormField({
                        formControlName: 'fa',
                        label: TranslateKey.persianName,
                        width: 6,
                        validations: [Validators.required],
                        placeholder: FieldsPlaceholder.NAME_FA,
                        fieldSetId: CompanyFormFieldSets.FULL_NAME
                    }),

                    new InputFormField({
                        formControlName: 'en',
                        label: TranslateKey.englishName,
                        width: 6,
                        validations: [Validators.required],
                        placeholder: FieldsPlaceholder.NAME_EN,
                        fieldSetId: CompanyFormFieldSets.FULL_NAME
                    })
                ]
            },

            new InputFormField({
                formControlName: 'code',
                label: TranslateKey.code,
                width: 4,
                validations: [Validators.required],
                placeholder: 'SPY9',
            }),

            new SelectOptionFormField({
                formControlName: 'sectorCode',
                label: TranslateKey.sectorCode,
                width: 4,
                validations: [Validators.required],
                placeholder: 'زراعت و خدمات وابسته - 01',
                options: TestUtils.getSectors().content,
                getTitle(sector: Sector): string { return sector ? sector.code + ' - ' + sector.name : ''; },
                optionFieldName: 'code'
            }),

            new SelectOptionFormField({
                formControlName: 'subSectorCode',
                label: TranslateKey.subSectorCode,
                width: 4,
                validations: [Validators.required],
                placeholder: 'پرورش طيور - 0141',
                options: TestUtils.getSubSectors(),
                getTitle(subSector: SubSector): string { return subSector ? subSector.code + ' - ' + subSector.name : ''; },
                optionFieldName: 'code',
                disable: true
            })
        ]);
    });

    it('should create', () => {
        ha.detectChanges();
        expect(ha.component).toBeTruthy();
    });

    it('should call hide() on the modal', () => {
        ha.component.close();

        expect(bsModalRefMock.hide).toHaveBeenCalled();
    });

    it('should translateKeys return the TranslateKey enum', () => {
        expect(ha.component.translateKeys).toEqual(TranslateKey);
    });

    it('should call #addCompany and hide the modal', () => {
        spyOn(companyDataServiceHarness, 'addCompany').and.returnValue(of({}));
        ha.detectChanges();

        ha.component.addCompany();

        expect(companyDataServiceHarness.addCompany).toHaveBeenCalled();
        expect(bsModalRefMock.hide).toHaveBeenCalled();
    });
});
