import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {Validators} from '@angular/forms';
import {Sector} from '@models/sector';
import {SubSector} from '@models/sub-sector';
import {CompanyFormFieldSets} from '../../pages/companies/company-form-field-sets';
import {CustomValidators} from '@constants/custom-validators';
import {InputMasks} from '@constants/input-masks';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';
import {AutoCompleteFormField} from '@modules/dynamic-form/form-fields/auto-complete-field/auto-complete-form-field';
import {FormFields} from '@modules/dynamic-form/form-field';
import {FieldsPlaceholder} from '@shared/constants/fields-placeholder';

export function setupNewCompanyFormFields(): FormFields {
    return [
        {
            formGroupName: 'shortName',
            formFields: [
                new InputFormField({
                    formControlName: 'fa',
                    label: TranslateKey.persianName,
                    width: 6,
                    validations: [Validators.required, Validators.maxLength(30)],
                    placeholder: FieldsPlaceholder.NAME_FA,
                    fieldSetId: CompanyFormFieldSets.SHORT_NAME
                }),

                new InputFormField({
                    formControlName: 'en',
                    label: TranslateKey.englishName,
                    width: 6,
                    validations: [Validators.required, Validators.maxLength(18)],
                    placeholder: FieldsPlaceholder.NAME_EN,
                    fieldSetId: CompanyFormFieldSets.SHORT_NAME
                }),
            ]
        },

        {
            formGroupName: 'fullName',
            formFields: [
                new InputFormField({
                    formControlName: 'fa',
                    label: TranslateKey.persianName,
                    width: 6,
                    validations: [Validators.required, Validators.maxLength(255)],
                    placeholder: FieldsPlaceholder.NAME_FA,
                    fieldSetId: CompanyFormFieldSets.FULL_NAME
                }),

                new InputFormField({
                    formControlName: 'en',
                    label: TranslateKey.englishName,
                    width: 6,
                    validations: [Validators.required, Validators.maxLength(255)],
                    placeholder: FieldsPlaceholder.NAME_EN,
                    fieldSetId: CompanyFormFieldSets.FULL_NAME
                })
            ]
        },

        new AutoCompleteFormField<Sector>({
            formControlName: 'sectorCode',
            label: TranslateKey.sectorCode,
            width: 6,
            validations: [Validators.required, Validators.maxLength(2)],
            placeholder: 'زراعت و خدمات وابسته - 01',
            options: [],
            getTitle(sector) { return sector ? sector.code + ' - ' + sector.name : ''; },
            searchExpField: 'code',
            optionFieldName: 'code'
        }),

        new AutoCompleteFormField<SubSector>({
            formControlName: 'subSectorCode',
            label: TranslateKey.subSectorCode,
            width: 6,
            validations: [Validators.required, Validators.maxLength(4)],
            placeholder: 'پرورش طيور - 0141',
            options: [],
            getTitle(subSector) { return subSector ? subSector.code + ' - ' + subSector.name : ''; },
            optionFieldName: 'code',
            searchExpField: 'code',
            disable: true
        }),

        new InputFormField({
            formControlName: 'code',
            label: TranslateKey.code,
            width: 6,
            validations: [Validators.required, CustomValidators.fixedLength(4)],
            placeholder: 'RDN9',
            mask: InputMasks.COMPANY_CODE
        })
    ]
}
