import {Component} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {FormGroup} from '@angular/forms';
import {Company} from '@models/company';
import {Subject} from 'rxjs';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {SectorDataService} from '@dataServices/sector-data.service';
import {takeUntil} from 'rxjs/operators';
import {CompanyDataService} from '@dataServices/company-data.service';
import {setupNewCompanyFormFields} from './setup-new-company-form-fields';
import {CompanyFormFieldSets} from '../../pages/companies/company-form-field-sets';
import {ChangeDetectionService} from '@services/change-detection.service';
import {FormFieldSet} from '@modules/dynamic-form/form-field-sets';
import {FormFields} from '@modules/dynamic-form/form-field';

@Component({
    selector: 'app-new-company-dialog',
    templateUrl: './new-company-dialog.component.html',
    standalone: false
})
export class NewCompanyDialogComponent {
    readonly translateKeys = TranslateKey;

    readonly formGroup = new FormGroup<Company.NewForm>({} as any);

    readonly formFieldSets: FormFieldSet[] = [
        {title: TranslateKey.shortName, id: CompanyFormFieldSets.SHORT_NAME},
        {title: TranslateKey.fullName, id: CompanyFormFieldSets.FULL_NAME}
    ];

    private _formFields: FormFields;
    get formFields(): FormFields { return this._formFields; };

    private readonly _onDestroy = new Subject();

    constructor(
        private _modalRef: BsModalRef,
        private _productDataService: CompanyDataService,
        private _sectorDataService: SectorDataService
    ) { }

    ngOnInit(): void {
        this._formFields = setupNewCompanyFormFields();
        this._setAllSectors();
    }

    ngAfterViewInit(): void {
        this._subscribeOnSectorCode();
    }

    ngOnDestroy(): void {
        this._onDestroy.next();
        this._onDestroy.complete();
    }

    addCompany(): void {
        this._productDataService
            .addCompany(this.formGroup.getRawValue())
            .subscribe(() => this._modalRef.hide());
    }

    close(): void {
        this._modalRef.hide();
    }

    private _subscribeOnSectorCode(): void {
        this.formGroup.controls.sectorCode?.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(resp => {
            if (resp) {
                this.formGroup.controls.subSectorCode.enable();
                this.formGroup.controls.subSectorCode.setValue(null);
                this._setAllSubSectors(resp);
            }
        });
    }

    private _setAllSectors(): void {
        this._sectorDataService
            .getAllSectors()
            .subscribe(resp => {
                (this._formFields.rawValue.sectorCode as any).options = resp.content;
                ChangeDetectionService.onChange.next({type: 'FORM'});
            });
    }

    private _setAllSubSectors(sectorCode: string): void {
        this._sectorDataService
            .getSubSectors(sectorCode)
            .subscribe(resp => {
                (this._formFields.rawValue.subSectorCode as any).options = resp.content;
                ChangeDetectionService.onChange.next({type: 'FORM'});
            });
    }
}
