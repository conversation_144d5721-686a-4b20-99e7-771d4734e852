@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";

.expand-arrow {
    display: flex;
    justify-content: center;
    cursor: pointer;
    padding-top: 5px;

    &__icon {
        transform: rotate(var(--primary-rotate));

        &--expanded {
            transform: rotate(90deg);
        }
    }
}

::ng-deep {
    .cell-updated {
        background-color: mix($warning, white, 15%)!important;
        color: mix(black, $warning);
    }

    .cell-deleted {
        background-color: mix($danger, white, 15%)!important;
        color: mix(black, $danger);
    }

    .cell-created {
        background-color: mix($success, white, 15%)!important;
        color: mix(black, $success);
    }
}
