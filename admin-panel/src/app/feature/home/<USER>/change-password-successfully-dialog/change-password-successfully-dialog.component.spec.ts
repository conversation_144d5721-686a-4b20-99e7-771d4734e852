import {TestBed} from '@angular/core/testing';
import {SharedModule} from '../../../../shared/shared.module';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {ChangePasswordSuccessfullyDialogComponent} from './change-password-successfully-dialog.component';
import {RouterTestingModule} from '@angular/router/testing';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {ModalHeaderComponent} from '@modules/shared-declarations/modal-header/modal-header.component';
import {Router} from '@angular/router';
import {of} from 'rxjs';
import {ComponentHarness} from '@test/harness/component-harness';
import {LoginDataService} from '@dataServices/login-data.service';

describe('ChangePasswordSuccessfullyDialogComponent', () => {
    let ha: ComponentHarness<ChangePasswordSuccessfullyDialogComponent>;
    let authDataService: LoginDataService;
    let router: Router;

    beforeEach(() => {
        ha = new ComponentHarness(ChangePasswordSuccessfullyDialogComponent, {
            declarations: [
                ChangePasswordSuccessfullyDialogComponent,
                ModalHeaderComponent
            ],
            imports: [
                TranslateTestingModule,
                RouterTestingModule,
                SharedModule
            ],
            providers: [
                BsModalRef
            ],
            detectChanges: false
        });

        authDataService = TestBed.inject(LoginDataService);
        router = TestBed.inject(Router);

        ha.detectChanges();
    });

    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });

    it('should logout user by clicking on Logout button', () => {
        spyOn(authDataService, 'logout').and.returnValue(of(null));
        spyOn(authDataService, 'invalidateAuthToken');
        spyOn(router, 'navigate').and.returnValue(new Promise(() => {}));

        ha.get('button').thatContains('Logout').click();
        expect(authDataService.invalidateAuthToken).toHaveBeenCalled();
    })
});
