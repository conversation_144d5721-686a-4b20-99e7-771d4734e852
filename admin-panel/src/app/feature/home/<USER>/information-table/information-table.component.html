<div style="display: flow-root">
    <div class="px-2"
         [class.col-12]="tableGroupsData.length == 1 || group.width == 12"
         [class.col-6]="tableGroupsData.length > 1 && group?.float || group.width == 6"
         *ngFor="let group of tableGroupsData"
         [ngClass]="group?.float">
        <div class="card card-body my-1 transparent-box">
            <h6 *ngIf="group.groupName">{{ group.groupName | translate }}</h6>

            <div
                *ngFor="let row of group.data"
                [attr.title]="row.value | titleValue | dynamicPipe:row.pipeToken:row.pipeArgs"
                class="card-row">
                <span class="text-nowrap space">{{ row.title | translate }}</span>
                <span
                    dir="auto"
                    class="text-truncate"
                    [innerHTML]="row.value | dynamicPipe:row.pipeToken:row.pipeArgs"></span>
            </div>
        </div>
    </div>
</div>
