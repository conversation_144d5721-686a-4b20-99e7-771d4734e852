<app-modal-header>{{ dialogTitle | translate }}</app-modal-header>

<div class="modal-body">
    <p *ngIf="fileHeader">
        <Strong>{{translateKeys.fileHeaderStructure | translate}}: </Strong><span dir="ltr">{{ fileHeader }}</span>
    </p>

    <app-upload-file
        [uploadMethod]="uploadMethod"
        (onUploadProgress)="uploadProgress($event)">

        <ul>
            <li *ngFor="let item of uploadInfo">
                {{ item.title | translate }}: {{ item.value | dynamicPipe:item.pipeToken:item.pipeArgs }}
            </li>
        </ul>
    </app-upload-file>
</div>
