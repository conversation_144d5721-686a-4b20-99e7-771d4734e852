<!-- Upload Box -->
<div class="d-flex justify-content-center my-2">
    <div appDragDrop
         (fileDropped)="onSelectFile($event[0])"
         (click)="isFileSelected ? false : fileInput.click()"
         class="border border-secondary flex-center flex-column"
         style="width: 300px; height: 150px; border-style: dashed!important; border-width: 2px!important;">
        <div [class.d-none]="isFileSelected"
             [class.d-flex]="!isFileSelected"
             class="flex-column align-items-center">
            <div>{{translateKeys.dragAndDropFileHere | translate}}</div>
            <div>{{translateKeys.or | translate}}</div>
            <button class="btn btn-primary" type="button">{{translateKeys.browseFile | translate}}</button>
            <input
                #fileInput
                hidden
                type="file"
                (change)="onSelectFile($event.target.files[0])">
        </div>

        <div *ngIf="fileIsSelectedButIsNotUploading" class="d-flex flex-column align-items-center">
            <div *ngIf="isFileSelected" dir="ltr">{{fileName}}</div>

            <div class="mt-2">
                <button
                    type="button"
                    class="btn btn-primary mx-1"
                    (click)="uploadFile(); $event.stopPropagation()">
                    {{translateKeys.uploadFile | translate}}
                </button>

                <button
                    type="button"
                    class="btn btn-secondary mx-1"
                    (click)="fileName = null; $event.stopPropagation()">
                    {{translateKeys.cancel | translate}}
                </button>
            </div>
        </div>

        <h2 *ngIf="isUploading" class="font-weight-normal">
            <ng-container *ngIf="isPending">{{uploadTime}}</ng-container>
            <ng-container *ngIf="isSendingOrder">{{fileProcessTime}}</ng-container>
        </h2>
        <!-- Progress bar -->
        <div class="progress mt-1"
             style="width: 90%"
             *ngIf="isUploading">
            <!-- Uploading phase -->
            <div class="progress-bar progress-bar-striped progress-bar-animated w-100 bg-warning"
                 *ngIf="isPending"></div>
            <!-- Progressing phase -->
            <div class="progress-bar"
                 [ngStyle]="{width: uploadPercentage}"
                 *ngIf="isSendingOrder">
                {{uploadPercentage}}
            </div>
        </div>

        <div *ngIf="isPending">{{translateKeys.loading | translate}}...</div>
    </div>
</div>

<!-- Uploading Status -->
<alert
    type="info"
    class="d-block mt-4" *ngIf="uploadInfo">
    <h5 class="alert-heading mb-4">
        {{translateKeys.uploadInfo | translate}}
        <small class="text-muted" *ngIf="isUploading"> {{translateKeys.processing | translate}}...</small>
    </h5>

    <ng-content></ng-content>
</alert>

<!-- Upload Error Status -->
<alert
    type="danger"
    *ngIf="hasUploadStatusError"
    class="d-block mt-4">
    {{translateKeys.uploadOrderFileError | translate}}
</alert>
