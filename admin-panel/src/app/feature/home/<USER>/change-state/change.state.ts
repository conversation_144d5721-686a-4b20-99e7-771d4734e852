import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {Subject} from 'rxjs';
import {ChangeDetectorRef, inject} from '@angular/core';
import {Group} from '@models/group';
import {FormGroup} from '@angular/forms';
import {ChangeStateFormFieldsConstant} from './change-state-form-fields.constant';
import {DynamicFormDialogService} from '../../dialogs/dynamic-form-dialog/dynamic-form-dialog.service';
import {InstrumentState} from '@homeModels/instrument';

const States = {...InstrumentState, ...Group.State};

type DropdownBadgeClasses = {
    [key in keyof typeof States as key]: string;
}

export abstract class ChangeState<T extends InstrumentState | Group.State> {
    readonly translateKeys = TranslateKey;

    protected readonly _changeDetectorRef = inject(ChangeDetectorRef);

    protected readonly _dynamicFormDialogService: DynamicFormDialogService;

    protected readonly _onChange = new Subject();

    readonly dropdownBadgeClasses: DropdownBadgeClasses = {
        CONTINUOUS_TRADING: 'badge-success',
        PRE_OPENING: 'badge-warning',
        SUSPENDED: 'badge-danger',
        FORBIDDEN: 'badge-secondary',
        HALTED: 'badge-secondary',
        RESERVED: 'badge-warning',
        SURVEILLANCE: 'badge-primary',
        FROZEN: 'badge-info',
        AUCTION: 'badge-dark',
        GROUP_AUCTION: 'badge-dark',
        AUTHORIZED: 'badge-success'
    };

    protected _isChanged: boolean;

    selectedState: T;

    currentState: T;

    state: T;

    statesLayout: T[];

    permissions: any[];

    get dropdownTitle(): T {
        return this.currentState || this.state;
    }

    changeState(state: T): void {
        this.selectedState = state;
        this._isChanged = null;

        this._openChangeStateDialog();
    }

    isCurrentState(state: T): boolean {
        return state === this.currentState;
    }

    selectCurrentState(): void {
        this.currentState = this.state;
        this.selectedState = this.state;

        this._changeDetectorRef.detectChanges();
    }

    abstract _onSubmitForm(formGroup: FormGroup): void;

    private _openChangeStateDialog(): void {
        this._dynamicFormDialogService.open({
            formTitle: TranslateKey.changeState,
            formFields: ChangeStateFormFieldsConstant(this.currentState, this.selectedState),
            onSubmit: this._onSubmitForm.bind(this)
        });
    }
}
