import {BrokerAccessMatrixPage} from './broker-access-matrix.page';
import {ComponentHarness} from '@test/harness/component-harness';
import {BsModalService} from 'ngx-bootstrap/modal';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {RouterTestingModule} from '@angular/router/testing';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {AccessMatrixDataService} from '../../shared/data-services/access-matrix-data.service';
import {accessMatrixDataServiceHarness} from '../../shared/data-services/access-matrix-data.service.harness';
import {of} from 'rxjs';
import {TestUtils} from '@test/test-utils';
import {MutableAny} from '@models/form-group-raw-value';
import {BrokerAccessMatrix} from '@homeModels/access-matrix/broker-access-matrix';

describe('BrokerAccessMatrixPage', () => {
    let ha: ComponentHarness<BrokerAccessMatrixPage>;

    beforeEach(() => {
        ha = new ComponentHarness(BrokerAccessMatrixPage, {
            imports: [
                BrokerAccessMatrixPage,
                TranslateTestingModule,
                RouterTestingModule
            ],
            providers: [
                BsModalService,
                {
                    provide: AccessMatrixDataService,
                    useValue: accessMatrixDataServiceHarness
                }
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
            detectChanges: false
        });
    });

    it('should create', () => {
        ha.detectChanges();
        expect(ha.component).toBeTruthy();
    });

    it('should call updateAccessMatrix with the correct body when updating broker access matrix', () => {
        spyOn(accessMatrixDataServiceHarness, 'updateAccessMatrix').and.returnValue(of());
        const brokerAccessMatrix = TestUtils.getBrokerGroup().content[0];
        ha.component.updateBrokerAccessMatrix(brokerAccessMatrix);

        const expectedBody: Omit<MutableAny<BrokerAccessMatrix>, 'group'> = {
            isSellBlocked: jasmine.anything(),
            isBuyBlocked: jasmine.anything(),
            groupCode: jasmine.anything(),
            brokerId: jasmine.anything()
        };
        expect(accessMatrixDataServiceHarness.updateAccessMatrix)
            .toHaveBeenCalledWith(jasmine.objectContaining(expectedBody));
    });
});
