import {DataTableColumn} from '@models/data-table';
import {TranslateKey} from '@enums/translate-key';
import {InputFilter} from '@modules/filter/input/input-filter';
import {BrokerAccessMatrix} from '@homeModels/access-matrix/broker-access-matrix';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';

export function BrokerAccessMatrixDataTableColumns(): DataTableColumn<BrokerAccessMatrix>[] {
    return [
        {
            title: TranslateKey.group,
            pipeToken: SelectByLanguagePipe,
            value(data) { return data.group.uniqueName },
            filter: new InputFilter({
                queryParam: 'groupCode'
            })
        },
        {
            title: TranslateKey.broker,
            value(data) { return data.brokerId },
            filter: new InputFilter({
                queryParam: 'brokerId'
            })
        },
        {
            title: TranslateKey.buySideIsBlocked,
            hasTemplate: true,
            value(): any {}
        },
        {
            title: TranslateKey.sellSideIsBlocked,
            hasTemplate: true,
            value(): any {}
        }
    ];
}
