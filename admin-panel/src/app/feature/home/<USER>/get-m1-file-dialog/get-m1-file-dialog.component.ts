import {ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit} from '@angular/core';
import {M1FileDataService} from '@dataServices/m1-file-data.service';
import {M1} from '@enums/m1';
import {ApiRequestStatus} from '@enums/api-request-status';
import {Page} from '@models/page';
import {TranslateKey} from '@enums/translate-key';
import {FileDownloaderService} from '@services/file-downloader.service';

@Component({
    selector: 'app-get-m1-file-dialog',
    templateUrl: './get-m1-file-dialog.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class GetM1FileDialogComponent implements OnInit {
    private readonly _translateKeys = TranslateKey;
    get translateKeys() { return this._translateKeys; }

    listOfM1Files: M1[] = [];

    private _apiRequestStatus: ApiRequestStatus;

    get isApiStatusPending(): boolean {
        return this._apiRequestStatus === ApiRequestStatus.PENDING;
    }

    get isApiStatusSuccess(): boolean {
        return this._apiRequestStatus === ApiRequestStatus.SUCCESS;
    }

    get isApiStatusFailed(): boolean {
        return this._apiRequestStatus === ApiRequestStatus.FAILED;
    }

    get hasNoDataToDisplay(): boolean {
        return this.isApiStatusSuccess && this.listOfM1Files.length === 0;
    }

    constructor(
        private _m1FileDataService: M1FileDataService,
        private _changeDetectorRef: ChangeDetectorRef
    ) { }

    ngOnInit(): void {
        this.getM1Files();
    }

    getM1Files(): void {
        this._apiRequestStatus = ApiRequestStatus.PENDING;

        this._m1FileDataService
            .getAllM1Files()
            .subscribe({
                next: this._onGetAllM1FilesSuccess.bind(this),
                error: this._onGetAllM1FilesError.bind(this)
            })
    }

    getM1FileByEndOffset(m1: M1): void {
        this._m1FileDataService
            .getM1FileByEndOffset(m1.endOffset)
            .subscribe(fileContent => FileDownloaderService.download(fileContent, `M1-${m1.endOffset}.mmtp`))
    }

    getM1LastMessages(): void {
        const n = this.listOfM1Files.length - 1;
        const endOffset = n > -1 ? this.listOfM1Files[n].endOffset : 0;

        this._m1FileDataService
            .getM1LastMessages()
            .subscribe(fileContent => {
                FileDownloaderService.download(fileContent, `M1-${endOffset}.mmtp`);
                this.getM1Files();
            })
    }

    createM1Messages() {
        this._m1FileDataService
            .getM1LastMessages()
            .subscribe(this.getM1Files.bind(this));
    }

    deleteAllM1Files(): void {
        this._m1FileDataService
            .deleteAllM1Files()
            .subscribe(() => {
                this.listOfM1Files = [];
                this._changeDetectorRef.detectChanges();
            })
    }

    private _onGetAllM1FilesSuccess(resp: Page<M1>): void {
        this.listOfM1Files = resp.content;
        this._apiRequestStatus = ApiRequestStatus.SUCCESS;
        this._changeDetectorRef.detectChanges();
    }

    private _onGetAllM1FilesError(): void {
        this._apiRequestStatus = ApiRequestStatus.FAILED;
        this._changeDetectorRef.detectChanges();
    }
}
