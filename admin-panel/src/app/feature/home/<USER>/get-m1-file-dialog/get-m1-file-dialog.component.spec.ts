import {TestBed} from '@angular/core/testing';
import {GetM1FileDialogComponent} from './get-m1-file-dialog.component';
import {Http} from '@http';
import {HttpClient} from '@angular/common/http';
import {ModalHeaderComponent} from '@modules/shared-declarations/modal-header/modal-header.component';
import {M1FileDataService} from '@dataServices/m1-file-data.service';
import {of} from 'rxjs';
import {Page} from '@models/page';
import {M1} from '@enums/m1';
import {TestUtils} from '@test/test-utils';
import {RouterTestingModule} from '@angular/router/testing';
import {HttpClientTestingModule} from '@angular/common/http/testing';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {SharedModule} from '../../../../shared/shared.module';
import {BsModalRef, ModalModule} from 'ngx-bootstrap/modal';
import {SharedDeclarations} from '@modules/shared-declarations/shared-declarations';
import {FileDownloaderService} from '@services/file-downloader.service';
import {ComponentHarness} from '@test/harness/component-harness';

describe('GetM1FileDialogComponent', () => {
    const M1_FILES_MOCK_DATA = TestUtils.getM1File();
    let ha: ComponentHarness<GetM1FileDialogComponent>;
    let m1FileDataService: M1FileDataService;

    beforeEach(() => {
        ha = new ComponentHarness(GetM1FileDialogComponent, {
            declarations: [
                GetM1FileDialogComponent,
                ModalHeaderComponent
            ],
            imports: [
                HttpClientTestingModule,
                TranslateTestingModule,
                RouterTestingModule,
                ModalModule.forRoot(),
                SharedModule,
                SharedDeclarations
            ],
            providers: [BsModalRef],
            detectChanges: false
        });

        Http.httpClient = TestBed.inject(HttpClient);
        m1FileDataService = TestBed.inject(M1FileDataService);
        ha.detectChanges();
    });

    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });

    it('should display "loading..." message when API status is pending', () => {
        expect(ha.get('.modal-body div')).toHaveContained('Loading...');
    });

    it('should display "noDataToDisplay" message when API has no value', () => {
        // Given
        spyOnGetAllM1Files([]);
        ha.component.ngOnInit();
        // Then
        expect(ha.get('.alert')).toBeExists();
    });

    it('should display "Create M1 Messages" btn and hide "deleteAllM1Files" btn when API has no value', () => {
        spyOnGetAllM1Files([]);
        ha.component.ngOnInit();

        expect(ha.get('.btn-danger')).not.toBeExists();
        expect(ha.get('button').thatContains('Create M1 Messages')).toBeExists();
    });

    it('should display list of M1 files and action buttons when API has value', () => {
        // Given
        spyOnGetAllM1Files(M1_FILES_MOCK_DATA);
        ha.component.ngOnInit();
        // Then
        expect(ha.component.listOfM1Files).toBe(M1_FILES_MOCK_DATA);
        expect(ha.get('.btn-danger')).toBeExists();
        expect(ha.get('.btn-primary')).toBeExists();
    });

    it('should get M1 file by clicking on the list items', () => {
        // Given
        spyOn(m1FileDataService, 'getM1FileByEndOffset').and.returnValue(of({}));
        spyOn(FileDownloaderService, 'download');
        spyOnGetAllM1Files(M1_FILES_MOCK_DATA);
        ha.component.ngOnInit();
        // When
        ha.get('a').thatContains('Download File').click();
        // Then
        expect(FileDownloaderService.download).toHaveBeenCalled();
    });

    it('should get M1 last messages file', () => {
        // Given
        spyOn(m1FileDataService, 'getM1LastMessages').and.returnValue(of({}));
        spyOn(FileDownloaderService, 'download');
        spyOnGetAllM1Files(M1_FILES_MOCK_DATA);
        ha.component.ngOnInit();
        // When
        ha.get('.btn-primary').click();
        // Then
        expect(FileDownloaderService.download).toHaveBeenCalled();
    });

    it('should delete all M1 messages', () => {
        // Given
        spyOn(m1FileDataService, 'deleteAllM1Files').and.returnValue(of(null));
        spyOnGetAllM1Files(M1_FILES_MOCK_DATA);
        ha.component.ngOnInit();
        // When
        ha.component.deleteAllM1Files();
        // Then
        expect(ha.component.listOfM1Files.length).toBe(0);
    });

    function spyOnGetAllM1Files(value: M1[]): void {
        spyOn(m1FileDataService, 'getAllM1Files').and.returnValue(of(new Page(value)));
    }
});
