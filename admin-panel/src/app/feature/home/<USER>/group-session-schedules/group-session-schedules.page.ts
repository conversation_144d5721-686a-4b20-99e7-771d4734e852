import {AfterViewInit, ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {PagesSharedModule} from '../../shared/modules/pages-shared.module';
import {PaginatedDataTable} from '@models/paginated-data-table';
import {SessionSchedule} from '@homeModels/session-schedule';
import {TranslateKey} from '@enums/translate-key';
import {Permissions} from '../../../../shared/constants/permissions.constant';
import {MatPaginator} from '@angular/material/paginator';
import {BsModalService} from 'ngx-bootstrap/modal';
import {ScheduleDataService} from '@dataServices/schedule-data.service';
import {map, takeUntil} from 'rxjs/operators';
import {Observable} from 'rxjs';
import {RequestOptions} from '@models/request-options';
import {GroupSessionSchedulesDataTableColumns} from './group-session-schedules-data-table-columns';
import {SystemState} from '@models/system-state';
import {StoreService} from '@services/store.service';
import {
    CreateBatchSessionScheduleComponent
} from '../../dialogs/create-batch-session-schedule/create-batch-session-schedule.component';
import {MbDatePipe} from '@modules/datepicker/mb-date.pipe';
import {UtilConstants} from '@constants/util-constants';
import {GDate} from 'mb-date/dist/src/gdate';

@Component({
    selector: 'app-group-session-schedules',
    imports: [PagesSharedModule],
    templateUrl: './group-session-schedules.page.html'
})
export class GroupSessionSchedulesPage extends PaginatedDataTable<SessionSchedule.Group> implements OnInit, AfterViewInit {

    readonly translateKeys = TranslateKey;

    readonly permissions = Permissions;

    readonly columns = GroupSessionSchedulesDataTableColumns();

    get isPostSession(): boolean {
        return this._currentState === SystemState.State.POST_SESSION;
    }

    private _currentState: SystemState.State;

    @ViewChild('tableContainer')
    override _tableContainer: ElementRef<HTMLDivElement>;

    @ViewChild(MatPaginator)
    override _paginator: MatPaginator;

    constructor(
        protected _changeDetectorRef: ChangeDetectorRef,
        private _modalService: BsModalService,
        private _scheduleDataService: ScheduleDataService
    ) {
        super();

        this._setFilterProperty();
    }

    ngOnInit(): void {
        this._fetchSystemState();

        this._subscribeOnModalHide();
    }

    override ngAfterViewInit() {
        this._changeDetectorRef.detectChanges();
        this._refreshPage();
        this._subscribeOnPageChange();
    }

    isDateToday(sessionSchedule: SessionSchedule.Group): boolean {
        const systemDate = (new MbDatePipe()).transform(StoreService.systemDateTime, UtilConstants.ENDPOINT_DATE_FORMAT);
        const scheduleDate = (new MbDatePipe()).transform(sessionSchedule.date, UtilConstants.ENDPOINT_DATE_FORMAT);
        return  systemDate === scheduleDate;
    }

    editSessionSchedule(sessionSchedule: SessionSchedule.Group): void {
        this._modalService.show(CreateBatchSessionScheduleComponent, {
            class: 'modal-lg non-scrollable-modal-body',
            initialState: new SessionSchedule.GroupScheduleFormDialog(sessionSchedule)
        });
    }

    deleteSessionSchedule(sessionSchedule: SessionSchedule.Group): void {
        const body = {
            groupCodes: [sessionSchedule.group.code],
            actions: []
        };
        this._scheduleDataService.updateGroupSessionSchedule(body).subscribe(this._refreshPageData.bind(this));
    }

    override fetchExcelData(): Observable<SessionSchedule.Group[]> {
        const requestOptions: RequestOptions = {
            params: this.getFilterQueryParams()
        }

        return this._scheduleDataService
            .getGroupSessionSchedules(requestOptions)
            .pipe(map(resp => resp.content));
    }

    override _fetchPageData(): void {
        const requestOptions: RequestOptions = {
            hasLocalErrorHandler: true,
            params: this.getFilterQueryParams()
        }

        this._scheduleDataService
            .getGroupSessionSchedules(requestOptions)
            .pipe(takeUntil(this._onDestroy))
            .subscribe(resp => {
                this._pageData = resp;
                this._changeDetectorRef.detectChanges();
            });
    }

    private _setFilterProperty(): void {
        this.filterProperty.actionBtns = [
            {
                title: TranslateKey.createScheduleAction,
                icon: 'more_time',
                isDisabled: () => this.isPostSession,
                callback: this._openCreateBatchSessionScheduleDialog.bind(this),
                permissions: this.permissions.UPDATE_GROUP_SESSION_SCHEDULE_PERMISSION
            }
        ];
        this.filterProperty.refreshPage = this._refreshPageData.bind(this);
        this.filterProperty.callback = this._filterPageData.bind(this);
    }

    private _openCreateBatchSessionScheduleDialog(): void {
        this._modalService.show(CreateBatchSessionScheduleComponent, {
            class: 'modal-lg non-scrollable-modal-body',
        });
    }

    private _fetchSystemState(): void {
        StoreService.systemState.subscribe(resp => {
            this._currentState = resp;
            this._changeDetectorRef.detectChanges();
        });
    }

    private _subscribeOnModalHide(): void {
        this._modalService.onHide
            .pipe(takeUntil(this._onDestroy))
            .subscribe(this._refreshPageData.bind(this));
    }

    private _refreshPage(): void {
        this._calcPageSize();
        this._filterByDate();
    }

    private _filterByDate(): void {
        const systemDate = StoreService.systemDateTime;
        const from = new GDate(systemDate).format(UtilConstants.ENDPOINT_DATE_FORMAT);
        const to = new GDate(systemDate).format(UtilConstants.ENDPOINT_DATE_FORMAT);

        this.filterProperty.filterParams['date'] = `${from}..${to}`;
        this._fetchPageData();
    }
}
