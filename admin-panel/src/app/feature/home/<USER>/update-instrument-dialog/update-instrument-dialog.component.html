<app-modal-header>{{translateKeys.editInstrument | translate}} {{instrument?.mnemonic | selectByLanguage}}</app-modal-header>

<!-- Body -->
<div class="modal-body overflow-auto">
    <app-dynamic-form
        *ngIf="formFields"
        [formFields]="formFields"
        [formGroup]="formGroup"
        [formFieldSets]="formFieldSets"></app-dynamic-form>
</div>

<div class="modal-footer">
    <button
        type="button"
        mat-flat-button
        color="primary"
        class="w-auto"
        [disabled]="formGroup.invalid"
        (click)="onSubmit()">
        {{translateKeys.submit | translate}}
    </button>

    <button
        type="button"
        mat-button
        (click)="modalRef.hide()">
        {{translateKeys.cancel | translate}}
    </button>
</div>
