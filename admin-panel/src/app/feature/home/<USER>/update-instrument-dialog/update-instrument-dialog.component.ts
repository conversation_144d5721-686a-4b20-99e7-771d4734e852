import {ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {FormGroup} from '@angular/forms';
import {TranslateKey} from '@enums/translate-key';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {InstrumentDataService} from '@dataServices/instrument-data.service';
import {InstrumentDialog} from '@models/instrument-dialog';
import {Instrument} from '@homeModels/instrument';
import {SnackBarService} from '@services/snack-bar.service';
import {UpdateInstrumentFormFieldSets} from './update-instrument-form-field-sets';
import {setupUpdateInstrumentFormFields} from './setup-update-instrument-form-fields';
import {Subject} from 'rxjs';
import {FormFields} from '@modules/dynamic-form/form-field';
import {FormFieldSet} from '@modules/dynamic-form/form-field-sets';


@Component({
    selector: 'app-update-instrument-dialog',
    templateUrl: './update-instrument-dialog.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class UpdateInstrumentDialogComponent implements OnInit, OnDestroy, InstrumentDialog<Instrument.Single> {
    readonly translateKeys = TranslateKey;

    readonly formGroup = new FormGroup({});

    instrument: Instrument.Single;

    private _formFields: FormFields;
    get formFields(): FormFields { return this._formFields; }

    private _formFieldSets: FormFieldSet[] = [
        {title: TranslateKey.orderSpec, id: UpdateInstrumentFormFieldSets.ORDER_SPEC}
    ];
    get formFieldSets(): FormFieldSet[] { return this._formFieldSets; }

    private _onDestroy = new Subject();

    constructor(
        public modalRef: BsModalRef,
        private _instrumentDataService: InstrumentDataService,
        private _snackBarService: SnackBarService,
        private _changeDetectorRef: ChangeDetectorRef
    ) {}

    ngOnInit(): void {
        this._fetchInstrumentStaticData();
    }

    ngOnDestroy(): void {
        this._onDestroy.next();
        this._onDestroy.complete();
    }

    onSubmit(): void {
        this._instrumentDataService
            .editQueueInstrument(this.instrument.securityId, this.formGroup.value)
            .subscribe(() => {
                this._snackBarService.open(TranslateKey.successfullySubmitted);
                this.modalRef.hide();
            });
    }

    private _fetchInstrumentStaticData(): void {
        this._instrumentDataService
            .getStaticData(this.instrument.securityId)
            .subscribe(resp => {
                this.instrument = resp;
                this._formFields = setupUpdateInstrumentFormFields(this.instrument);
                this._changeDetectorRef.detectChanges();
            })
    }
}
