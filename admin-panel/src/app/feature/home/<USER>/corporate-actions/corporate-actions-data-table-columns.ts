import {DataTableColumn} from '@models/data-table';
import {TranslateKey} from '@enums/translate-key';
import {TranslatePipe} from '@ngx-translate/core';
import {QueuedCommand} from '@models/queued-command';
import {InputFilter} from '@modules/filter/input/input-filter';
import {InputMasks} from '@constants/input-masks';
import {SelectOptionFilter} from '@modules/filter/select-option/select-option-filter';
import {AdminCommand} from '@models/admin-command';
import {enumToKeyValue, toCamelCase} from '@core/utils';

export function corporateActionsDataTableColumns(): DataTableColumn<QueuedCommand>[] {
    return [
        {
            title: TranslateKey.productId,
            isSticky: true,
            class: 'monospace',
            value(data) { return data.productId },
            filter: new InputFilter<string>({
                imask: InputMasks.ISIN,
                queryParam: 'entityId'
            })
        },
        {
            title: TranslateKey.commands,
            pipeToken: TranslatePipe,
            value(data) { return toCamelCase(data.adminCommandType) },
            filter: new SelectOptionFilter({
                data: enumToKeyValue(AdminCommand.QueuedCorporateActionType),
                queryParam: 'adminCommandType',
                queryParamValueField: 'key'
            })
        },
        {
            title: TranslateKey.status,
            pipeToken: TranslatePipe,
            value(data) { return toCamelCase(data.status) },
            filter: new SelectOptionFilter({
                data: enumToKeyValue(QueuedCommand.Status),
                queryParam: 'status',
                queryParamValueField: 'key'
            })
        }
    ];
}
