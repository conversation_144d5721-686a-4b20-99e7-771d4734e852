import {CorporateActionsPage} from './corporate-actions.page';
import {ComponentHarness} from '@test/harness/component-harness';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {RouterTestingModule} from '@angular/router/testing';
import {queuedCommandDataServiceHarness} from '@dataServices/queued-command-data.service.harness';
import {QueuedCommandDataService} from '@dataServices/queued-command-data.service';
import {BsModalService} from 'ngx-bootstrap/modal';
import {of} from 'rxjs';
import {QueuedCommand} from '@models/queued-command';

describe('CorporateActionsPage', () => {
    let ha: ComponentHarness<CorporateActionsPage>;

    beforeEach(() => {
        ha = new ComponentHarness(CorporateActionsPage, {
            imports: [
                CorporateActionsPage,
                TranslateTestingModule,
                RouterTestingModule
            ],
            providers: [
                BsModalService,
                {provide: QueuedCommandDataService, useValue: queuedCommandDataServiceHarness}
            ],
            detectChanges: false
        });
    });

    it('should create', () => {
        ha.detectChanges();
        expect(ha.component).toBeTruthy();
    });

    it('should fetch page data with "majorPriority" query param', () => {
        spyOn(queuedCommandDataServiceHarness, 'getAllQueuedCommands').and.returnValue(of());
        ha.detectChanges();

        expect(queuedCommandDataServiceHarness.getAllQueuedCommands)
            .toHaveBeenCalledWith(jasmine.objectContaining({
                params: jasmine.objectContaining({
                    majorPriority: QueuedCommand.MajorPriority.CORPORATE_ACTION
                })
            }));
    })
});
