<div class="flex-center flex-column vh-100 vw-100 animated-background">
    <mat-icon class="prepare-to-shutdown-icon">settings_power</mat-icon>
    <h5>{{translateKeys.prepareToShutdown | translate}}</h5>
    <h6>{{translateKeys.prepareToShutdownDesc | translate}}</h6>

    <div class="mt-5">
        <button
            (click)="saveSystemData()"
            [disabled]="isSaveBtnDisabled"
            mat-flat-button>
            <mat-icon *ngIf="!isSavingSystemData">save</mat-icon>
            <mat-spinner
                class="d-inline-block"
                *ngIf="isSavingSystemData"
                [diameter]="24"></mat-spinner>

            {{translateKeys.saveAll | translate}}
        </button>
        &nbsp;&nbsp;
        <button
            mat-flat-button
            (mousedown)="shutdown()"
            (mouseup)="cancelShutdown()"
            (mouseleave)="cancelShutdown()"
            [disabled]="isShutdownDisabled">
            <mat-icon>power_settings_new</mat-icon>
            {{translateKeys.shutdown | translate}}
            <strong *ngIf="isShuttingDown">{{countDown}}</strong>
        </button>
    </div>

    <div class="log-block"
         *ngIf="adminCommand">
        <div>CommandId: {{adminCommand.commandId}}</div>
        <div>Status: {{adminCommand.status}}</div>
        <div>Timestamp: {{adminCommand.timestamp}}</div>
        <div>CommandType: {{adminCommand.type}}</div>
    </div>
    <alert *ngIf="!isWorkingDay" class="mt-5 w-25 text-center">
        <div class="text-info">{{translateKeys.isNotWorkingDay | translate}}</div>
        <div class="mt-1">
            <span class="text-info">{{translateKeys.description | translate}}</span>
            <span class="text-info">{{': '}}{{offDayDescription}}</span>
        </div>
    </alert>
</div>

<div class="fixed-top m-4">
    <button
        mat-flat-button
        color="accent"
        class="menu__title d-flex align-items-center cursor-pointer"
        (click)="openSystemStateChangeWarning()">
        <span class="menu__name px-3">{{translateKeys.back | translate}}</span>
    </button>
</div>
