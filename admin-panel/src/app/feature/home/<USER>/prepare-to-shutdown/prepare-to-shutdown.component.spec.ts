import {fakeAsync, tick} from '@angular/core/testing';
import {PrepareToShutdownComponent} from './prepare-to-shutdown.component';
import {NO_ERRORS_SCHEMA} from '@angular/core';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {SharedModule} from '../../../../shared/shared.module';
import {SaveSystemDateService} from '@dataServices/save-system-date.service';
import {saveSystemDateServiceHarness} from '@dataServices/save-system-date.service.harness';
import {of, Subject, throwError} from 'rxjs';
import {StoreService} from '@services/store.service';
import {ComponentHarness} from '@test/harness/component-harness';
import {CalendarDataService} from '@dataServices/calendar-data.service';
import {calendarDataServiceHarness} from '@dataServices/calendar-data.service.harness';
import {BsModalService, ModalModule} from 'ngx-bootstrap/modal';
import {SystemDataService} from '@dataServices/system-data.service';
import {systemDataServiceHarness} from '@dataServices/system-data.service.harness';

describe('PrepareToShutdownComponent (unit test)', () => {
    let ha: ComponentHarness<PrepareToShutdownComponent>;

    beforeEach(async () => {
        ha = new ComponentHarness(PrepareToShutdownComponent, {
            declarations: [PrepareToShutdownComponent],
            imports: [
                TranslateTestingModule,
                ModalModule.forRoot(),
                SharedModule,
            ],
            providers: [
                BsModalService,
                {provide: SaveSystemDateService, useValue: saveSystemDateServiceHarness},
                {provide: SystemDataService, useValue: systemDataServiceHarness},
                {provide: CalendarDataService, useValue: calendarDataServiceHarness}
            ],
            schemas: [NO_ERRORS_SCHEMA],
            detectChanges: false
        });
    });

    afterEach(() => {
        StoreService.adminCommandCallBacks.splice(0);
    })

    it('should create', () => {
        ha.detectChanges();
        expect(ha.component).toBeTruthy();
    });

    it('should the save btn be enabled and the shutdown btn be disabled when the component is initialized while system data is not saved yet', async () => {
        spyOn(saveSystemDateServiceHarness, 'getSaveResult').and.returnValue(of(null));

        await ha.detectChanges();

        expect(ha.get('button').thatContains('Save')).toHaveAttribute('disabled');
        expect(ha.get('button').thatContains('Shutdown')).not.toHaveAttribute('disabled');
    });

    it('should the save btn be disabled and the shutdown btn be enabled when the component is initialized while system data is saved', async () => {
        spyOn(saveSystemDateServiceHarness, 'getSaveResult').and.returnValue(throwError({}));

        await ha.detectChanges();

        expect(ha.get('button').thatContains('Save')).not.toHaveAttribute('disabled');
        expect(ha.get('button').thatContains('Shutdown')).toHaveAttribute('disabled');
    });

    it('should call #getSaveResult when component is initialized', () => {
        spyOn(saveSystemDateServiceHarness, 'getSaveResult').and.callFake(() => new Subject());

        ha.detectChanges();

        expect(saveSystemDateServiceHarness.getSaveResult).toHaveBeenCalledTimes(1);
    });

    it('should #shutdown & #cancelShutdown be bounded to the shutdown button', async () => {
        spyOn(saveSystemDateServiceHarness, 'getSaveResult').and.returnValue(of(null));
        spyOn(ha.component, 'shutdown');
        spyOn(ha.component, 'cancelShutdown');

        await ha.detectChanges();

        ha.get('button').thatContains('Shutdown').triggerMouseEvent('mousedown');
        ha.get('button').thatContains('Shutdown').triggerMouseEvent('mouseup');

        expect(ha.component.shutdown).toHaveBeenCalledTimes(1);
        expect(ha.component.cancelShutdown).toHaveBeenCalledTimes(1);
    });

    it('should enable the save button when the #saveSystemData request is failed', async () => {
        spyOn(saveSystemDateServiceHarness, 'save').and.returnValue(throwError(null));
        spyOn(saveSystemDateServiceHarness, 'getSaveResult').and.returnValue(throwError(null));

        await ha.detectChanges();
        ha.get('button').thatContains('Save').click();

        expect(ha.get('button').thatContains('Save')).not.toHaveAttribute('disabled');
    });

    it('should call #shutdown when the shutdown button is pressed for 10 seconds', fakeAsync(() => {
        spyOn(saveSystemDateServiceHarness, 'getSaveResult').and.returnValue(of(null));
        spyOn(systemDataServiceHarness, 'shutdown').and.callFake(() => new Subject());

        ha.detectChanges();
        expect(ha.component.countDown).toEqual(9);

        ha.get('button').thatContains('Shutdown').triggerMouseEvent('mousedown');

        tick(1000);
        expect(ha.component.countDown).toEqual(8);
        expect(ha.get('button').thatContains('Shutdown')).toHaveContained('8');
        tick(1000 * 8);
        expect(ha.component.countDown).toEqual(0);
        expect(ha.get('button').thatContains('Shutdown')).not.toHaveContained('0');
        expect(ha.get('button').thatContains('Shutdown')).toHaveAttribute('disabled');
        expect(systemDataServiceHarness.shutdown).toHaveBeenCalled();
    }));

    it('should call the #cancelShutdown when the mouse leaves the shutdown button', fakeAsync(() => {
        spyOn(saveSystemDateServiceHarness, 'getSaveResult').and.returnValue(of(null));
        ha.detectChanges();

        ha.get('button').thatContains('Shutdown').triggerMouseEvent('mousedown');
        tick(1000);
        expect(ha.component.countDown).toEqual(8);
        expect(ha.component.isShuttingDown).toBeTrue();

        ha.get('button').thatContains('Shutdown').triggerMouseEvent('mouseleave');
        expect(ha.component.countDown).toEqual(9);
        expect(ha.component.isShuttingDown).toBeFalse();
    }));

    it('should call #cancelShutdown when the mouseup event happens over the shutdown button', fakeAsync(() => {
        spyOn(saveSystemDateServiceHarness, 'getSaveResult').and.returnValue(of(null));
        ha.detectChanges();

        ha.get('button').thatContains('Shutdown').triggerMouseEvent('mousedown');
        tick(1000);
        expect(ha.component.countDown).toEqual(8);
        expect(ha.component.isShuttingDown).toBeTrue();

        ha.get('button').thatContains('Shutdown').triggerMouseEvent('mouseup');
        expect(ha.component.countDown).toEqual(9);
        expect(ha.component.isShuttingDown).toBeFalse();
    }));

    it('should call #setIsWorkingDay and not #_setOffDayDescription', () => {
        spyOn(calendarDataServiceHarness, 'isWorkingDay').and.returnValue(of({workingDay: true}));
        spyOn(calendarDataServiceHarness, 'offDay').and.returnValue(of());

        ha.detectChanges();

        expect(calendarDataServiceHarness.isWorkingDay).toHaveBeenCalled();
        expect(calendarDataServiceHarness.offDay).not.toHaveBeenCalled();
    });

    it('should call #setIsWorkingDay and #_setOffDayDescription method', () => {
        spyOn(calendarDataServiceHarness, 'isWorkingDay').and.returnValue(of({workingDay: false}));
        spyOn(calendarDataServiceHarness, 'offDay').and.returnValue(of());

        ha.detectChanges();

        expect(calendarDataServiceHarness.isWorkingDay).toHaveBeenCalled();
        expect(calendarDataServiceHarness.offDay).toHaveBeenCalled();
    });
});
