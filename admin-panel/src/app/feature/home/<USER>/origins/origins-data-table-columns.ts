import {DataTableColumn} from '@models/data-table';
import {TranslateKey} from '@enums/translate-key';
import {Origin} from '@homeModels/origin';
import {TranslatePipe} from '@ngx-translate/core';
import {toCamelCase} from '@core/utils';

export function originsDataTableColumns(): DataTableColumn<Origin>[] {
    return [
        {
            title: TranslateKey.id,
            value(data) { return data.id; }
        },
        {
            title: TranslateKey.type,
            pipeToken: TranslatePipe,
            value(data) { return toCamelCase(data.type); }
        },
        {
            title: TranslateKey.priority,
            value(data) { return data.priority; }
        }
    ];
}
