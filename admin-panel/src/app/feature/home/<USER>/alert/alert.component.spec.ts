import {AlertComponent} from './alert.component';
import {ComponentHarness} from '@test/harness/component-harness';
import {SharedModule} from '../../../../shared/shared.module';
import {TranslateTestingModule} from '../../../../../translate-testing.module';

describe('AlertComponent', () => {
    let ha: ComponentHarness<AlertComponent>;

    beforeEach(async () => {
        ha = new ComponentHarness(AlertComponent, {
            declarations: [
                AlertComponent
            ],
            imports: [
                SharedModule,
                TranslateTestingModule
            ],
            detectChanges: false
        });
    });

    it('should create', () => {
        ha.component.alertMessage = 'Alert test message';
        ha.detectChanges();

        expect(ha.component).toBeTruthy();
    });
});
