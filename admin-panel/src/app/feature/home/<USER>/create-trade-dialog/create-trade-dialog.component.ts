import {ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit, ViewChild} from '@angular/core';
import {Instrument} from '@homeModels/instrument';
import {FormGroup} from '@angular/forms';
import {TradeDataService} from '@dataServices/trade-data.service';
import {InstrumentDialog} from '@models/instrument-dialog';
import {TranslateKey} from '@enums/translate-key';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {FormFieldSets, setupCreateTradeFormFields} from './setup-create-trade-form-fields';
import {ChangeDetectionService} from '@services/change-detection.service';
import {TownDataService} from '@dataServices/town-data.service';
import {Trade} from '@models/trade';
import {FormFieldSet} from '@modules/dynamic-form/form-field-sets';
import {FormFields} from '@modules/dynamic-form/form-field';
import {DynamicFormComponent} from '@modules/dynamic-form/dynamic-form.component';

@Component({
    selector: 'app-create-trade-dialog',
    templateUrl: './create-trade-dialog.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class CreateTradeDialogComponent implements OnInit, InstrumentDialog<Instrument.Single> {
    readonly translateKeys = TranslateKey;

    readonly formGroup = new FormGroup<Trade.CreateFormGroup>({} as any);

    readonly formFieldSets: FormFieldSet[] = [
        {title: TranslateKey.buyer, id: FormFieldSets.BUYER},
        {title: TranslateKey.seller, id: FormFieldSets.SELLER},
    ];

    instrument = new Instrument.Single();

    tradeMessage: {
        type: string,
        message: string
    };

    private _formFields: FormFields<Trade.CreateFormGroup>;
    get formFields(): FormFields { return this._formFields; }

    private _keepModalOpen: boolean;
    get keepModalOpen(): boolean { return this._keepModalOpen; }
    set keepModalOpen(value: boolean) { this._keepModalOpen = value; }

    @ViewChild(DynamicFormComponent)
    private _dynamicForm: DynamicFormComponent;

    constructor(
        public modalRef: BsModalRef,
        private _townDataService: TownDataService,
        private _tradeDataService: TradeDataService,
        private _changeDetectorRef: ChangeDetectorRef
    ) {}

    ngOnInit(): void {
        this._formFields = setupCreateTradeFormFields(this.instrument.securityId);
        this._setTownCodeFieldOptions();
    }

    onSubmit(): void {
        this._createTrade();
        this._changeDetectorRef.detectChanges();
    }

    private _createTrade(): void {
        const body = Object.create(this.formGroup);
        const withoutImpactValue = this.formGroup.controls.hasImpactOnMarketStatistics.value;
        body.controls.hasImpactOnMarketStatistics.setValue(!withoutImpactValue);
        this._tradeDataService
            .createTrade(this.instrument.securityId, body.getRawValue())
            .subscribe(() => {
                if (!this._keepModalOpen) {
                    this.modalRef.hide();
                }

                this.tradeMessage = {
                    type: 'info',
                    message: this.translateKeys.createTradeSent
                };
                this._dynamicForm?.reset();
                this._changeDetectorRef.detectChanges();
            });
    }

    private _setTownCodeFieldOptions(): void {
        this._townDataService
            .getTowns()
            .subscribe(resp => {
                this._formFields.rawValue.buyer.clearingData.brokerBusinessIdentificationCode.townCode.options = resp.content;
                this._formFields.rawValue.seller.clearingData.brokerBusinessIdentificationCode.townCode.options = resp.content;
                ChangeDetectionService.onChange.next({type: 'FORM'});
            })
    }
}
