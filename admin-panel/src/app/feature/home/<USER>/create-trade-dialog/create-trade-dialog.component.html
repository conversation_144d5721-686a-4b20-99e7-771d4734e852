<app-modal-header>{{instrument.mnemonic | selectByLanguage}}</app-modal-header>

<!-- Body -->
<div class="modal-body">
    <app-dynamic-form
        [formGroup]="formGroup"
        [formFields]="formFields"
        [formFieldSets]="formFieldSets">
    </app-dynamic-form>

    <alert
        class="mt-3 d-block"
        *ngIf="tradeMessage"
        [type]="tradeMessage.type">
        {{tradeMessage.message | translate}}
    </alert>
</div>

<div class="modal-footer">
    <!-- Create another -->
    <label>
        <input type="checkbox" [(ngModel)]="keepModalOpen">
        {{translateKeys.keepDialogOpened | translate}}
    </label>
    <!-- Submit button -->
    <button
        type="submit"
        mat-flat-button
        color="primary"
        class="w-auto"
        [disabled]="formGroup.invalid"
        (click)="onSubmit()">
        {{translateKeys.createTrade | translate}}
    </button>
    <!-- Cancel button -->
    <button
        type="button"
        mat-button
        (click)="modalRef.hide()">
        {{translateKeys.cancel | translate}}
    </button>
</div>
