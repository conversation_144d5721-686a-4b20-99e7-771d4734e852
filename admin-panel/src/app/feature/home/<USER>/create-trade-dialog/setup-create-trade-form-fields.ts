import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {Validators} from '@angular/forms';
import {StoreService} from '@shared/services/store.service';
import {InputMasks} from '@constants/input-masks';
import {CustomValidators} from '@constants/custom-validators';
import {Town} from '@models/town';
import {Trader} from '@models/trader';
import {FieldsPlaceholder} from '../../../../shared/constants/fields-placeholder';
import {ReadOnlyFormField} from '@modules/dynamic-form/form-fields/read-only-field/read-only-form-field';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';
import {AutoCompleteFormField} from '@modules/dynamic-form/form-fields/auto-complete-field/auto-complete-form-field';
import {CheckboxFormField} from '@modules/dynamic-form/form-fields/checkbox-field/checkbox-form-field';

export enum FormFieldSets {
    SELLER = 'seller',
    BUYER = 'buyer'
}

export function setupCreateTradeFormFields(securityId: string) {
    return [
        new ReadOnlyFormField({
            formControlName: 'securityId',
            value: securityId
        }),

        new InputFormField({
            formControlName: 'quantity',
            label: TranslateKey.quantity,
            width: 4,
            validations: [Validators.required, Validators.min(1), Validators.maxLength(12),
                CustomValidators.greaterThan(0)],
            mask: InputMasks.COMMA_SEPERATED_NUMBER,
            placeholder: FieldsPlaceholder.DECIMAL_NUMBER
        }),

        new InputFormField({
            formControlName: 'price',
            label: TranslateKey.price,
            width: 4,
            validations: [Validators.required, Validators.maxLength(14), Validators.pattern(InputMasks.ORDER_PRICE.pattern)],
            mask: InputMasks.FLOATING_NUMBER,
            placeholder: FieldsPlaceholder.DECIMAL_NUMBER
        }),


        new CheckboxFormField({
            formControlName: 'hasImpactOnMarketStatistics',
            label: TranslateKey.withoutImpact,
            width: 4,
            style: {marginTop: '25px'},
            value: false
        }),

        new ReadOnlyFormField({
            formControlName: 'entryDate',
            value: StoreService.systemDateTime
        }),

        {
            formGroupName: 'buyer',
            formFields: [
                new InputFormField({
                    formControlName: 'shareholderId',
                    label: TranslateKey.shareholder,
                    width: 3,
                    validations: [Validators.required, Validators.maxLength(16)],
                    mask: InputMasks.SHAREHOLDER,
                    fieldSetId: FormFieldSets.BUYER,
                    placeholder: FieldsPlaceholder.SHAREHOLDER_ID
                }),

                new AutoCompleteFormField({
                    formControlName: 'brokerId',
                    label: TranslateKey.broker,
                    width: 6,
                    validations: [Validators.required, CustomValidators.fixedLength(3)],
                    searchExpField: 'searchExpression',
                    options: StoreService.brokers,
                    getTitle(option): string { return option ? option.id + ' - ' + option.name.fa : ''; },
                    optionFieldName: 'id',
                    fieldSetId: FormFieldSets.BUYER,
                    placeholder: FieldsPlaceholder.BROKER
                }),

                {
                    formGroupName: 'clearingData',
                    formFields: [
                        new AutoCompleteFormField<Trader>({
                            formControlName: 'traderId',
                            label: TranslateKey.trader,
                            width: 3,
                            validations: [Validators.required],
                            fieldSetId: FormFieldSets.BUYER,
                            placeholder: FieldsPlaceholder.TRADER_ID,
                            searchExpField: 'traderId',
                            options: StoreService.traders,
                            getTitle(option): string { return option ? option.traderId : ''; },
                            optionFieldName: 'traderId'
                        }),

                        new InputFormField({
                            formControlName: 'traderOrderNumber',
                            label: TranslateKey.traderOrderNumber,
                            width: 3,
                            validations: [Validators.maxLength(8)],
                            fieldSetId: FormFieldSets.BUYER,
                            placeholder: FieldsPlaceholder.TRADER_ORDER_NUMBER
                        }),

                        new ReadOnlyFormField({
                            formControlName: 'brokerOrderEntryDateTime',
                            value: StoreService.systemDateTime,
                            fieldSetId: FormFieldSets.BUYER
                        }),

                        new AutoCompleteFormField({
                            formControlName: 'giveUpBrokerId',
                            label: TranslateKey.giveUpBroker,
                            width: 6,
                            searchExpField: 'searchExpression',
                            options: StoreService.brokers,
                            getTitle(option): string { return option ? option.id + ' - ' + option.name.fa : ''; },
                            optionFieldName: 'id',
                            fieldSetId: FormFieldSets.BUYER,
                            placeholder: FieldsPlaceholder.BROKER
                        }),

                        new InputFormField({
                            formControlName: 'freeText',
                            label: TranslateKey.freeText,
                            width: 3,
                            validations: [Validators.maxLength(18)],
                            fieldSetId: FormFieldSets.BUYER,
                            placeholder: FieldsPlaceholder.FREE_TEXT
                        }),

                        {
                            formGroupName: 'brokerBusinessIdentificationCode',
                            formFields: [

                                new InputFormField({
                                    formControlName: 'bankCode',
                                    label: TranslateKey.bankCode,
                                    width: 3,
                                    validations: [Validators.maxLength(3)],
                                    mask: InputMasks.BANK_CODE,
                                    fieldSetId: FormFieldSets.BUYER,
                                    placeholder: FieldsPlaceholder.BANK_CODE
                                }),

                                new AutoCompleteFormField<Town>({
                                    formControlName: 'townCode',
                                    label: TranslateKey.townCode,
                                    width: 3,
                                    fieldSetId: FormFieldSets.BUYER,
                                    placeholder: FieldsPlaceholder.TOWN_CODE,
                                    searchExpField: 'searchExpression',
                                    options: [],
                                    getTitle(option): string { return option ? `${option.code} - ${option.name.en} - ${option.name.fa}` : ''; },
                                    optionFieldName: 'code',
                                    panelWidth: 'auto'
                                }),

                                new InputFormField({
                                    formControlName: 'branchCode',
                                    label: TranslateKey.branchCode,
                                    width: 3,
                                    validations: [Validators.maxLength(3)],
                                    mask: InputMasks.BRANCH_CODE,
                                    fieldSetId: FormFieldSets.BUYER,
                                    placeholder: FieldsPlaceholder.BRANCH_CODE
                                }),

                                new ReadOnlyFormField({
                                    formControlName: 'countryCode',
                                    value: 'IR',
                                    fieldSetId: FormFieldSets.BUYER
                                })
                            ]
                        }
                    ]
                }
            ]
        },

        {
            formGroupName: 'seller',
            formFields: [
                new InputFormField({
                    formControlName: 'shareholderId',
                    label: TranslateKey.shareholder,
                    width: 3,
                    validations: [Validators.required, Validators.maxLength(16)],
                    mask: InputMasks.SHAREHOLDER,
                    fieldSetId: FormFieldSets.SELLER,
                    placeholder: FieldsPlaceholder.SHAREHOLDER_ID
                }),

                new AutoCompleteFormField({
                    formControlName: 'brokerId',
                    label: TranslateKey.broker,
                    width: 6,
                    validations: [Validators.required, CustomValidators.fixedLength(3)],
                    searchExpField: 'searchExpression',
                    options: StoreService.brokers,
                    getTitle(option): string { return option ? option.id + ' - ' + option.name.fa : ''; },
                    optionFieldName: 'id',
                    fieldSetId: FormFieldSets.SELLER,
                    placeholder: FieldsPlaceholder.BROKER
                }),


                {
                    formGroupName: 'clearingData',
                    formFields: [
                        new AutoCompleteFormField<Trader>({
                            formControlName: 'traderId',
                            label: TranslateKey.trader,
                            width: 3,
                            validations: [Validators.required],
                            fieldSetId: FormFieldSets.SELLER,
                            placeholder: FieldsPlaceholder.TRADER_ID,
                            searchExpField: 'traderId',
                            options: StoreService.traders,
                            getTitle(option): string { return option ? option.traderId : ''; },
                            optionFieldName: 'traderId'
                        }),

                        new InputFormField({
                            formControlName: 'traderOrderNumber',
                            label: TranslateKey.traderOrderNumber,
                            width: 3,
                            validations: [Validators.maxLength(8)],
                            fieldSetId: FormFieldSets.SELLER,
                            placeholder: FieldsPlaceholder.TRADER_ORDER_NUMBER
                        }),

                        new ReadOnlyFormField({
                            formControlName: 'brokerOrderEntryDateTime',
                            value: StoreService.systemDateTime,
                            fieldSetId: FormFieldSets.SELLER
                        }),

                        new AutoCompleteFormField({
                            formControlName: 'giveUpBrokerId',
                            label: TranslateKey.giveUpBroker,
                            width: 6,
                            searchExpField: 'searchExpression',
                            options: StoreService.brokers,
                            getTitle(option): string { return option ? option.id + ' - ' + option.name.fa : ''; },
                            optionFieldName: 'id',
                            fieldSetId: FormFieldSets.SELLER,
                            placeholder: FieldsPlaceholder.BROKER
                        }),

                        new InputFormField({
                            formControlName: 'freeText',
                            label: TranslateKey.freeText,
                            width: 3,
                            validations: [Validators.maxLength(18)],
                            fieldSetId: FormFieldSets.SELLER,
                            placeholder: FieldsPlaceholder.FREE_TEXT

                        }),

                        {
                            formGroupName: 'brokerBusinessIdentificationCode',
                            formFields: [

                                new InputFormField({
                                    formControlName: 'bankCode',
                                    label: TranslateKey.bankCode,
                                    width: 3,
                                    validations: [Validators.maxLength(3)],
                                    mask: InputMasks.BANK_CODE,
                                    fieldSetId: FormFieldSets.SELLER,
                                    placeholder: FieldsPlaceholder.BANK_CODE
                                }),

                                new AutoCompleteFormField<Town>({
                                    formControlName: 'townCode',
                                    label: TranslateKey.townCode,
                                    width: 3,
                                    fieldSetId: FormFieldSets.SELLER,
                                    placeholder: FieldsPlaceholder.TOWN_CODE,
                                    searchExpField: 'searchExpression',
                                    options: [],
                                    getTitle(option): string { return option ? `${option.code} - ${option.name.en} - ${option.name.fa}` : ''; },
                                    optionFieldName: 'code',
                                    panelWidth: 'auto'
                                }),

                                new InputFormField({
                                    formControlName: 'branchCode',
                                    label: TranslateKey.branchCode,
                                    width: 3,
                                    validations: [Validators.maxLength(3)],
                                    mask: InputMasks.BRANCH_CODE,
                                    fieldSetId: FormFieldSets.SELLER,
                                    placeholder: FieldsPlaceholder.BRANCH_CODE
                                }),

                                new ReadOnlyFormField({
                                    formControlName: 'countryCode',
                                    value: 'IR',
                                    fieldSetId: FormFieldSets.SELLER
                                })
                            ]
                        }
                    ]
                }
            ]
        }

    ];
}
