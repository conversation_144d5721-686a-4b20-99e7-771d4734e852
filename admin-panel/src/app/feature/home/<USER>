import {AfterViewInit, ChangeDetectorRef, Component, OnDestroy, OnInit} from '@angular/core';
import {KeyHandlerService} from '@services/key-handler.service';
import {filter, map, takeUntil} from 'rxjs/operators';
import {NavigationEnd, Router} from '@angular/router';
import {CTRL_LEFT, KEY_F, KEY_H} from 'mb-keycode';
import {TranslateKey} from '@shared/enums/translate-key';
import {BsModalService} from 'ngx-bootstrap/modal';
import {StoreService} from '@shared/services/store.service';
import {RoutingLayout} from '@constants/routing-layout';
import {Subject} from 'rxjs';
import {SystemState} from '@models/system-state';
import {StartMarketDialogComponent} from './dialogs/start-market-dialog/start-market-dialog.component';
import {InitializationDataService} from '@dataServices/initialization-data.service';
import {Sidebar} from '@layouts/main-layout/sidebar/sidebar';
import {HomeSidebarService} from './shared/services/home-sidebar.service';
import {SystemStateDataService} from '@dataServices/system-state-data.service';

@Component({
    selector: 'app-home',
    templateUrl: './home.page.html',
    styleUrls: ['./home.page.scss'],
    providers: [HomeSidebarService],
    standalone: false
})
export class HomePage implements OnInit, OnDestroy, AfterViewInit {
    isSearchBoxOpen: boolean;

    readonly sidebar: Sidebar;

    private _onDestroy = new Subject();

    private _currentSystemState: SystemState.State;

    get isHomePage(): boolean {
        return window.location.pathname === `/${RoutingLayout.HOME}`;
    }

    get isSystemStatePrepareToShutdown(): boolean {
        return this._currentSystemState === SystemState.State.PREPARE_TO_SHUTDOWN;
    }

    constructor(
        private _router: Router,
        private _modalService: BsModalService,
        private _changeDetectorRef: ChangeDetectorRef,
        private _keyHandlerService: KeyHandlerService,
        private _systemStateDataService: SystemStateDataService,
        private _initializationDataService: InitializationDataService,
        private _homeSidebarService: HomeSidebarService
    ) {
        this.sidebar = _homeSidebarService.getSidebar();
    }

    ngOnInit(): void {
        this._fetchSystemState();
        this._setupKeyboardShortcuts();
        this._closeSearchBoxOnRouteChange();
        this._subscribeSystemStateChanges();
        this._subscribeIsMarketStartedChanges();
        this._fetchMarketInitialization();
    }

    ngOnDestroy(): void {
        this._onDestroy.next(null);
        this._onDestroy.complete();
    }

    ngAfterViewInit(): void {
        this._setSidenavBackgroundColor();
        this._subscribeToRouteChangesForSidenavStyle();
    }

    closeSearchBox(event): void {
        if (event.target.classList.thatContains('search-box-popup')) {
            this.isSearchBoxOpen = false;
        }
    }

    onCloseSearchBox(): void {
        this.isSearchBoxOpen = false;
    }

    // MARK: - Private Methods - Keyboard Shortcuts

    private _setupKeyboardShortcuts(): void {
        this._setupSearchBoxShortcut();
        this._setupHomePageShortcut();
    }

    private _setupSearchBoxShortcut(): void {
        this._registerKeyboardShortcut('openSearchBox1', [CTRL_LEFT, KEY_F])
            .subscribe(({event}) => {
                event.preventDefault();
                this.isSearchBoxOpen = true;
                this._focusOnSearchBox();
            });
    }

    private _setupHomePageShortcut(): void {
        this._registerKeyboardShortcut('goHomePage', [CTRL_LEFT, KEY_H])
            .subscribe(({event}) => {
                event.preventDefault();
                if (!this.isHomePage) {
                    this._router.navigate([`/${RoutingLayout.HOME}`]);
                }
            });
    }

    private _registerKeyboardShortcut(id: string, keymap: string[]) {
        return this._keyHandlerService
            .addItem(id, keymap)
            .pipe(takeUntil(this._onDestroy));
    }

    private _focusOnSearchBox(): void {
        setTimeout(() => document.querySelector<HTMLInputElement>('app-search-box input').select());
    }

    // MARK: - Private Methods - Route Change Handling

    private _closeSearchBoxOnRouteChange(): void {
        this._router.events
            .pipe(
                takeUntil(this._onDestroy),
                filter(resp => resp instanceof NavigationEnd)
            )
            .subscribe(() => this.isSearchBoxOpen = false);
    }

    private _subscribeToRouteChangesForSidenavStyle(): void {
        this._router.events
            .pipe(
                filter(event => event instanceof NavigationEnd),
                map((event: NavigationEnd) => event.url === `/${RoutingLayout.HOME}`)
            )
            .subscribe(this._setSidenavBackgroundColor.bind(this));
    }

    // MARK: - Private Methods - System State Handling

    private _subscribeSystemStateChanges(): void {
        StoreService.systemState.subscribe(systemState => {
            this._updateSystemStateIcon(systemState);
            this._currentSystemState = systemState;
            this._handlePrepareToShutdownState();
            this._homeSidebarService.updateAllSubmenuStatuses(systemState);
        });
    }

    private _updateSystemStateIcon(systemState: SystemState.State): void {
        this.sidebar.footerItems[0].icon = this._homeSidebarService.getSystemStateIcon(systemState);
    }

    private _handlePrepareToShutdownState(): void {
        if (this.isSystemStatePrepareToShutdown) {
            window.history.replaceState('', '', '/home?prepareToShutdown');
        }
    }

    // MARK: - Private Methods - Sidenav Styling

    private _setSidenavBackgroundColor(): void {
        this._getSidenavElm()
            .then(sidenavElm => {
                const isHomePage = location.pathname === `/${RoutingLayout.HOME}`;
                sidenavElm.style.backgroundColor = isHomePage ? '' : 'rgba(0,0,0,0.04)';
                sidenavElm.style.borderColor = isHomePage ? 'white' : '#dee2e6';
            });
    }

    private _getSidenavElm(): Promise<HTMLDivElement> {
        return new Promise<HTMLDivElement>(resolve => {
            const timeInterval = setInterval(() => {
                const sidenavElm = document.querySelector<HTMLDivElement>('.sidenav');
                if (sidenavElm) {
                    resolve(sidenavElm);
                    clearInterval(timeInterval);
                }
            }, 50);
        });
    }

    // MARK: - Private Methods - Market Initialization

    private _fetchMarketInitialization(): void {
        this._initializationDataService.ShouldInitializeMarket().subscribe(resp => {
            StoreService.isMarketStarted = !resp;
            this._setStartMarketButtonDisabled();
            if (!StoreService.isMarketStarted && !this.isSystemStatePrepareToShutdown) {
                this._openStartMarketDialog();
            }
        });
    }

    private _openStartMarketDialog(): void {
        this._modalService.show(StartMarketDialogComponent, {
            class: 'modal-dialog-centered',
            ignoreBackdropClick: true
        });
    }

    private _subscribeIsMarketStartedChanges(): void {
        StoreService.marketStartChange.subscribe(resp => {
            StoreService.isMarketStarted = resp;
            this._setStartMarketButtonDisabled();
        });
    }

    private _setStartMarketButtonDisabled(): void {
        const startMarketButton = this.sidebar.footerItems.find(item => item.title === TranslateKey.startMarket);
        if (startMarketButton) {
            startMarketButton.disabled = StoreService.isMarketStarted;
            if (StoreService.isMarketStarted) {
                startMarketButton.class = '';
            }
        }
        this._changeDetectorRef.detectChanges();
    }

    private _fetchSystemState(): void {
        this._systemStateDataService
            .getCurrentState()
            .subscribe(resp => StoreService.systemState.next(resp.state));

        StoreService.systemState.subscribe(resp =>
            StoreService.currentSystemState = resp);
    }
}
