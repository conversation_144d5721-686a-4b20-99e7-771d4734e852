import {NgModule} from '@angular/core';
import {HomeRoutingModule} from './home-routing.module';
import {HomePage} from './home.page';
import {SearchBoxComponent} from './components/search-box/search-box.component';
import {InsertOrderComponent} from './dialogs/insert-order/insert-order.component';

import {
    ChangeInstrumentStateComponent
} from './components/change-state/change-instrument-state/change-instrument-state.component';
import {UploadOrderFileDialog} from './dialogs/upload-order-file/upload-order-file.dialog';
import {
    InstrumentInfoSummaryComponent
} from './components/search-box/instrument-search-details/instrument-detail-section/instrument-info-summary/instrument-info-summary.component';
import {
    InstrumentDetailSectionComponent
} from './components/search-box/instrument-search-details/instrument-detail-section/instrument-detail-section.component';
import {GroupDetailDialogComponent} from './dialogs/group-detail-dialog/group-detail-dialog.component';
import {GetM1FileDialogComponent} from './dialogs/get-m1-file-dialog/get-m1-file-dialog.component';
import {CreateTradeDialogComponent} from './dialogs/create-trade-dialog/create-trade-dialog.component';
import {RequiredErrComponent} from './components/required-err/required-err.component';
import {UploadCsdFileDialog} from './dialogs/upload-csd-file/upload-csd-file.dialog';
import {UploadFileComponent} from './components/upload-file/upload-file.component';
import {ShareholderBlockDialogComponent} from './dialogs/shareholder-block-dialog/shareholder-block-dialog.component';
import {TransferShareDialogComponent} from './dialogs/transfer-share-dialog/transfer-share-dialog.component';
import {ChangePasswordDialogComponent} from './dialogs/change-password-dialog/change-password-dialog.component';
import {CorporateActionDialogComponent} from './dialogs/corporate-action-dialog/corporate-action-dialog.component';
import {UpdateInstrumentDialogComponent} from './dialogs/update-instrument-dialog/update-instrument-dialog.component';
import {PageTemplateModule} from '@modules/page-template/page-template.module';
import {MultiPickModule} from '@modules/multi-pick/multi-pick.module';
import {SharedDeclarations} from '@modules/shared-declarations/shared-declarations';
import {SharedModule} from '../../shared/shared.module';
import {SystemStateDialogComponent} from './dialogs/system-state-dialog/system-state-dialog.component';
import {
    InstrumentSearchDetailsComponent
} from './components/search-box/instrument-search-details/instrument-search-details.component';
import {
    InstrumentSearchResultsComponent
} from './components/search-box/instrument-search-results/instrument-search-results.component';
import {GroupSearchResultsComponent} from './components/search-box/group-search-results/group-search-results.component';
import {
    BrokerSearchResultsComponent
} from './components/search-box/broker-search-results/broker-search-results.component';
import {
    InvestorSearchResultsComponent
} from './components/search-box/investor-search-results/investor-search-results.component';
import {
    ShareholderSearchResultsComponent
} from './components/search-box/shareholder-search-results/shareholder-search-results.component';
import {
    ShareholderSearchDetailsComponent
} from './components/search-box/shareholder-search-details/shareholder-search-details.component';
import {
    InvestorSearchDetailsComponent
} from './components/search-box/investor-search-details/investor-search-details.component';
import {GroupSearchDetailsComponent} from './components/search-box/group-search-details/group-search-details.component';
import {
    BrokerSearchDetailsComponent
} from './components/search-box/broker-search-details/broker-search-details.component';
import {AllSearchResultsComponent} from './components/search-box/all-search-results/all-search-results.component';
import {AllSearchDetailsComponent} from './components/search-box/all-search-details/all-search-details.component';
import {SearchResultsComponent} from './components/search-box/search-results/search-results.component';
import {
    ChangePasswordSuccessfullyDialogComponent
} from './dialogs/change-password-successfully-dialog/change-password-successfully-dialog.component';
import {GetAramisFilesDialogComponent} from './dialogs/get-aramis-files-dialog/get-aramis-files-dialog.component';

import {WeekendDialogComponent} from './dialogs/weekend-dialog/weekend-dialog.component';
import {HolidayDialogComponent} from './dialogs/holiday-dialog/holiday-dialog.component';
import {InformationTableComponent} from './components/information-table/information-table.component';
import {NewProductDialogComponent} from './dialogs/new-product-dialog/new-product-dialog.component';
import {TableGroupDialogComponent} from './dialogs/table-group-dialog/table-group-dialog.component';
import {
    PrepareToShutDownWarningComponent
} from './components/prepare-to-shut-down-warning/prepare-to-shut-down-warning.component';
import {NotifyMarketDialog} from './dialogs/notify-market-dialog/notify-market.dialog';
import {
    UpdateImmediateInstrumentDialogComponent
} from './dialogs/update-immediate-instrument-dialog/update-immediate-instrument-dialog.component';
import {
    TraderSearchResultsComponent
} from './components/search-box/trader-search-results/trader-search-results.component';
import {
    TraderSearchDetailsComponent
} from './components/search-box/trader-search-details/trader-search-details.component';
import {AddTraderDialogComponent} from './dialogs/add-trader-dialog/add-trader-dialog.component';
import {NewCompanyDialogComponent} from './dialogs/new-company-dialog/new-company-dialog.component';
import {PurgeOrderDialogComponent} from './dialogs/purge-order-dialog/purge-order-dialog.component';
import {CreateInstrumentDialogComponent} from './dialogs/create-instrument-dialog/create-instrument-dialog.component';
import {
    CommandQueueDetailDialogComponent
} from './dialogs/command-queue-detail-dialog/command-queue-detail-dialog.component';
import {UpdateProductDialogComponent} from './dialogs/update-product-dialog/update-product-dialog.component';
import {UpdateCompanyDialogComponent} from './dialogs/update-company-dialog/update-company-dialog.component';
import {DataTableModule} from '@modules/data-table/data-table.module';
import {
    UpdateGroupStaticThresholdDialogComponent
} from './dialogs/update-group-static-threshold-dialog/update-group-static-threshold-dialog.component';
import {ChangeGroupStateComponent} from './components/change-state/change-group-state/change-group-state.component';
import {PageLayoutModule} from '@layouts/main-layout/page-layout.module';
import {AlertComponent} from './components/alert/alert.component';
import {DynamicFormDialog} from './dialogs/dynamic-form-dialog/dynamic-form.dialog';
import {
    CreateBatchSessionScheduleComponent
} from './dialogs/create-batch-session-schedule/create-batch-session-schedule.component';
import {UploadFileDialog} from './dialogs/upload-file/upload-file.dialog';
import {DynamicFormModule} from '@modules/dynamic-form/dynamic-form.module';
import {MarketDataWidgetComponent} from './components/market-data-widget/market-data-widget.component';
import {ErrorMessageDialogComponent} from '@core/error-message-dialog/error-message-dialog.component';
import {
    CreateGroupScheduleTemplateDialogComponent
} from './dialogs/create-group-schedule-template-dialog/create-group-schedule-template-dialog.component';
import {
    CreateSystemScheduleTemplateDialogComponent
} from './dialogs/create-system-schedule-template-dialog/create-system-schedule-template-dialog.component';
import {
    UpdateSystemSessionScheduleComponent
} from './dialogs/update-system-session-schedule/update-system-session-schedule.component';
import {PrepareToShutdownComponent} from './components/prepare-to-shutdown/prepare-to-shutdown.component';
import {ScheduleActionsComponent} from './components/schedule-actions/schedule-actions.component';
import {StartMarketDialogComponent} from './dialogs/start-market-dialog/start-market-dialog.component';
import {
    EventReportDetailDialogComponent
} from './dialogs/event-report-detail-dialog/event-report-detail-dialog.component';

@NgModule({
    declarations: [
        HomePage,
        SearchBoxComponent,
        InsertOrderComponent,
        UploadOrderFileDialog,
        InstrumentInfoSummaryComponent,
        InstrumentDetailSectionComponent,
        GroupDetailDialogComponent,
        GetM1FileDialogComponent,
        CreateTradeDialogComponent,
        RequiredErrComponent,
        UploadCsdFileDialog,
        UploadFileComponent,
        ShareholderBlockDialogComponent,
        TransferShareDialogComponent,
        ErrorMessageDialogComponent,
        ChangePasswordDialogComponent,
        CorporateActionDialogComponent,
        UpdateInstrumentDialogComponent,
        SystemStateDialogComponent,
        InstrumentSearchDetailsComponent,
        InstrumentSearchResultsComponent,
        GroupSearchResultsComponent,
        BrokerSearchResultsComponent,
        InvestorSearchResultsComponent,
        ShareholderSearchResultsComponent,
        ShareholderSearchDetailsComponent,
        InvestorSearchDetailsComponent,
        GroupSearchDetailsComponent,
        BrokerSearchDetailsComponent,
        AllSearchResultsComponent,
        AllSearchDetailsComponent,
        SearchResultsComponent,
        ChangePasswordSuccessfullyDialogComponent,
        GetAramisFilesDialogComponent,
        PrepareToShutdownComponent,
        WeekendDialogComponent,
        HolidayDialogComponent,
        InformationTableComponent,
        NewProductDialogComponent,
        TableGroupDialogComponent,
        PrepareToShutDownWarningComponent,
        StartMarketDialogComponent,
        NotifyMarketDialog,
        UpdateImmediateInstrumentDialogComponent,
        TraderSearchDetailsComponent,
        TraderSearchResultsComponent,
        AddTraderDialogComponent,
        NewCompanyDialogComponent,
        PurgeOrderDialogComponent,
        CreateInstrumentDialogComponent,
        CommandQueueDetailDialogComponent,
        UpdateProductDialogComponent,
        UpdateCompanyDialogComponent,
        UpdateGroupStaticThresholdDialogComponent,
        ChangeGroupStateComponent,
        AlertComponent,
        DynamicFormDialog,
        CreateBatchSessionScheduleComponent,
        UploadFileDialog,
        CreateGroupScheduleTemplateDialogComponent,
        CreateSystemScheduleTemplateDialogComponent,
        UpdateSystemSessionScheduleComponent,
        EventReportDetailDialogComponent
    ],
    imports: [
        ChangeInstrumentStateComponent,
        DataTableModule,
        DynamicFormModule,
        HomeRoutingModule,
        MultiPickModule,
        PageLayoutModule,
        PageTemplateModule,
        SharedDeclarations,
        SharedModule,
        MarketDataWidgetComponent,
        ScheduleActionsComponent
    ],
    exports: [
        InsertOrderComponent
    ]
})
export class HomeModule {}
