.app-container {
    width: 100%;
    height: 100%;
    flex-basis: 0;
    flex-grow: 1;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}
.app-sidenav {
    width: 55px;
    height: 100%;
    position: sticky;
    top: 0;
    z-index: 1000;
}
.app-content {
    width: 100%;
    height: 100%;
}

.search-box-popup {
    z-index: 1000;
    top: 0;
    left: 0;
}

::ng-deep {
    @keyframes blinking {
        0%   {}
        50%   {box-shadow: 0 0 20px #e91e63;}
        100%   {}
    }

    app-sidebar .blink-animation {
        animation-name: blinking;
        animation-duration: 1000ms;
        animation-iteration-count: infinite;
    }
}
