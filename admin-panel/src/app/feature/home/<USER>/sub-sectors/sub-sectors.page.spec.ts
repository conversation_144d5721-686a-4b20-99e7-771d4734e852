import {SubSectorsPage} from './sub-sectors.page';
import {ComponentHarness} from '@test/harness/component-harness';
import {SectorsPage} from '../sectors/sectors.page';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {RouterTestingModule} from '@angular/router/testing';
import {BsModalService} from 'ngx-bootstrap/modal';
import {DynamicFormDialogService} from '../../dialogs/dynamic-form-dialog/dynamic-form-dialog.service';
import {DynamicFormDialogServiceHarness} from '../../dialogs/dynamic-form-dialog/dynamic-form-dialog.service.harness';
import {SectorDataService} from '@dataServices/sector-data.service';
import {sectorDataServiceHarness} from '@dataServices/sector-data.service.harness';

describe('SubSectorsPage', () => {
    let ha: ComponentHarness<SubSectorsPage>;

    beforeEach(() => {
        ha = new ComponentHarness(SubSectorsPage, {
            imports: [
                SectorsPage,
                TranslateTestingModule,
                RouterTestingModule
            ],
            providers: [
                BsModalService,
                {provide: SectorDataService, useValue: sectorDataServiceHarness},
                {provide: DynamicFormDialogService, useValue: DynamicFormDialogServiceHarness }
            ]
        });
    });

    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });
});
