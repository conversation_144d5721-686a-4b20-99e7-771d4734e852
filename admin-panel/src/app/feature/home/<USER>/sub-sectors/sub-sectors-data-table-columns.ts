import {DataTableColumn} from '@models/data-table';
import {TranslateKey} from '@enums/translate-key';
import {InputFilter} from '@modules/filter/input/input-filter';
import {SubSector} from '@models/sub-sector';
import {InputMasks} from '@constants/input-masks';

export function SubSectorsDataTableColumns(): DataTableColumn<SubSector>[] {
    return [
        {
            title: TranslateKey.code,
            value(data) { return data.code },
            filter: new InputFilter({
                queryParam: 'code',
                imask: InputMasks.SUB_SECTOR_CODE
            })
        },
        {
            title: TranslateKey.name,
            value(data) { return data.name },
            filter: new InputFilter({
                queryParam: 'name'
            })
        },
        {
            title: TranslateKey.sectorCode,
            value(data) { return data.sectorCode },
            filter: new InputFilter({
                queryParam: 'sectorCode',
                imask: InputMasks.SECTOR_CODE
            })
        }
    ];
}
