import {Injectable} from '@angular/core';
import {BsModalService} from 'ngx-bootstrap/modal';
import {SectorDataService} from '@dataServices/sector-data.service';
import {FormGroup} from '@angular/forms';
import {SubSector} from '@models/sub-sector';

@Injectable({
  providedIn: 'root'
})
export class SubSectorsService {

    constructor(
        private _modalService: BsModalService,
        private _sectorDataService: SectorDataService
    ) { }

    createSubSector(formGroup: FormGroup): void {
        this._sectorDataService.createSubSector(formGroup.getRawValue()).subscribe(() => {
            this._modalService.hide();
        });
    }

    updateSubSector(formGroup: FormGroup<SubSector.Create>): void {
        this._sectorDataService.updateSubSector(formGroup.getRawValue().code, formGroup.getRawValue()).subscribe(() => {
            this._modalService.hide();
        });
    }
}
