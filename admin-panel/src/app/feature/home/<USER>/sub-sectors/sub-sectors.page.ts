import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {PagesSharedModule} from '../../shared/modules/pages-shared.module';
import {PaginatedDataTable} from '@models/paginated-data-table';
import {SubSector} from '@models/sub-sector';
import {TranslateKey} from '@enums/translate-key';
import {MatPaginator} from '@angular/material/paginator';
import {SectorDataService} from '@dataServices/sector-data.service';
import {Observable} from 'rxjs';
import {RequestOptions} from '@models/request-options';
import {map, takeUntil} from 'rxjs/operators';
import {SubSectorsDataTableColumns} from './sub-sectors-data-table-columns';
import {BsModalService} from 'ngx-bootstrap/modal';
import {DynamicFormDialogService} from '../../dialogs/dynamic-form-dialog/dynamic-form-dialog.service';
import {subSectorCreateFormFields} from './sub-sector-create-form-fields';
import {subSectorUpdateFormFields} from './sub-sector-update-form-fields';
import {Sector} from '@models/sector';
import {SubSectorsService} from './sub-sectors.service';
import {FormGroup} from '@angular/forms';
import {FormFields} from '@modules/dynamic-form/form-field';
import {StoreService} from '@shared/services/store.service';
import {SystemState} from '@models/system-state';
import {Permissions} from '../../../../shared/constants/permissions.constant';

@Component({
    selector: 'app-sub-sectors',
    imports: [PagesSharedModule],
    templateUrl: './sub-sectors.page.html'
})
export class SubSectorsPage extends PaginatedDataTable<SubSector> implements OnInit {

    readonly translateKeys = TranslateKey;

    readonly permissions = Permissions;

    readonly columns = SubSectorsDataTableColumns();

    get isTradingSession(): boolean {
        return this._currentState === SystemState.State.TRADING_SESSION;
    }

    private _currentState: SystemState.State;

    @ViewChild('tableContainer')
    override _tableContainer: ElementRef<HTMLDivElement>;

    @ViewChild(MatPaginator)
    override _paginator: MatPaginator;

    constructor(
        private _modalService: BsModalService,
        private _subSectorsService: SubSectorsService,
        private _sectorDataService: SectorDataService,
        private _dynamicFormDialogService: DynamicFormDialogService,
    ) {
        super();

        this._setFilterProperty();
    }

    ngOnInit(): void {
        this._fetchSystemState();

        this._modalService.onHide
            .pipe(takeUntil(this._onDestroy))
            .subscribe(this._refreshPageData.bind(this));
    }

    openUpdateSectorDialog(subSector: SubSector): void {
        const formFields: FormFields<SubSector.Create> = subSectorUpdateFormFields(subSector);

        this._dynamicFormDialogService.open({
            formTitle: TranslateKey.updateSubSector,
            formFields,
            onSubmit: this._subSectorsService.updateSubSector.bind(this)
        });
    }

    deleteSectorDialog(subSector: SubSector): void {
        this._sectorDataService.deleteSubSector(subSector.code).subscribe(() => this._modalService.hide());
    }

    override fetchExcelData(): Observable<SubSector[]> {
        const requestOptions: RequestOptions = {
            params: this.getFilterQueryParams()
        }

        return this._sectorDataService
            .getAllSubSectors(requestOptions)
            .pipe(map(resp => resp.content));
    }

    override _fetchPageData(): void {
        const requestOptions: RequestOptions = {
            hasLocalErrorHandler: true,
            params: this.getFilterQueryParams()
        }

        this._sectorDataService
            .getAllSubSectors(requestOptions)
            .pipe(takeUntil(this._onDestroy))
            .subscribe(resp => {
                this._pageData = resp;
                this._changeDetectorRef.detectChanges();
            });
    }

    private _setFilterProperty(): void {
        this.filterProperty.actionBtns.unshift(...[
            {
                title: TranslateKey.createSubSector,
                icon: 'add',
                isDisabled: () => !this.isTradingSession,
                callback: this._setSectorsAndOpenCreateSectorDialog.bind(this),
                permissions: this.permissions.CREATE_SUB_SECTOR_PERMISSION
            }
        ]);
        this.filterProperty.refreshPage = this._refreshPageData.bind(this);
        this.filterProperty.callback = this._filterPageData.bind(this);
    }

    private _setSectorsAndOpenCreateSectorDialog(): void {
        this._sectorDataService.getAllSectors().subscribe(resp => {
            this._openCreateSectorDialog(resp.content)
        });
    }

    private _openCreateSectorDialog(sectors: Sector[]): void {
        const formFields: FormFields<SubSector.Create> = subSectorCreateFormFields(sectors);

        const dynamicFormDialog = this._dynamicFormDialogService.open<SubSector.Create>({
            formTitle: TranslateKey.createSubSector,
            formFields,
            onSubmit: this._subSectorsService.createSubSector.bind(this)
        });

        this._subscribeOnSectorCode(dynamicFormDialog.content.formGroup);
    }

    private _subscribeOnSectorCode(formGroup: FormGroup<SubSector.Create>) {
        formGroup.controls.sectorCode.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe((resp: any) => {
                if (resp) {
                    const controls = formGroup.controls;
                    controls.code.enable();
                    controls.code.setValue(resp);
                }
            });
    }

    private _fetchSystemState(): void {
        StoreService.systemState.subscribe(resp => {
            this._currentState = resp;
            this._changeDetectorRef.detectChanges();
        });
    }

}
