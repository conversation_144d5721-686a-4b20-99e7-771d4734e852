import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {Valida<PERSON>} from '@angular/forms';
import {InputMasks} from '@constants/input-masks';
import {FieldsPlaceholder} from '../../../../shared/constants/fields-placeholder';
import {Sector} from '@models/sector';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';
import {AutoCompleteFormField} from '@modules/dynamic-form/form-fields/auto-complete-field/auto-complete-form-field';
import {FormFields} from '@modules/dynamic-form/form-field';

export function subSectorCreateFormFields(sectors: Sector[]): FormFields {
    return [
        new AutoCompleteFormField<Sector>({
            formControlName: 'sectorCode',
            label: TranslateKey.sectorCode,
            width: 12,
            validations: [Validators.required, Validators.maxLength(2)],
            placeholder: FieldsPlaceholder.SECTOR,
            options: sectors,
            getTitle(sector) { return sector ? sector.code + ' - ' + sector.name : ''; },
            optionFieldName: 'code',
            searchExpField: 'code'
        }),

        new InputFormField({
            formControlName: 'code',
            label: TranslateKey.code,
            validations: [Validators.required, Validators.maxLength(4)],
            width: 12,
            mask: InputMasks.SUB_SECTOR_CODE,
            disable: true,
            placeholder: FieldsPlaceholder.SECTOR_CODE
        }),

        new InputFormField({
            formControlName: 'name',
            label: TranslateKey.name,
            validations: [Validators.required, Validators.maxLength(40)],
            width: 12,
            mask: InputMasks.FA_NAME,
            placeholder: FieldsPlaceholder.SECTOR_NAME
        })
    ];
}
