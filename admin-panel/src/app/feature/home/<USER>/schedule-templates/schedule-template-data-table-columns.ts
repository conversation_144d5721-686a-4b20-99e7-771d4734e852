import {DataTableColumn} from '@models/data-table';
import {TranslateKey} from '@enums/translate-key';
import {InputFilter} from '@modules/filter/input/input-filter';
import {ScheduleTemplate} from '@models/schedule-template/schedule-template';
import {SelectOptionFilter} from '@modules/filter/select-option/select-option-filter';
import {enumToKeyValue, toCamelCase} from '@core/utils';
import {TranslatePipe} from '@ngx-translate/core';

export function ScheduleTemplatesDataTableColumns(): DataTableColumn<ScheduleTemplate>[] {
    return [
        {
            title: TranslateKey.code,
            value(data) { return data.code },
            filter: new InputFilter({
                queryParam: 'code'
            })
        },
        {
            title: TranslateKey.name,
            value(data) { return data.name.en + ' - ' + data.name.fa },
            filter: new InputFilter({
                queryParam: 'name'
            })
        },
        {
            title: TranslateKey.type,
            value(data) { return toCamelCase(data.type) },
            pipeToken:  TranslatePipe,
            filter: new SelectOptionFilter({
                queryParam: 'type',
                data: enumToKeyValue(ScheduleTemplate.Type),
                getTitle(item): string { return item.key; }
            })
        }
    ];
}
