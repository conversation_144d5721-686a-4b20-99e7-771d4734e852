import {RoutePredicatePage} from './route-predicate.page';
import {ComponentHarness} from '@test/harness/component-harness';
import {SharedModule} from '../../../../shared/shared.module';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {BsModalService} from 'ngx-bootstrap/modal';
import {RouterTestingModule} from '@angular/router/testing';
import {PageTemplateModule} from '@modules/page-template/page-template.module';
import {DataTableModule} from '@modules/data-table/data-table.module';
import {TestBed} from '@angular/core/testing';
import {RoutePredicateService} from '../../shared/data-services/route-predicate.service';
import {routePredicateServiceHarness} from '../../shared/data-services/route-predicate.service.harness';
import {of} from 'rxjs';
import {TestUtils} from '@test/test-utils';
import {HasPermissionService} from '@directives/has-permission.service';
import {hasPermissionServiceHarness} from '@directives/has-permission.service.harness';

describe('RoutePredicatePage', () => {
    let ha: ComponentHarness<RoutePredicatePage>;
    let modalService: BsModalService;

    beforeEach(() => {
        ha = new ComponentHarness(RoutePredicatePage, {
            imports: [
                RoutePredicatePage,
                TranslateTestingModule,
                RouterTestingModule,
                PageTemplateModule,
                SharedModule,
                DataTableModule,
            ],
            providers: [
                BsModalService,
                {provide: RoutePredicateService, useValue: routePredicateServiceHarness},
                {provide: HasPermissionService, useValue: hasPermissionServiceHarness}
            ],
            detectChanges: false
        });

        modalService = TestBed.inject(BsModalService);

        spyOn(modalService, 'show');
        spyOn(routePredicateServiceHarness, 'getRoutePredicates').and.returnValue(of(TestUtils.getRoutePredicates()));
        spyOn(routePredicateServiceHarness, 'deleteRoutePredicate').and.returnValue(of(null));

        ha.detectChanges();
    });

    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });

    it('should fetch page data on initialization', () => {
        ha.component.ngOnInit();

        expect(routePredicateServiceHarness.getRoutePredicates).toHaveBeenCalled();
        expect(ha.component['_pageData'].content.length).toBeGreaterThan(0);
    });


    it('should delete a predicate when clicking "Delete" button', () => {
        ha.component.ngOnInit();

        ha.get('button').thatContains('more_vert').click();
        ha.get('button').thatContains('Delete').confirm();

        expect(routePredicateServiceHarness.deleteRoutePredicate).toHaveBeenCalled();
    });
});
