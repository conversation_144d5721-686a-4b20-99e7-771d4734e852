import {DataTableColumn} from '@models/data-table';
import {RoutePredicate} from '@homeModels/route-predicate';
import {TranslateKey} from '@enums/translate-key';
import {InputFilter} from '@modules/filter/input/input-filter';
import {SelectOptionFilter} from '@modules/filter/select-option/select-option-filter';
import {enumToKeyValue} from '@core/utils';
import {RouteTree} from '@homeModels/route-tree';
import {InputMasks} from '@constants/input-masks';

export function routePredicatesDataTableColumns(): DataTableColumn<RoutePredicate>[] {
    return [
        {
            title: TranslateKey.title,
            value(data) { return data.title },
            filter: new InputFilter({
                queryParam: 'title'
            })
        },
        {
            title: TranslateKey.type,
            value(data) { return data.routeTreeType },
            filter: new SelectOptionFilter({
                skipTranslatingOptions: true,
                data: enumToKeyValue(RouteTree.Type),
                queryParam: 'routeTreeType',
                queryParamValueField: 'key'
            })
        },
        {
            title: TranslateKey.position,
            value(data) { return data.position },
            filter: new InputFilter({
                queryParam: 'position',
                imask: InputMasks.RAW_NUMBER
            })
        },
        {
            title: TranslateKey.length,
            value(data) { return data.length },
            filter: new InputFilter({
                queryParam: 'length',
                imask: InputMasks.RAW_NUMBER
            })
        }
    ];
}
