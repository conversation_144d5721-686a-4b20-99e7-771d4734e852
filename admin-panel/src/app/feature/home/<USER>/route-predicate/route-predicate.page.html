<app-page-panel [filterProperty]="filterProperty">
    <ng-container *breadcrumb>{{ translateKeys.routePredicates | translate }}</ng-container>

    <app-panel-content>
        <div class="w-100 h-100 position-absolute" #tableContainer>
            <app-data-table
                [isPageDataLoaded]="isPageDataLoaded()"
                [dataSource]="dataSource"
                [filterProperty]="filterProperty"
                [noDataRowText]="noDataRowText"
                [columns]="columns"
                [dataTableColumns]="getDataTableColumns()">

                <ng-container *rowActionBtns="let row">
                    <button
                        *hasPermission="permissions.UPDATE_ROUTE_PREDICATE_PERMISSION"
                        mat-menu-item
                        (click)="openUpdateRoutePredicateDialog(row)">
                        <mat-icon>edit</mat-icon>
                        <span>{{translateKeys.edit | translate}}</span>
                    </button>
                    <button
                        *hasPermission="permissions.DELETE_ROUTE_PREDICATE_PERMISSION"
                        mat-menu-item
                        (confirm)="deleteRoutePredicate(row)">
                        <mat-icon>delete</mat-icon>
                        <span>{{translateKeys.delete | translate}}</span>
                    </button>
                </ng-container>
            </app-data-table>

            <mat-paginator
                *paginator
                [ngClass]="paginatorClass"
                [length]="dataSourceLength"
                [pageSize]="dataSourcePageSize"
                [showFirstLastButtons]="true">
            </mat-paginator>
        </div>
    </app-panel-content>
</app-page-panel>
