import {UpdateProductDialogComponent} from './update-product-dialog.component';
import {ComponentHarness} from '@test/harness/component-harness';
import {SharedModule} from '../../../../shared/shared.module';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {HttpClientTestingModule} from '@angular/common/http/testing';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {ProductDataService} from '@dataServices/product-data.service';
import {productDataServiceHarness} from '@dataServices/product-data.service.harness';
import {Http} from '@http';
import {TestBed} from '@angular/core/testing';
import {HttpClient} from '@angular/common/http';
import {FormControl, FormGroup} from '@angular/forms';
import {Product} from '@homeModels/product';
import {OrderPreference} from '@enums/order-preference';
import {of} from 'rxjs';
import {FormGroupRawValue} from '@models/form-group-raw-value';
import {TranslateKey} from '@enums/translate-key';
import {TestUtils} from '@test/test-utils';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';

describe('UpdateProductDialogComponent', () => {
    let ha: ComponentHarness<UpdateProductDialogComponent>;
    let bsModalRefMock = { hide() {} };

    beforeEach(() => {
        ha = new ComponentHarness(UpdateProductDialogComponent, {
            declarations: [
                UpdateProductDialogComponent,
                SelectByLanguagePipe
            ],
            imports: [
                SharedModule,
                TranslateTestingModule,
                HttpClientTestingModule
            ],
            providers: [
                SelectByLanguagePipe,
                {provide: BsModalRef, useValue: bsModalRefMock},
                {provide: ProductDataService, useValue: productDataServiceHarness}
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
            detectChanges: false
        });

        spyOn(bsModalRefMock, 'hide').and.returnValue(null);

        Http.httpClient = TestBed.inject(HttpClient);

        ha.component['_formGroup'] = new FormGroup<Product.UpdateForm>({
            name: new FormGroup<any>({
                fa: new FormControl('test'),
                en: new FormControl('test')
            }),
            boardCode: new FormControl('9'),
            marketFlowCode: new FormControl('00'),
            tradingStartDate: new FormControl(),
            tradingEndDate: new FormControl(),
            maturityDate: new FormControl(),
            maxAllowedOwnership: new FormControl(0.2),
            normalBlockSize: new FormControl('1'),
            issuePrice: new FormControl(),
            parValue: new FormControl('1000'),
            matchingType: new FormControl(OrderPreference.FIFO),
            strikePrice: new FormControl(),
            underlyingProductId: new FormControl(),
            isPricePercentage: new FormControl()
        });

        ha.component.product = TestUtils.getProduct() as any;
    });

    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });

    it('should call #updateProduct on the product data service and hide the modal', () => {
        const formValue = {
            name: {en: 'Test Name', fa: 'نام تستی'},
            boardCode: '1',
            marketFlowCode: '0',
            tradingStartDate: '2001-01-01',
            tradingEndDate: '2001-01-01',
            maturityDate: '2001-01-01',
            maxAllowedOwnership: 1,
            normalBlockSize: '4',
            issuePrice: '1223',
            parValue: '10',
            matchingType: OrderPreference.FIFO,
            strikePrice: 200,
            underlyingProductId: 'IRCISIN00001',
            isPricePercentage: false
        };
        const adminCommand = TestUtils.getUpdateProductCommand();
        spyOn(productDataServiceHarness, 'updateProduct').and.returnValue(of(adminCommand));
        ha.detectChanges();

        ha.component.formGroup.setValue(formValue);
        ha.component.updateProduct();

        expect(productDataServiceHarness.updateProduct).toHaveBeenCalledWith(ha.component.product.productId, formValue as FormGroupRawValue<Product.UpdateForm>);
        expect(bsModalRefMock.hide).toHaveBeenCalled();
    });

    it('should call hide() on the modal', () => {
        ha.component.close();

        expect(bsModalRefMock.hide).toHaveBeenCalled();
    });

    it('should translateKeys return the TranslateKey enum', () => {
        expect(ha.component.translateKeys).toEqual(TranslateKey);
    });

    it('should formGroup return the _formGroup property', () => {
        expect(ha.component.formGroup).toEqual(ha.component['_formGroup']);
    });
});
