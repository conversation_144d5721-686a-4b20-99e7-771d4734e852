import {Component, OnInit} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {ProductDialog} from '@models/product-dialog';
import {Product} from '@homeModels/product';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {ProductDataService} from '@dataServices/product-data.service';
import {FormGroup} from '@angular/forms';
import {ProductFormFieldSets} from '../../shared/constants/product-form-field-sets';
import {Board} from '@models/board';
import {MarketFlow} from '@models/market-flow';
import {BoardDataService} from '@dataServices/board-data.service';
import {ChangeDetectionService} from '@services/change-detection.service';
import {SnackBarService} from '@services/snack-bar.service';
import {FormFieldSet} from '@modules/dynamic-form/form-field-sets';
import {FormFields} from '@modules/dynamic-form/form-field';
import {updateProductFormFields} from '../../shared/constants/update-product-form-fields';

@Component({
    selector: 'app-update-product-dialog',
    templateUrl: './update-product-dialog.component.html',
    standalone: false
})
export class UpdateProductDialogComponent implements OnInit, ProductDialog<Product.Details> {

    readonly translateKeys = TranslateKey;

    product: Product.Details;

    private _boards: Board[] = [];

    private _marketFlows: MarketFlow[] = [];

    private _formGroup = new FormGroup<Product.UpdateForm>({} as any);
    get formGroup(): FormGroup<Product.UpdateForm> { return this._formGroup; }

    private _formFields: FormFields;
    get formFields(): FormFields { return this._formFields; }

    private _formFieldSets: FormFieldSet[] = [
        { title: TranslateKey.productSpec, id:  ProductFormFieldSets.PRODUCT_SPEC},
        { title: TranslateKey.marketSec, id: ProductFormFieldSets.MARKET_SEC },
        { title: TranslateKey.derivativeSpec, id: ProductFormFieldSets.DERIVATIVE_SPEC }
    ];
    get formFieldSets(): FormFieldSet[] { return this._formFieldSets; }


    constructor(
        private _modalRef: BsModalRef,
        private _boardDataService: BoardDataService,
        private _productDataService: ProductDataService,
        private _snackBarService: SnackBarService
    ) { }

    ngOnInit() {
        this._formFields = updateProductFormFields(this.product as Product.Details, this._boards, this._marketFlows);
        this._setProductDetail();
    }

    updateProduct(): void {
        this._productDataService.updateProduct(this.product.productId, this._formGroup.getRawValue())
            .subscribe(() => {
                this._snackBarService.open(TranslateKey.successfullySubmitted);
                this._modalRef.hide();
            });
    }

    close(): void {
        this._modalRef.hide();
    }

    private _setProductDetail(): void {
        this._productDataService.getProduct(this.product.productId)
            .subscribe( resp => {
                this.product = resp;
                this._updateFormGroup();
                this._setAllBoards();
                this._setMarketFlows();
            });
    }

    private _updateFormGroup(): void {
        this._formGroup.markAllAsTouched();
        this._formGroup.controls.matchingType.setValue((this.product as Product.Details).matchingType);
        this._formGroup.controls.parValue.setValue((this.product as Product.Details).parValue);
        this._formGroup.controls.normalBlockSize.setValue((this.product as Product.Details).normalBlockSize.toString());
        this._formGroup.controls.issuePrice.setValue((this.product as Product.Details).issuePrice);
        this._formGroup.controls.underlyingProductId.setValue((this.product as Product.Details).underlyingProductId);
        this._formGroup.controls.strikePrice.setValue((this.product as Product.Details).strikePrice);
        ChangeDetectionService.onChange.next({type: 'FORM'});
    }

    private _setAllBoards(): void {
        this._boardDataService.getBoards().subscribe(resp => {
            resp.content.forEach(item => {
                this._boards.push(item);
            });
            ChangeDetectionService.onChange.next({type: 'FORM'});
            this._formGroup.controls.boardCode.setValue(this.product.boardCode);
            this._formGroup.updateValueAndValidity();
        });
    }

    private _setMarketFlows(): void {
        this._productDataService.getMarketFlows().subscribe(resp => {
            resp.content.forEach(item => {
                this._marketFlows.push(item);
            });
            ChangeDetectionService.onChange.next({type: 'FORM'});
            this._formGroup.controls.marketFlowCode.setValue(this.product.marketFlowCode);
            ChangeDetectionService.onChange.next({type: 'FORM'});
        });
    }

}
