<input
    (ngModelChange)="onValueChange($event)"
    [(ngModel)]="searchStr"
    [disabled]="disableSearch"
    [matAutocomplete]="auto"
    [placeholder]="placeholder | translate"
    [required]="required !== undefined"
    class="form-control"
    type="text">

<mat-autocomplete
    #auto="matAutocomplete"
    (optionSelected)="writeValue($event.option.value)"
    [displayWith]="translateOptionTitle.bind(this)"
    autoActiveFirstOption>

    <mat-optgroup
        *ngFor="let group of groupedFilteredOptions"
        [label]="group.label | lowercase | translate">
        <mat-option
            *ngFor="let option of group.children"
            [value]="option">
            {{translateOptionTitle(option)}}
        </mat-option>
    </mat-optgroup>
</mat-autocomplete>
