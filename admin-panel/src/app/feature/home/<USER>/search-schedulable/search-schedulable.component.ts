import {Component, forwardRef, Input, OnInit} from '@angular/core';
import {NG_VALUE_ACCESSOR} from '@angular/forms';
import {StoreService} from '@services/store.service';
import {Instrument} from '@homeModels/instrument';
import {LangService} from '@shared/services/lang.service';
import {TranslateKey} from '@enums/translate-key';
import {System} from '@models/system';
import {SearchableInstrument} from '@models/searchable-instrument';
import {SearchableGroup} from '@models/searchable-group';
import {plainToInstance} from '../../../../shared/plain-to-instance/plain-to-instance';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {ValueAccessor} from '@shared/value-accessor';
import {Schedulable} from '@models/schedule-template/schedulable';

interface ListGroup {
    label: Schedulable.Type;
    children: Schedulable[];
}

@Component({
    selector: 'app-search-schedulable',
    templateUrl: './search-schedulable.component.html',
    providers: [{
        provide: NG_VALUE_ACCESSOR,
        useExisting: forwardRef(() => SearchSchedulableComponent),
        multi: true
    },
        SelectByLanguagePipe],
    standalone: false
})
export class SearchSchedulableComponent extends ValueAccessor<Schedulable> implements OnInit {
    searchStr: string;
    @Input() placeholder = TranslateKey.search;
    @Input() disableSearch = false;
    @Input() required;
    @Input() contains: ('instruments' | 'group' | 'systemState')[] = [];
    private _filteredOptions: Schedulable[];
    private _data: Schedulable[] = [];

    constructor(
        private _selectByLanguage: SelectByLanguagePipe
    ) {
        super();
    }

    private _groupedFilteredOptions: ListGroup[];
    get groupedFilteredOptions(): ListGroup[] {
        return this._groupedFilteredOptions;
    }

    private _excludedInstruments: Instrument.Single[] = [];

    @Input()
    get excludedInstruments() {
        return this._excludedInstruments;
    };

    set excludedInstruments(value: Instrument.Single[]) {
        this._excludedInstruments = value;
        this._updateData();
    }

    ngOnInit(): void {
        this._updateData();
    }

    override writeValue(schedulable: Schedulable) {
        if (schedulable) {
            this._updateSearchStr(schedulable);
            this.onChange(schedulable);
        }
    }

    translateOptionTitle(option): string {
        if (!option) {
            return;
        }

        return typeof option === 'string'
            ? option
            : this._selectByLanguage.transform(option.schedulableName);
    }

    onValueChange(searchExp: string): void {
        this._filteredOptions = this._data
            .filter(option => this._selectByLanguage
                .transform(option.schedulableName)
                .toLowerCase()
                .includes(searchExp.toString().toLowerCase()));
        this._groupFilterOptionsByType();
    }

    private _concatDataWithGroup(): void {
        const groups = plainToInstance(SearchableGroup, StoreService.groups);
        this._data = [...this._data, ...groups];
    }

    private _concatDataWithSystemState(): void {
        this._data = [...this._data, new System()];
    }

    private _concatDataWithInstruments(): void {
        const instruments = plainToInstance(SearchableInstrument,
            StoreService.instruments.filter(item => !this._findExcludedInstrumentBySecurityId(item))) as any;

        this._data = [...this._data, ...instruments];
    }

    private _findExcludedInstrumentBySecurityId(instrument: any): Instrument.Single {
        return this._excludedInstruments.find(excludedItem => excludedItem.securityId === instrument.securityId);
    }

    private _updateSearchStr(schedulable: Schedulable): void {
        this.searchStr = LangService.getName(schedulable.schedulableName);
    }


    private _updateData(): void {
        this._data = [];

        if (this.contains.find(item => item === 'systemState')) {
            this._concatDataWithSystemState();
        }

        if (this.contains.find(item => item === 'group')) {
            this._concatDataWithGroup();
        }

        if (this.contains.find(item => item === 'instruments')) {
            this._concatDataWithInstruments();
        }

        this._filteredOptions = this._data;

        this._groupFilterOptionsByType();
    }

    private _groupFilterOptionsByType(): void {
        const groupedByObj = this._groupBy(this._filteredOptions);
        this._groupedFilteredOptions = Object.keys(groupedByObj).map((key: Schedulable.Type) => ({
            label: key,
            children: groupedByObj[key]
        }));
    }

    private _groupBy(list: Schedulable[]) {
        return list.reduce((currentList, item) => {
            const groupType = item.type;

            currentList[groupType] = currentList[groupType] || [];
            currentList[groupType].push(item);

            return currentList;
        }, {});
    }
}
