<ng-container *ngIf="actionStates.length">
    <!-- Title Columns -->
    <div class="form-row mb-2">
        <div class="col-2"></div>
        <div class="col-3 required">{{ translateKeys.runAt | translate }}</div>
        <div class="col-4 required">{{ translateKeys.targetState | translate }}</div>
        <div *ngIf="!isSystemAction" class="col-3">{{ translateKeys.shouldOpen | translate }}</div>
    </div>

    <!-- Input Columns -->
    <div class="form-row flex-nowrap"
         *ngFor="let action of actions; let i = index">
        <div class="col-2 d-flex p-0">
            <button
                mat-icon-button
                color="warn"
                [disabled]="actions.length === 1"
                [class.d-none]="isSingleAction"
                (click)="removeAction(i)">
                <mat-icon>remove</mat-icon>
            </button>

            <button
                mat-icon-button
                color="primary"
                [class.d-none]="isSingleAction"
                (click)="addAction(i + 1)">
                <mat-icon>add</mat-icon>
            </button>
        </div>

        <div class="form-group col-3">
            <input
                type="text"
                class="form-control"
                [disabled]="disabled"
                [name]="'time' + i"
                [imask]="timeMask"
                [(ngModel)]="action.runAt">
        </div>

        <div class="form-group" [ngClass]="isSystemAction ? 'col-6' : 'col-4'">
            <select
                class="form-control"
                [disabled]="disabled"
                [name]="'targetState' + i"
                [(ngModel)]="action.targetState">
                <option hidden selected>{{ translateKeys.select | translate }}</option>
                <option
                    *ngFor="let state of actionStates"
                    [value]="state">{{ state | camelCase | translate }}
                </option>
            </select>
        </div>

        <div
            *ngIf="!isSystemAction"
            class="form-group col-3">
            <mat-checkbox
                [name]="'shouldOpen' + i"
                [(ngModel)]="action.shouldOpen">
            </mat-checkbox>
        </div>
    </div>
</ng-container>
