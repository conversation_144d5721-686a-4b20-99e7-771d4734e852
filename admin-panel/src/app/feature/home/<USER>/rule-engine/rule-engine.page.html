<app-page-panel [filterProperty]="filterProperty">
    <ng-container *breadcrumb>{{translateKeys.ruleEngine | translate}}</ng-container>

    <app-panel-content>
        <div class="w-100 h-100 position-absolute" #tableContainer>
            <app-data-table
                [isPageDataLoaded]="isPageDataLoaded()"
                [dataSource]="dataSource"
                [filterProperty]="filterProperty"
                [noDataRowText]="noDataRowText"
                [columns]="columns"
                [dataTableColumns]="getDataTableColumns()">

                <ng-container *rowActionBtns="let row">
                    <button mat-menu-item (click)="openRuleDetailsDialog(row)">
                        <mat-icon>feed</mat-icon>
                        <span>{{translateKeys.detail | translate}}</span>
                    </button>
                    <button
                        *hasPermission="permissions.UPDATE_RULE_ENGINE_PERMISSION"
                        mat-menu-item
                        (click)="fetchTownAndUpdateRule(row)">
                        <mat-icon>edit</mat-icon>
                        <span>{{translateKeys.edit | translate}}</span>
                    </button>
                    <button
                        *hasPermission="permissions.DELETE_RULE_ENGINE_PERMISSION"
                        mat-menu-item
                        (confirm)="removeRule(row)">
                        <mat-icon>delete</mat-icon>
                        <span>{{translateKeys.delete | translate}}</span>
                    </button>
                </ng-container>
            </app-data-table>

            <mat-paginator
                *paginator
                [ngClass]="paginatorClass"
                [length]="dataSourceLength"
                [pageSize]="dataSourcePageSize"
                [showFirstLastButtons]="true">
            </mat-paginator>
        </div>
    </app-panel-content>
</app-page-panel>
