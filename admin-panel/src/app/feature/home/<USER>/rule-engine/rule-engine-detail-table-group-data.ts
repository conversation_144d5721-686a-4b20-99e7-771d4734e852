import {AdvancedTableData} from '@models/table-representation/advanced-table-representation';
import {TranslateKey} from '@enums/translate-key';
import {TableGroupData} from '@models/table-representation/table-group-data';
import {Rule, RuleEngineItem} from '@models/rule';
import {toCamelCase} from '@core/utils';
import {TranslatePipe} from '@ngx-translate/core';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {StoreService} from '@services/store.service';

function getIsNegatedValue(value: RuleEngineItem<any>): TranslateKey {
    return (value && value.isNegated !== undefined)
        ? (value.isNegated ? TranslateKey.nonOf : TranslateKey.oneOf)
        : TranslateKey.disable;
}

export function RuleEngineDetailTableGroupData(rule: Rule): AdvancedTableData[] {
    const basicData: TableGroupData[] = [
        {
            title: TranslateKey.ruleId,
            value: rule.ruleId
        },
        {
            title: TranslateKey.actionType,
            value: toCamelCase(rule.actionType),
            pipeToken: TranslatePipe
        },
        {
            title: TranslateKey.description,
            value: rule.description
        }
    ];

    const originData: TableGroupData[] = rule.originIds?.items.map(origin => (
        {
            title: TranslateKey.origin,
            value: toCamelCase(StoreService.origins.find(item => item.id === origin).type),
            pipeToken: TranslatePipe
        }
    ));

    const origins: TableGroupData[] = [
        {
            title: TranslateKey.condition,
            value: getIsNegatedValue(rule.originIds),
            pipeToken: TranslatePipe
        }
    ];
    if (originData) {
        origins.push(...originData);
    }

    const securityData: TableGroupData[] = rule.securityIds?.items.map(security => (
        {
            title: TranslateKey.instrument,
            value: StoreService.instrumentsObj[security]?.mnemonic,
            pipeToken: SelectByLanguagePipe
        }
    ));

    const securities: TableGroupData[] = [
        {
            title: TranslateKey.condition,
            value: getIsNegatedValue(rule.securityIds),
            pipeToken: TranslatePipe
        }
    ];
    if (securityData) {
        securities.push(...securityData);
    }

    const shareholderData: TableGroupData[] = rule.shareholderIds?.items.map(shareholder => (
        {
            title: TranslateKey.shareholder,
            value: shareholder,
        }
    ));

    const shareholders: TableGroupData[] = [
        {
            title: TranslateKey.condition,
            value: getIsNegatedValue(rule.shareholderIds),
            pipeToken: TranslatePipe
        }
    ];
    if (shareholderData) {
        shareholders.push(...shareholderData);
    }

    const brokerData: TableGroupData[] = rule.brokerIds?.items.map(broker => (
        {
            title: TranslateKey.broker,
            value: StoreService.brokersObj[broker].searchExpression,
        }
    ));

    const brokers: TableGroupData[] = [
        {
            title: TranslateKey.condition,
            value: getIsNegatedValue(rule.brokerIds),
            pipeToken: TranslatePipe
        }
    ];

    if (brokerData) {
        brokers.push(...brokerData);
    }

    const accountData: TableGroupData[] = rule.accountIds?.items.map(account => (
        {
            title: TranslateKey.accountId,
            value: account,
        }
    ));

    const accounts: TableGroupData[] = [
        {
            title: TranslateKey.condition,
            value: getIsNegatedValue(rule.accountIds),
            pipeToken: TranslatePipe
        }
    ];

    if (accountData) {
        accounts.push(...accountData);
    }

    const orderTypeData: TableGroupData[] = rule.orderTypes?.items.map(orderType => (
        {
            title: TranslateKey.orderType,
            value: toCamelCase(orderType),
            pipeToken: TranslatePipe
        }
    ));

    const orderTypes: TableGroupData[] = [
        {
            title: TranslateKey.condition,
            value: getIsNegatedValue(rule.orderTypes),
            pipeToken: TranslatePipe
        }
    ];

    if (orderTypeData) {
        orderTypes.push(...orderTypeData);
    }

    const traderData: TableGroupData[] = rule.traderIds?.items.map(trader => (
        {
            title: TranslateKey.trader,
            value: trader
        }
    ));

    const traders: TableGroupData[] = [
        {
            title: TranslateKey.condition,
            value: getIsNegatedValue(rule.traderIds),
            pipeToken: TranslatePipe
        }
    ];

    if (traderData) {
        traders.push(...traderData);
    }

    const requestTypeData: TableGroupData[] = rule.requestTypes?.items.map(requestType => (
        {
            title: TranslateKey.requestType,
            value: requestType.toLowerCase(),
            pipeToken: TranslatePipe
        }
    ));

    const requestTypes: TableGroupData[] = [
        {
            title: TranslateKey.condition,
            value: getIsNegatedValue(rule.requestTypes),
            pipeToken: TranslatePipe
        }
    ];

    if (requestTypeData) {
        requestTypes.push(...requestTypeData);
    }

    const townsData: TableGroupData[] = rule.traderTownCodes?.items.map(town => (
        {
            title: TranslateKey.townCode,
            value: town
        }
    ));

    const towns: TableGroupData[] = [
        {
            title: TranslateKey.condition,
            value: getIsNegatedValue(rule.traderTownCodes),
            pipeToken: TranslatePipe
        }
    ];

    if (townsData) {
        towns.push(...townsData);
    }

    return [
        {groupName: TranslateKey.basicInfo, data: basicData, width: 12},
        {groupName: TranslateKey.origins, data: origins, width: 6, float: 'start'},
        {groupName: TranslateKey.instruments, data: securities, width: 6, float: 'end'},
        {groupName: TranslateKey.shareholders, data: shareholders, width: 6, float: 'start'},
        {groupName: TranslateKey.brokers, data: brokers, width: 6, float: 'end'},
        {groupName: TranslateKey.accounts, data: accounts, width: 6, float: 'start'},
        {groupName: TranslateKey.traders, data: traders, width: 6, float: 'end'},
        {groupName: TranslateKey.orderTypes, data: orderTypes, width: 6, float: 'start'},
        {groupName: TranslateKey.requestTypes, data: requestTypes, width: 6, float: 'end'},
        {groupName: TranslateKey.traderTownCodes, data: towns, width: 6, float: 'start'}
    ];
}
