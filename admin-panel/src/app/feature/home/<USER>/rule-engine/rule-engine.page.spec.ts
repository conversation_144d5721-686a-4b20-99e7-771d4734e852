import {TestBed} from '@angular/core/testing';
import {RuleEnginePage} from './rule-engine.page';
import {BsModalService} from 'ngx-bootstrap/modal';
import {RouterTestingModule} from '@angular/router/testing';
import {Http} from '@http';
import {HttpClient} from '@angular/common/http';
import {HttpClientTestingModule} from '@angular/common/http/testing';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {SharedModule} from '../../../../shared/shared.module';
import {PageTemplateModule} from '@modules/page-template/page-template.module';
import {DataTableFunctionality} from '@test/data-table-functionality';
import {ComponentHarness} from '@test/harness/component-harness';
import {DataTableModule} from '@modules/data-table/data-table.module';
import {OpenInsertOrderDialogDirective} from '@directives/open-insert-order-dialog.directive';
import {HasPermissionService} from '@directives/has-permission.service';
import {hasPermissionServiceHarness} from '@directives/has-permission.service.harness';

describe('RuleEnginePage', () => {
    let ha: ComponentHarness<RuleEnginePage>;
    let modalService: BsModalService;

    beforeEach(() => {
        ha = new ComponentHarness(RuleEnginePage, {
            declarations: [
                OpenInsertOrderDialogDirective,
            ],
            providers: [
                BsModalService,
                {provide: HasPermissionService, useValue: hasPermissionServiceHarness}
            ],
            imports: [
                HttpClientTestingModule,
                TranslateTestingModule,
                RouterTestingModule,
                PageTemplateModule,
                DataTableModule,
                RuleEnginePage,
                SharedModule
            ],
            detectChanges: false
        });

        Http.httpClient = TestBed.inject(HttpClient);
        modalService = TestBed.inject(BsModalService);

        ha.detectChanges();
    });

    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });

    it('should open dialog-box by clicking on "Create Rule" button', () => {
        spyOn(modalService, 'show');

        ha.get('button').thatContains('Add Rule').click();

        expect(modalService.show);
    });

    describe('DataTable functionality', () => {
        it('when the total number of rows exceeds the page size, pagination should be displayed', () => {
            DataTableFunctionality.when_the_total_number_of_rows_exceeds_the_page_size_pagination_should_be_displayed(ha);
        });

        it('pagination should be hidden when the total number of rows is less than the page size', () => {
            DataTableFunctionality.pagination_should_be_hidden_when_the_total_number_of_rows_is_less_than_the_page_size(ha);
        });
    })
});
