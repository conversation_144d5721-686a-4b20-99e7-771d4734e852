import {DataTableColumn} from '@models/data-table';
import {TranslateKey} from '@enums/translate-key';
import {FailedCsdRecord} from '@models/csd-file';

export function FailedCsdRecordsDataTableColumns(): DataTableColumn<FailedCsdRecord>[] {
    return [
        {
            title: TranslateKey.originalRecord,
            isSticky: true,
            class: 'monospace',
            value(data) { return data.originalRecord }
        },
        {
            title: TranslateKey.message,
            class: 'monospace',
            value(data) { return data.message }
        }
    ];
}
