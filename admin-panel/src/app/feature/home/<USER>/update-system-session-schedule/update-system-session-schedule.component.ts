import {Component, OnInit, ViewChild} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {FormGroup} from '@angular/forms';
import {Group} from '@models/group';
import {StoreService} from '@services/store.service';
import {ScheduleDataService} from '@dataServices/schedule-data.service';
import {FormFieldSet} from '@modules/dynamic-form/form-field-sets';
import {ScheduleTemplateFormFieldSets} from '../../shared/constants/schedule-template-form-field-sets';
import {SessionSchedule} from '@homeModels/session-schedule';
import {FormFields, FormFieldsGroup} from '@modules/dynamic-form/form-field';
import {DynamicFormComponent} from '@modules/dynamic-form/dynamic-form.component';
import {SnackBarService} from '@services/snack-bar.service';
import {GroupActionFields, SetupUpdateSystemSessionScheduleFormFields} from './system-session-schedule-form-fields';
import {SessionScheduleFormDialog} from '@homeModels/session-schedule/session-schedule-form-dialog';

@Component({
    selector: 'app-update-system-session-schedule',
    templateUrl: './update-system-session-schedule.component.html',
    standalone: false
})
export class UpdateSystemSessionScheduleComponent implements OnInit, SessionScheduleFormDialog<SessionSchedule.System> {

    readonly translateKeys = TranslateKey;

    readonly formFieldSets: FormFieldSet[] = [
        {title: TranslateKey.actions, id: ScheduleTemplateFormFieldSets.ACTIONS}
    ];

    readonly groups: Group[] = StoreService.groups.slice(0);

    sessionSchedule: SessionSchedule.System;

    get hasAtLeastOneAction(): boolean {
        return !!this._formGroup.controls?.actions?.length;
    }

    private _formGroup = new FormGroup<SessionSchedule.UpdateSystemForm>({} as any);
    get formGroup(): FormGroup<SessionSchedule.UpdateSystemForm> { return this._formGroup; }

    private _formFields: FormFields<SessionSchedule.UpdateSystemForm>;
    get formFields(): FormFields { return this._formFields; };

    @ViewChild(DynamicFormComponent)
    private _dynamicFormComponent: DynamicFormComponent;


    constructor(
        private _modalRef: BsModalRef,
        private _snackBarService: SnackBarService,
        private _scheduleDataService: ScheduleDataService
    ) {}

    ngOnInit() {
        this._formFields = SetupUpdateSystemSessionScheduleFormFields();

        this._initializeActions();
    }

    private _initializeActions(): void {
        if (this.sessionSchedule?.actions?.length) {
            const actionFormArray = this._formFields.find(
                (item: FormFieldsGroup) => item.formArrayName === 'actions'
            ) as FormFieldsGroup;

            this.sessionSchedule.actions.forEach((action) => {
                const formFields = GroupActionFields(() => {
                    const index = actionFormArray.formFields.findIndex(f => f === formFields);
                    (this._formFields.rawValue.actions as any[])[index].remove();
                }, action);

                (actionFormArray.formFields as FormFields[]).push(formFields);
            });
        }
    }

    addAction(): void {
        // Find actions form array.
        const actionFormArray = this._formFields
            .find((item: FormFieldsGroup) => item.formArrayName === 'actions') as FormFieldsGroup;

        // Crete new action form Group.
        const actionsFormGroup = GroupActionFields(() => {
            const index = actionFormArray.formFields.findIndex(item => item === actionsFormGroup);

            (this._formFields.rawValue.actions as any[])[index].remove();
        });

        // Push actions its form array.
        (actionFormArray.formFields as FormFields[]).push(actionsFormGroup);

        // Update dynamic form group.
        this._dynamicFormComponent.ngAfterViewInit();

    }

    submit(): void {
        this._scheduleDataService
            .updateSystemSessionSchedule(this._formGroup?.getRawValue())
            .subscribe(() => {
                this._snackBarService.open(TranslateKey.successfullySubmitted);
                this._modalRef.hide();
            });
    }

    close(): void {
        this._modalRef.hide();
    }
}
