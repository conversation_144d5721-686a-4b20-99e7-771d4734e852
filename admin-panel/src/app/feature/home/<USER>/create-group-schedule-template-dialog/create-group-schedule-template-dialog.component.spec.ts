import {ComponentHarness} from '@test/harness/component-harness';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {HttpClientTestingModule} from '@angular/common/http/testing';
import {SharedModule} from '../../../../shared/shared.module';
import {ScheduleTemplateDataService} from '@dataServices/schedule-template-data.service';
import {scheduleTemplateDataServiceHarness} from '@dataServices/schedule-template-data.service.harness';
import {TestBed} from '@angular/core/testing';
import {CreateGroupScheduleTemplateDialogComponent} from './create-group-schedule-template-dialog.component';
import {HttpClient} from '@angular/common/http';
import {NO_ERRORS_SCHEMA} from '@angular/core';

describe('CreateGroupScheduleTemplateDialogComponent', () => {
    let ha: ComponentHarness<CreateGroupScheduleTemplateDialogComponent>;
    let bsModalRefMock = { hide() {} };

    beforeEach(() => {
        ha = new ComponentHarness(CreateGroupScheduleTemplateDialogComponent, {
            declarations: [
                CreateGroupScheduleTemplateDialogComponent
            ],
            imports: [
                SharedModule,
                TranslateTestingModule,
                HttpClientTestingModule
            ],
            providers: [
                { provide: BsModalRef, useValue: bsModalRefMock },
                { provide: ScheduleTemplateDataService, useValue: scheduleTemplateDataServiceHarness }
            ],
            schemas: [NO_ERRORS_SCHEMA],
            detectChanges: false
        });

        spyOn(bsModalRefMock, 'hide').and.returnValue(null);

        TestBed.inject(HttpClient);

        ha.detectChanges();
    });

    it('should create the component', () => {
        expect(ha.component).toBeTruthy();
    });

    it('should initialize the form fields correctly', () => {
        const formFields = ha.component.formFields;
        expect(formFields).toBeTruthy();
        expect(formFields.length).toBeGreaterThan(0);
    });

    it('should call #hide function on the modal', () => {
        ha.component.close();
        expect(bsModalRefMock.hide).toHaveBeenCalled();
    });

    it('should formGroup return the _formGroup property', () => {
        expect(ha.component.formGroup).toEqual(ha.component['_formGroup']);
    });
});



