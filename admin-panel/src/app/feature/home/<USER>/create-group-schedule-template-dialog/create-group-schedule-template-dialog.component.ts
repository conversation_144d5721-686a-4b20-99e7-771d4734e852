import {Component, OnInit, ViewChild} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {FormGroup} from '@angular/forms';
import {ScheduleTemplate} from '@models/schedule-template/schedule-template';
import {FormFields, FormFieldsGroup} from '@modules/dynamic-form/form-field';
import {DynamicFormComponent} from '@modules/dynamic-form/dynamic-form.component';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {ScheduleTemplateDataService} from '@dataServices/schedule-template-data.service';
import {SnackBarService} from '@services/snack-bar.service';
import {GroupActionFields, SetupCreateScheduleTemplateFormFields} from './setup-create-group-schedule-from-fields';
import {FormFieldSet} from '@modules/dynamic-form/form-field-sets';
import {ScheduleTemplateFormFieldSets} from '../../shared/constants/schedule-template-form-field-sets';

@Component({
    selector: 'app-create-group-schedule-template-dialog',
    templateUrl: './create-group-schedule-template-dialog.component.html',
    standalone: false
})
export class CreateGroupScheduleTemplateDialogComponent implements OnInit {
    readonly translateKeys = TranslateKey;

    readonly formFieldSets: FormFieldSet[] = [
        {title: TranslateKey.actions, id: ScheduleTemplateFormFieldSets.ACTIONS}
    ];

    get hasAtLeastOneAction(): boolean {
        return !!this._formGroup.controls?.groupActions?.length;
    }

    private _formGroup = new FormGroup<ScheduleTemplate.CreateGroup>({} as any);
    get formGroup(): FormGroup<ScheduleTemplate.CreateGroup> { return this._formGroup; }

    private _formFields: FormFields<ScheduleTemplate.CreateGroup>;
    get formFields(): FormFields { return this._formFields; };

    @ViewChild(DynamicFormComponent)
    private _dynamicFormComponent: DynamicFormComponent;

    constructor(
        private _modalRef: BsModalRef,
        private _snackBarService: SnackBarService,
        private _scheduleTemplateDataService: ScheduleTemplateDataService
    ) {}

    ngOnInit(): void {
        this._formFields = SetupCreateScheduleTemplateFormFields();
    }

    addAction(): void {
        // Find actions form array.
        const actionFormArray = this._formFields
            .find((item: FormFieldsGroup) => item.formArrayName === 'groupActions') as FormFieldsGroup;

        // Crete new action form Group.
        const actionsFormGroup = GroupActionFields(() => {
            const index = actionFormArray.formFields.findIndex(item => item === actionsFormGroup);

            (this._formFields.rawValue.groupActions as any[])[index].remove();
        });

        // Push instrument form group to its form array.
        (actionFormArray.formFields as FormFields[]).push(actionsFormGroup);

        // Update dynamic form group.
        this._dynamicFormComponent.ngAfterViewInit();

    }

    submit(): void {
        this._scheduleTemplateDataService
            .addTemplate(this._formGroup?.getRawValue())
            .subscribe(() => {
                this._snackBarService.open(TranslateKey.successfullySubmitted);
                this._modalRef.hide();
            });
    }

    close(): void {
        this._modalRef.hide();
    }
}
