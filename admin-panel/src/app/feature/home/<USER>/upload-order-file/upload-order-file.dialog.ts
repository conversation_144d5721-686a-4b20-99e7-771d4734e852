import {Component} from '@angular/core';
import {OrderDataService} from '@dataServices/order-data.service';
import {TranslateKey} from '@enums/translate-key';
import {UploadOrderStatus} from '@models/upload';

@Component({
    templateUrl: './upload-order-file.dialog.html',
    standalone: false
})
export class UploadOrderFileDialog {
    readonly translateKeys = TranslateKey;
    orderFileUploadMethod;
    uploadStatusMethod;

    constructor(private orderDataService: OrderDataService) {
        this.orderFileUploadMethod = this.orderDataService.uploadFile;
        this.uploadStatusMethod = this.orderDataService.getStatusOfUploadFile;
    }

    orderFileProcessPercentage(uploadInfo: UploadOrderStatus): number {
        return (uploadInfo?.sendMessageCount * 100) / uploadInfo?.totalMessageCount;
    }
}
