import {ComponentFixture, TestBed} from '@angular/core/testing';
import {UploadOrderFileDialog} from './upload-order-file.dialog';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {ModalHeaderComponent} from '@modules/shared-declarations/modal-header/modal-header.component';
import {UploadFileComponent} from '../../components/upload-file/upload-file.component';
import {provideHttpClientTesting} from '@angular/common/http/testing';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {SharedModule} from '../../../../shared/shared.module';
import {SharedDeclarations} from '@modules/shared-declarations/shared-declarations';
import {provideHttpClient, withInterceptorsFromDi} from '@angular/common/http';

describe('UploadFileDialog', () => {
    let component: UploadOrderFileDialog;
    let fixture: ComponentFixture<UploadOrderFileDialog>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            declarations: [
                UploadOrderFileDialog,
                ModalHeaderComponent,
                UploadFileComponent
            ],
            imports: [TranslateTestingModule,
                SharedModule,
                SharedDeclarations],
            providers: [BsModalRef, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()]
        })
            .compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(UploadOrderFileDialog);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
