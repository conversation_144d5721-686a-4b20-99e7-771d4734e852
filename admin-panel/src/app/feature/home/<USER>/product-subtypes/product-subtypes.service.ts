import {Injectable} from '@angular/core';
import {BsModalService} from 'ngx-bootstrap/modal';
import {ProductDataService} from '@dataServices/product-data.service';
import {FormGroup} from '@angular/forms';
import {ProductSubType} from '@models/product-sub-type';

@Injectable({
  providedIn: 'root'
})
export class ProductSubtypesService {

    constructor(
        private _modalService: BsModalService,
        private _productDataService: ProductDataService
    ) { }

    createProductSubType(formGroup: FormGroup<ProductSubType.Create>): void {
        this._productDataService.createProductSubType(formGroup.getRawValue()).subscribe(() => {
            this._modalService.hide();
        });
    }

    updateProductSubType(formGroup: FormGroup<ProductSubType.Create>): void {
        this._productDataService.updateProductSubType(formGroup.getRawValue().code, formGroup.getRawValue()).subscribe(() => {
            this._modalService.hide();
        });
    }
}
