import {TableDataProvider} from '@models/table-representation/table-representable';

export class CommandQueueDetail {
    rejectionCause: string[];
    commandRequest: TableDataProvider;
    title: string;

    constructor({commandRequest, title, rejectionCause}: CommandQueueDetail) {
        this.rejectionCause = rejectionCause;
        this.commandRequest = commandRequest;
        this.title = title;
    }
}
