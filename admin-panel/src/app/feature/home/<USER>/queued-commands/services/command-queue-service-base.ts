import {BsModalService} from 'ngx-bootstrap/modal';
import {DynamicFormDialogService} from '../../../dialogs/dynamic-form-dialog/dynamic-form-dialog.service';
import {SnackBarService} from '@services/snack-bar.service';
import {QueuedCommandDataService} from '@dataServices/queued-command-data.service';
import {QueuedCommand} from '@models/queued-command';
import {TranslateKey} from '@enums/translate-key';
import {FormGroup} from '@angular/forms';
import {Observable} from 'rxjs';
import {Injectable} from '@angular/core';

@Injectable()
export abstract class CommandQueueServiceBase {

    abstract updateCommand(command: QueuedCommand): void | Promise<any>;

    protected abstract _getUpdateCommandMethod(command: QueuedCommand, formGroup: FormGroup): Observable<any>;

    constructor(
        protected _modalService: BsModalService,
        protected _snackBarService: SnackBarService,
        protected _dynamicFormDialogService: DynamicFormDialogService,
        protected _queuedCommandDataService: QueuedCommandDataService
    ) {}

    protected _onSubmitForm(command: QueuedCommand, formGroup: FormGroup): void {
        this._getUpdateCommandMethod(command, formGroup)
            .subscribe(() => {
                this._modalService.hide();
                this._snackBarService.open(TranslateKey.successfullySubmitted);
            });
    }
}
