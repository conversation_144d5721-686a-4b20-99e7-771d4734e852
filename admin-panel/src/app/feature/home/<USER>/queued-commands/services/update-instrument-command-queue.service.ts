import {Injectable} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {QueuedCommand} from '@models/queued-command';
import {CommandQueueServiceBase} from './command-queue-service-base';
import {FormGroup} from '@angular/forms';
import {FormFieldSet} from '@modules/dynamic-form/form-field-sets';
import {
    UpdateInstrumentFormFieldSets
} from '../../../dialogs/update-instrument-dialog/update-instrument-form-field-sets';
import {updateInstrumentFormFields} from '../../../shared/constants/update-instrument-form-fields';
import {InstrumentAdminCommands} from '@homeModels/instrument/instrument-admin-commands';


@Injectable()
export class UpdateInstrumentCommandQueueService extends CommandQueueServiceBase {

    private _formFieldSets: FormFieldSet[] = [
        { title: TranslateKey.orderSpec, id: UpdateInstrumentFormFieldSets.ORDER_SPEC }
    ];

    updateCommand(command: QueuedCommand<InstrumentAdminCommands.Update>) {
        this._dynamicFormDialogService.open({
            formTitle: TranslateKey.changeGroupStaticPriceBand,
            formFields: updateInstrumentFormFields(command.request),
            formFieldSets: this._formFieldSets,
            onSubmit: this._onSubmitForm.bind(this, command)
        }, {class: 'modal-lg'});
    }

    override _getUpdateCommandMethod(command: QueuedCommand, formGroup: FormGroup) {
        return this._queuedCommandDataService
            .updateInstrumentCommand(
                command.securityId,
                command.majorPriority,
                command.minorPriority,
                formGroup.getRawValue()
            );
    }
}
