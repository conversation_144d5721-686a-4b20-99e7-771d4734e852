import {Injectable} from '@angular/core';
import {Schedule} from '@models/schedule';
import {TranslateKey} from '@enums/translate-key';
import {
    associateGroupScheduleFormFields,
    AssociateScheduleFormFieldSets,
    associateSystemScheduleFormFields
} from '../constants/associate-group-schedule-form-fields';
import {FormGroup} from '@angular/forms';
import {DynamicFormDialogService} from '../../dialogs/dynamic-form-dialog/dynamic-form-dialog.service';
import {ScheduleDataService} from '@dataServices/schedule-data.service';
import {BsModalService} from 'ngx-bootstrap/modal';
import {ScheduleTemplate} from '@models/schedule-template/schedule-template';
import {ScheduleTemplateDataService} from '@dataServices/schedule-template-data.service';
import {Group} from '@models/group';

type AssociateTemplateFormGroup = FormGroup<Schedule.AssociateTemplateFormGroup>;

@Injectable({
    providedIn: 'root'
})
export class ScheduleService {

    constructor(
        private _modalService: BsModalService,
        private _scheduleDataService: ScheduleDataService,
        private _dynamicFormDialogService: DynamicFormDialogService,
        private _scheduleTemplateDataService: ScheduleTemplateDataService
    ) {}

    associateSystemSchedule = async (schedule?: any) => {
        const scheduleTemplates = (await this._fetchAllSchedules())
            .filter(item => !item.groupActions);

        this._dynamicFormDialogService.open({
            formTitle: TranslateKey.systemSchedules,
            formFields: associateSystemScheduleFormFields(scheduleTemplates, schedule),
            hasKeepDialogOpen: !schedule,
            formFieldSets: AssociateScheduleFormFieldSets,
            onSubmit: (schedule
                ? this._onUpdateSystemSchedule
                : this._onCreateSystemSchedule).bind(this)
        }, {class: 'non-scrollable-modal-body'});
    }

    associateGroupSchedule = async (schedule?: any, group?: Group) => {
        const scheduleTemplates = (await this._fetchAllSchedules())
            .filter(item => !item.systemActions);

        this._dynamicFormDialogService.open({
            formTitle: TranslateKey.groupSchedules,
            formFields: associateGroupScheduleFormFields(scheduleTemplates, schedule, group),
            hasKeepDialogOpen: !schedule,
            formFieldSets: AssociateScheduleFormFieldSets,
            onSubmit: (schedule
                ? this._onUpdateGroupSchedule
                : this._onCreateGroupSchedule).bind(this)
        }, {class: 'non-scrollable-modal-body'});
    }

    private _onCreateGroupSchedule(formGroup: AssociateTemplateFormGroup, keepOpenDialog: boolean): void {
        this._scheduleDataService
            .setScheduleOnBatchGroup(formGroup.getRawValue())
            .subscribe(() => {
                if (!keepOpenDialog) { this._modalService.hide(); }
            })
    }

    private _onUpdateGroupSchedule(formGroup: AssociateTemplateFormGroup): void {
        const formBody = formGroup.getRawValue();

        this._scheduleDataService
            .updateGroupSchedule(formBody.groupCodes[0], formBody.from, formBody)
            .subscribe(() => this._modalService.hide());
    }

    private _onCreateSystemSchedule(formGroup: AssociateTemplateFormGroup, keepOpenDialog: boolean): void {
        this._scheduleDataService
            .setScheduleOnSystem(formGroup.getRawValue())
            .subscribe(() => {
                if (!keepOpenDialog) { this._modalService.hide(); }
            })
    }

    private _onUpdateSystemSchedule(formGroup: AssociateTemplateFormGroup): void {
        const formBody = formGroup.getRawValue();

        this._scheduleDataService
            .updateSystemSchedule(formBody.from, formBody)
            .subscribe(() => this._modalService.hide());
    }

    private _fetchAllSchedules(): Promise<ScheduleTemplate[]> {
        return this._scheduleTemplateDataService
            .getAllTemplates()
            .toPromise();
    }
}
