import {CreateBatchSessionScheduleComponent} from './create-batch-session-schedule.component';
import {ComponentHarness} from '@test/harness/component-harness';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {SharedModule} from '../../../../shared/shared.module';

import {ModalHeaderComponent} from '@modules/shared-declarations/modal-header/modal-header.component';
import {BsModalService} from 'ngx-bootstrap/modal';

describe('CreateBatchSessionScheduleComponent', () => {
    let ha: ComponentHarness<CreateBatchSessionScheduleComponent>;

    beforeEach(() => {
        ha = new ComponentHarness(CreateBatchSessionScheduleComponent, {
            declarations: [
                CreateBatchSessionScheduleComponent,
                ModalHeaderComponent
            ],
            imports: [
                TranslateTestingModule,
                SharedModule
            ],
            providers: [BsModalService],
            detectChanges: false,
        });
    });

    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });
});
