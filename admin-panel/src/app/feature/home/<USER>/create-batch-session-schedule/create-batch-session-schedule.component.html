<app-modal-header>{{ translateKeys.addGroupStateScheduleTemplate | translate }}</app-modal-header>

<div class="modal-body">
    <app-dynamic-form
        [formGroup]="formGroup"
        [formFields]="formFields"
        [formFieldSets]="formFieldSets">
    </app-dynamic-form>
</div>

<div class="modal-footer justify-content-between">
    <button
        type="button"
        mat-flat-button
        color="primary"
        class="w-auto"
        (click)="addAction()">
        <mat-icon style="height: 20px !important;">add</mat-icon>
        {{ translateKeys.createScheduleAction | translate }}
    </button>


    <div class="d-flex">
        <button
            type="button"
            mat-flat-button
            color="primary"
            class="w-auto"
            [disabled]="formGroup.invalid || !hasAtLeastOneAction"
            (click)="submit()">
            {{ translateKeys.submit | translate }}
        </button>

        <!-- Close Button -->
        <button
            type="button"
            mat-button
            style="text-align: center!important;"
            (click)="close()">
            {{ translateKeys.close | translate }}
        </button>
    </div>
</div>
