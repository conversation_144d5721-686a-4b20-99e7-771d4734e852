import {Component, OnInit, ViewChild} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {FormGroup} from '@angular/forms';
import {Group} from '@models/group';
import {StoreService} from '@services/store.service';
import {ScheduleDataService} from '@dataServices/schedule-data.service';
import {FormFieldSet} from '@modules/dynamic-form/form-field-sets';
import {ScheduleTemplateFormFieldSets} from '../../shared/constants/schedule-template-form-field-sets';
import {SessionSchedule} from '@homeModels/session-schedule';
import {FormFields, FormFieldsGroup} from '@modules/dynamic-form/form-field';
import {DynamicFormComponent} from '@modules/dynamic-form/dynamic-form.component';
import {SnackBarService} from '@services/snack-bar.service';
import {GroupActionFields, SetupCreateGroupBatchSessionScheduleFormFields} from './group-session-schedule-form-fields';
import {SessionScheduleFormDialog} from '@homeModels/session-schedule/session-schedule-form-dialog';

@Component({
    selector: 'app-create-batch-session-schedule',
    templateUrl: './create-batch-session-schedule.component.html',
    standalone: false
})
export class CreateBatchSessionScheduleComponent implements OnInit, SessionScheduleFormDialog<SessionSchedule.Group> {

    readonly translateKeys = TranslateKey;

    readonly formFieldSets: FormFieldSet[] = [
        {title: TranslateKey.actions, id: ScheduleTemplateFormFieldSets.ACTIONS}
    ];

    readonly groups: Group[] = StoreService.groups.slice(0);

    sessionSchedule: SessionSchedule.Group;

    get hasAtLeastOneAction(): boolean {
        return !!this._formGroup.controls?.actions?.length;
    }

    private _formGroup = new FormGroup<SessionSchedule.CreateGroupBatchForm>({} as any);
    get formGroup(): FormGroup<SessionSchedule.CreateGroupBatchForm> { return this._formGroup; }

    private _formFields: FormFields<SessionSchedule.CreateGroupBatchForm>;
    get formFields(): FormFields { return this._formFields; };

    @ViewChild(DynamicFormComponent)
    private _dynamicFormComponent: DynamicFormComponent;


    constructor(
        private _modalRef: BsModalRef,
        private _snackBarService: SnackBarService,
        private _scheduleDataService: ScheduleDataService
    ) {}

    ngOnInit() {
        this._formFields = SetupCreateGroupBatchSessionScheduleFormFields(this.sessionSchedule);

        this._initializeActions();
    }

    private _initializeActions(): void {
        if (this.sessionSchedule?.actions?.length) {
            const actionFormArray = this._formFields.find(
                (item: FormFieldsGroup) => item.formArrayName === 'actions'
            ) as FormFieldsGroup;

            this.sessionSchedule.actions.forEach((action) => {
                const formFields = GroupActionFields(() => {
                    const index = actionFormArray.formFields.findIndex(f => f === formFields);
                    (this._formFields.rawValue.actions as any[])[index].remove();
                }, action);

                (actionFormArray.formFields as FormFields[]).push(formFields);
            });
        }
    }

    addAction(): void {
        // Find actions form array.
        const actionFormArray = this._formFields
            .find((item: FormFieldsGroup) => item.formArrayName === 'actions') as FormFieldsGroup;

        // Crete new action form Group.
        const actionsFormGroup = GroupActionFields(() => {
            const index = actionFormArray.formFields.findIndex(item => item === actionsFormGroup);

            (this._formFields.rawValue.actions as any[])[index].remove();
        });

        // Push actions its form array.
        (actionFormArray.formFields as FormFields[]).push(actionsFormGroup);

        // Update dynamic form group.
        this._dynamicFormComponent.ngAfterViewInit();

    }

    submit(): void {
        this._scheduleDataService
            .updateGroupSessionSchedule(this._formGroup?.getRawValue())
            .subscribe(() => {
                this._snackBarService.open(TranslateKey.successfullySubmitted);
                this._modalRef.hide();
            });
    }

    close(): void {
        this._modalRef.hide();
    }
}
