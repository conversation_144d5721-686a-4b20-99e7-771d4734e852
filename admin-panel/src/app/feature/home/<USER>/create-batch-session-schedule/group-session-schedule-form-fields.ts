import {FormFields} from "@modules/dynamic-form/form-field";
import {InputForm<PERSON>ield} from "@modules/dynamic-form/form-fields/input-field/input-form-field";
import {Translate<PERSON>ey} from "@enums/translate-key";
import {Validators} from "@angular/forms";
import {FieldsPlaceholder} from "../../../../shared/constants/fields-placeholder";
import {SelectOptionFormField} from "@modules/dynamic-form/form-fields/select-option-field/select-option-form-field";
import {Group} from "@models/group";
import {enumToKeyValue, toCamelCase} from "@core/utils";
import {ActionButtonField} from "@modules/dynamic-form/form-fields/action-button-field/action-button-field";
import {ScheduleTemplateFormFieldSets} from "../../shared/constants/schedule-template-form-field-sets";
import {InputMasks} from "@constants/input-masks";
import {CheckboxFormField} from "@modules/dynamic-form/form-fields/checkbox-field/checkbox-form-field";
import {MultiPick<PERSON>ield} from "@modules/dynamic-form/form-fields/multi-pick-field/multi-pick-field";
import {StoreService} from "@services/store.service";
import {SessionSchedule} from "@homeModels/session-schedule";
import {GroupStateTemplateAction} from "@models/schedule-template/group-state-template-action";

export function SetupCreateGroupBatchSessionScheduleFormFields(sessionSchedule?: SessionSchedule.Group): FormFields {
    return [
        new MultiPickField<Group>({
            formControlName: 'groupCodes',
            label: TranslateKey.groups,
            searchExpField: 'searchExpression',
            list: StoreService.groups.slice(0),
            validations: [Validators.required],
            width: 12,
            optionFieldName: 'code',
            getTitle(item): any { return item.name.fa; },
            placeholder: FieldsPlaceholder.GROUP_CODE,
            value: !!sessionSchedule?.group ? [sessionSchedule?.group] : null,
            disable: !!sessionSchedule?.group
        }),

        {
            formArrayName: 'actions',
            formFields: []
        }
    ];
}

export function GroupActionFields(onRemove: () => void, action?: GroupStateTemplateAction): FormFields {
    return [
        new ActionButtonField({
            title: '-',
            width: 1,
            fieldSetId: ScheduleTemplateFormFieldSets.ACTIONS,
            onClick: onRemove,
            style: {marginTop: '28px'}
        }),

        new InputFormField({
            formControlName: 'runAt',
            label: TranslateKey.runAt,
            width: 4,
            validations: [Validators.required],
            fieldSetId: ScheduleTemplateFormFieldSets.ACTIONS,
            mask: InputMasks.TIME_MASK,
            unmask: false,
            value: action?.runAt || '00:00:00'
        }),

        new SelectOptionFormField({
            formControlName: 'targetState',
            label: TranslateKey.targetState,
            width: 4,
            validations: [Validators.required],
            options: enumToKeyValue(Group.SchedulableState),
            optionFieldName: 'key',
            fieldSetId: ScheduleTemplateFormFieldSets.ACTIONS,
            value: action?.targetState,
            getTitle(state): string { return toCamelCase(state.value); },
        }),

        new CheckboxFormField({
            formControlName: 'shouldOpen',
            label: TranslateKey.shouldOpen,
            width: 3,
            fieldSetId: ScheduleTemplateFormFieldSets.ACTIONS,
            style: {marginTop: '25px'},
            value: action?.shouldOpen ?? false
        })
    ];
}
