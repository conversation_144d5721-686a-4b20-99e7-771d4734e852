import {TestBed} from '@angular/core/testing';
import {CorporateActionDialogComponent} from './corporate-action-dialog.component';
import {Http} from '@http';
import {HttpClient} from '@angular/common/http';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {HttpClientTestingModule} from '@angular/common/http/testing';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {ModalHeaderComponent} from '@modules/shared-declarations/modal-header/modal-header.component';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {SharedModule} from '../../../../shared/shared.module';
import {SharedDeclarations} from '@modules/shared-declarations/shared-declarations';
import {CorporateActions} from '@enums/corporate-actions';
import {of} from 'rxjs';
import {TestUtils} from '@test/test-utils';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {ComponentHarness} from '@test/harness/component-harness';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {ProductDataService} from '@dataServices/product-data.service';

describe('CorporateActionDialogComponent', () => {
    let ha: ComponentHarness<CorporateActionDialogComponent>;
    let productDataService: ProductDataService;

    beforeEach(() => {
        ha = new ComponentHarness(CorporateActionDialogComponent, {
            declarations: [
                CorporateActionDialogComponent,
                ModalHeaderComponent,
                SelectByLanguagePipe
            ],
            imports: [
                TranslateTestingModule,
                HttpClientTestingModule,
                SharedModule,
                SharedDeclarations,
                BrowserAnimationsModule
            ],
            providers: [
                BsModalRef
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
            detectChanges: false
        });

        Http.httpClient = TestBed.inject(HttpClient);
        productDataService = TestBed.inject(ProductDataService);

        ha.component.product = TestUtils.getProduct();

        ha.detectChanges();
    });

    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });

    it('according to selected Corporate Actions, #submitForm should call related endpoints', () => {
        spyOn(productDataService, 'setCorporateAction').and.returnValue(of(null));

        ha.component.submitForm();
        expect(productDataService.setCorporateAction)
            .toHaveBeenCalledWith('IRB5GACH0046', CorporateActions.DIVIDENDS, jasmine.objectContaining({}));
    });

    it('should initialize form groups correctly', () => {
        expect(ha.component.rightsFormFields.length).toBeTruthy();
        expect(ha.component.dividendsFormFields.length).toBeTruthy();
        expect(ha.component.bonusRightsFormFields.length).toBeTruthy();
        expect(ha.component.bonusFormFields.length).toBeTruthy();
        expect(ha.component.customFormFields.length).toBeTruthy();
    });
});
