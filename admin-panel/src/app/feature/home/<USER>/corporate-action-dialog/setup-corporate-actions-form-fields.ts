import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {InputMasks} from '@constants/input-masks';
import {Valida<PERSON>} from '@angular/forms';
import {CustomValidators} from '@constants/custom-validators';
import {FormFields} from '@modules/dynamic-form/form-field';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';
import {RadioButtonFormField} from '@modules/dynamic-form/form-fields/radio-button-field/radio-button-form-field';

export enum FormFieldSets {
    BONUS = 'bonus',
    RIGHTS = 'rights'
}

const width = 12;

export class SetupCorporateActions {
    static setupRightsFormFields(): FormFields {
        return [
            new InputFormField({
                formControlName: 'offeredShares',
                label: TranslateKey.offeredShares,
                width,
                validations: [Validators.required, Validators.maxLength(13), CustomValidators.greaterThan(0)],
                mask: InputMasks.COMMA_SEPERATED_NUMBER,
                placeholder: '500000000'
            }),
            new InputFormField({
                formControlName: 'heldShares',
                label: TranslateKey.heldShares,
                width,
                validations: [Validators.required, Validators.maxLength(13), CustomValidators.greaterThan(0)],
                mask: InputMasks.COMMA_SEPERATED_NUMBER,
                placeholder: '7000000000'
            }),
            new InputFormField({
                formControlName: 'discountedPrice',
                label: TranslateKey.discountedPrice,
                width,
                validations: [Validators.required, Validators.maxLength(13), CustomValidators.greaterThan(0)],
                mask: InputMasks.COMMA_SEPERATED_NUMBER,
                placeholder: '1000'
            })
        ];
    }

    static setupDividendsFormFields(): FormFields {
        const isPercentageOptions = [
            {title: TranslateKey.dividendAmount, value: false},
            {title: TranslateKey.dividendPercentage, value: true}
        ];
        return [
            new RadioButtonFormField({
                formControlName: 'isPercentage',
                label: '',
                width,
                validations: [Validators.required],
                value: false,
                options: isPercentageOptions,
                optionFieldName: 'value',
                getTitle(option): string {
                    return option.title
                }
            }),
            new InputFormField({
                formControlName: 'value',
                label: TranslateKey.value,
                width,
                validations: [Validators.required, CustomValidators.greaterThan(0)],
                mask: InputMasks.COMMA_SEPERATED_NUMBER,
                placeholder: '1000'
            })
        ];
    }

    static setupBonusRightsFormFields(): FormFields {
        return [
            {
                formGroupName: 'bonusInputs',
                formFields: [
                    new InputFormField({
                        formControlName: 'offeredShares',
                        label: TranslateKey.offeredShares,
                        width,
                        validations: [Validators.required, Validators.maxLength(13), CustomValidators.greaterThan(0)],
                        mask: InputMasks.COMMA_SEPERATED_NUMBER,
                        placeholder: '3500000000',
                        fieldSetId: FormFieldSets.BONUS
                    }),
                    new InputFormField({
                        formControlName: 'heldShares',
                        label: TranslateKey.heldShares,
                        width,
                        validations: [Validators.required, Validators.maxLength(13), CustomValidators.greaterThan(0)],
                        mask: InputMasks.COMMA_SEPERATED_NUMBER,
                        placeholder: '7000000000',
                        fieldSetId: FormFieldSets.BONUS
                    })
                ]
            },
            {
                formGroupName: 'rightsInputs',
                formFields: [
                    new InputFormField({
                        formControlName: 'offeredShares',
                        label: TranslateKey.offeredShares,
                        width,
                        validations: [Validators.required, Validators.maxLength(13), CustomValidators.greaterThan(0)],
                        mask: InputMasks.COMMA_SEPERATED_NUMBER,
                        placeholder: '500000000',
                        fieldSetId: FormFieldSets.RIGHTS
                    }),
                    new InputFormField({
                        formControlName: 'heldShares',
                        label: TranslateKey.heldShares,
                        width,
                        validations: [Validators.required, Validators.maxLength(13), CustomValidators.greaterThan(0)],
                        mask: InputMasks.COMMA_SEPERATED_NUMBER,
                        placeholder: '7000000000',
                        fieldSetId: FormFieldSets.RIGHTS
                    }),
                    new InputFormField({
                        formControlName: 'discountedPrice',
                        label: TranslateKey.discountedPrice,
                        width,
                        validations: [Validators.required, Validators.maxLength(13), CustomValidators.greaterThan(0)],
                        mask: InputMasks.COMMA_SEPERATED_NUMBER,
                        placeholder: '1000',
                        fieldSetId: FormFieldSets.RIGHTS
                    })
                ]
            }
        ];
    }

    static setupBonusFormFields(): FormFields {
        return [
            new InputFormField({
                formControlName: 'offeredShares',
                label: TranslateKey.offeredShares,
                width,
                validations: [Validators.required, Validators.maxLength(13), CustomValidators.greaterThan(0)],
                mask: InputMasks.COMMA_SEPERATED_NUMBER,
                placeholder: '3500000000',
            }),
            new InputFormField({
                formControlName: 'heldShares',
                label: TranslateKey.heldShares,
                width,
                validations: [Validators.required, Validators.maxLength(13), CustomValidators.greaterThan(0)],
                mask: InputMasks.COMMA_SEPERATED_NUMBER,
                placeholder: '7000000000',
            })
        ];
    }

    static setupCustomFormFields(): FormFields {
        return [
            new InputFormField({
                formControlName: 'newTotalShareNumbers',
                label: TranslateKey.newTotalShareNumbers,
                width,
                validations: [Validators.required, Validators.maxLength(13)],
                mask: InputMasks.COMMA_SEPERATED_NUMBER,
                placeholder: '7000000000',
            }),
            new InputFormField({
                formControlName: 'newClosingPrice',
                label: TranslateKey.newClosingPrice,
                width,
                validations: [Validators.required, Validators.maxLength(13), Validators.min(0)],
                mask: InputMasks.COMMA_SEPERATED_NUMBER,
                placeholder: '1000',
            })
        ];
    }
}
