import {Component} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {InitializationDataService} from '@dataServices/initialization-data.service';
import {StoreService} from '@services/store.service';
import {AdminCommandCallback} from '@models/admin-command-callback';
import {AdminCommand} from '@models/admin-command';
import {Permissions} from '../../../../shared/constants/permissions.constant';

@Component({
    selector: 'app-start-market-dialog',
    templateUrl: './start-market-dialog.component.html',
    standalone: false
})
export class StartMarketDialogComponent {

    readonly translateKeys = TranslateKey;

    readonly permissions = Permissions;

    constructor(
      private _initializationDataService: InitializationDataService,
      public modalRef: BsModalRef
    ) { }

    startMarket(): void {
      this._initializationDataService.startMarket().subscribe((resp) => {
          this.modalRef.hide();
          const adminCommandCallback = new AdminCommandCallback(resp.commandId, this._updateIsStartMarket.bind(this));
          StoreService.adminCommandCallBacks.push(adminCommandCallback);
      });
    }

    private _updateIsStartMarket(adminCommand: AdminCommand): void {
        if (adminCommand.isSuccess) {
          StoreService.marketStartChange.next(true);
      }
    }

}
