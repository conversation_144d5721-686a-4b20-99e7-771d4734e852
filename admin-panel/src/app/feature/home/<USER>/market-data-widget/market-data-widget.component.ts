import {ChangeDetectionStrategy, Component, Input} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {TableGroupData} from '@models/table-representation/table-group-data';
import {SharedModule} from '../../../../shared/shared.module';
import {SharedDeclarations} from '@modules/shared-declarations/shared-declarations';

@Component({
    selector: 'app-market-data-widget',
    templateUrl: './market-data-widget.component.html',
    imports: [
        SharedModule,
        SharedDeclarations
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class MarketDataWidgetComponent {
    readonly translateKeys = TranslateKey;

    @Input()
    widgetData: TableGroupData[];
}
