import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {Http} from '@http';
import {RequestOptions} from '@models/request-options';
import {map} from 'rxjs/operators';
import {Page} from '@models/page';
import {plainToInstance} from '../../../../shared/plain-to-instance/plain-to-instance';
import {FormGroupRawValue} from '@models/form-group-raw-value';
import {Cap} from '@homeModels/cap';

@Injectable({
  providedIn: 'root'
})
export class CapDataService {
    getAllCaps(requestOptions?: RequestOptions): Observable<Page<Cap.Simple>> {
        return Http.get<Page<Cap.Simple>>(`/caps`, requestOptions)
            .pipe(map(resp => {
                resp.content = plainToInstance(Cap.Simple, resp.content);
                return resp;
            }));
    }

    getCapDetail(accountId: string): Observable<Cap.Details> {
        return Http.get(`/caps/${accountId}`)
            .pipe(map(resp => plainToInstance(Cap.Details, resp)));
    }

    createCap(body: FormGroupRawValue<Cap.Create>): Observable<any> {
        return Http.post(`/caps`, body)
    }

    updateCap(accountId: string, body: FormGroupRawValue<Cap.Update>): Observable<any> {
        return Http.put(`/caps/${accountId}`, body)
    }

    deleteCap(accountId: string): Observable<any> {
        return Http.delete(`/caps/${accountId}`);
    }

    putCapGroups(accountId: string, body: FormGroupRawValue<Cap.AddCapGroupsForm>): Observable<null> {
        return Http.put(`/caps/${accountId}/cap-groups`, body);
    }
}
