import {Subject} from 'rxjs';
import {CapDataService} from './cap-data.service';

export const CapDataServiceHarness: CapDataService = {
    getAllCaps() { return new Subject(); },

    getCapDetail() { return new Subject(); },

    createCap() { return new Subject(); },

    updateCap() { return new Subject(); },

    deleteCap() { return new Subject(); },

    putCapGroups() { return new Subject(); }
}
