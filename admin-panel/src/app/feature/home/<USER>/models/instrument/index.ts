import {ChangeInstrumentShortSellStateForm} from './change-instrument-short-sell-state-form';
import {CreateInstrumentForm} from './create-instrument-form';
import {InstrumentChangeGroupForm} from './instrument-change-group-form';
import {InstrumentChangeStateForm} from './instrument-change-state-form';
import {InstrumentDynamicData} from './instrument-dynamic-data';
import {InstrumentOrderSpec} from './instrument-order-spec';
import {InstrumentUpdateSpec} from './instrument-update-spec';
import {InstrumentSecuritySpec} from './instrument-security-spec';
import {InstrumentSimple} from './instrument-simple';
import {InstrumentSingle} from './instrument-single';
import {SchedulableInstrumentState} from './schedulable-instrument-state';

export * from './instrument-base';
export * from './instrument-state';
export * from './following-group-instrument';
export * from './not-following-group-instrument';
export * from './schedulable-instrument';

export namespace Instrument {
    export type ChangeStateForm = InstrumentChangeStateForm;

    export type ChangeShortSellStateForm = ChangeInstrumentShortSellStateForm;

    export type CreateForm = CreateInstrumentForm;

    export type ChangeGroupForm = InstrumentChangeGroupForm;

    export type  DynamicData = InstrumentDynamicData;
    export const DynamicData = InstrumentDynamicData;

    export type  UpdateSpec = InstrumentUpdateSpec;
    export const UpdateSpec = InstrumentUpdateSpec;

    export type  OrderSpec = InstrumentOrderSpec;
    export const OrderSpec = InstrumentOrderSpec;

    export type  SecuritySpec = InstrumentSecuritySpec;
    export const SecuritySpec = InstrumentSecuritySpec;

    export type  Simple = InstrumentSimple;
    export const Simple = InstrumentSimple;

    export type  Single = InstrumentSingle;
    export const Single = InstrumentSingle;

    export type  SchedulableState = SchedulableInstrumentState;
    export const SchedulableState = SchedulableInstrumentState;
}
