import {Getter} from '../../../../../shared/plain-to-instance/getter';
import {Excel} from '../../../../../shared/to-excel-columns/excel';
import {Setter} from '../../../../../shared/plain-to-instance/setter';
import {Schedulable} from '@models/schedule-template/schedulable';
import {Group} from '@models/group';
import {InstrumentBase} from './instrument-base';
import {StoreService} from '@shared/services/store.service';
import {Instrument} from '@homeModels/instrument/index';
import {Type} from '@shared/plain-to-instance/type';

@Getter
@Excel({fileName: 'InstrumentsSimple'})
export class InstrumentSimple extends InstrumentBase {
    readonly type = Schedulable.Type.SECURITY; // TODO: remove this line when Schedulable is fixed

    readonly productId: string;

    readonly isFollowingGroup: boolean;

    @Type(InstrumentSimple)
    readonly ghostView: InstrumentSimple;

    @Setter
    readonly groupCode: string;

    get group(): Group { return StoreService.groupsObj[this.groupCode]};

    update(instrument: Instrument.Simple): void {
        if (!instrument) {
            return;
        }

        for (const prop in instrument) {
            if (this.hasOwnProperty(prop)) {
                this[prop] = instrument[prop];
            }
        }
    }
}
