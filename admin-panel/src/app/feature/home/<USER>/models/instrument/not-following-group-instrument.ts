import {Getter} from '../../../../../shared/plain-to-instance/getter';
import {Excel} from '../../../../../shared/to-excel-columns/excel';
import {Type} from '@shared/plain-to-instance/type';
import {ExcelColumns} from '../../../../../shared/to-excel-columns/excel-columns';
import {Product} from '@homeModels/product';
import {PriceBandPercentage} from '@models/price-band-percentage';
import {Group} from '@models/group';
import {InstrumentBase} from './instrument-base';

@Getter
@Excel({fileName: 'NotFollowingGroupInstrument'})
export class NotFollowingGroupInstrument extends InstrumentBase {
    @Type(Product.Simple)
    @ExcelColumns({title: 'Product Id', valuePath: 'product.productId'})
    readonly product: Product.Simple;

    @Type(PriceBandPercentage)
    @ExcelColumns(
        {title: 'Lower Bound Percentage', valuePath: 'priceBandPercentage.lowerBoundPercentage'},
        {title: 'Upper Bound Percentage', valuePath: 'priceBandPercentage.upperBoundPercentage'}
    )
    readonly priceBandPercentage: PriceBandPercentage;

    @Type(Group)
    @ExcelColumns({title: 'Group Code', valuePath: 'group.code'})
    readonly group: Group;
}
