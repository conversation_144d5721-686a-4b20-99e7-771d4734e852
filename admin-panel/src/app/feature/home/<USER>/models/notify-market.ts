import {Getter} from '../../../../shared/plain-to-instance/getter';
import {Excel} from '../../../../shared/to-excel-columns/excel';
import {FormControl} from '@angular/forms';
import {MutableAny} from '@models/form-group-raw-value';
import {SelectOptionFormField} from '@modules/dynamic-form/form-fields/select-option-field/select-option-form-field';
import {TextAreaFormField} from '@modules/dynamic-form/form-fields/text-area-field/text-area-form-field';

export namespace NotifyMarket {

    @Getter
    @Excel({fileName: 'notifyMarketTemplate'})
    export class Template {
        readonly title: string;

        readonly message: string;

        readonly messageNature: MessageNature;

        readonly addressType: AddressType;
    }

    @Getter
    export class Message extends Template {
        readonly messageMarket: string;
    }

    export class CreateTemplate implements MutableAny<Template> {
        title: FormControl<string>;
        message: FormControl<string>;
        messageNature: FormControl<MessageNature>;
        addressType: FormControl<AddressType>;
    }

    export class CreateMessage implements MutableAny<Message> {
        template: FormControl<SelectOptionFormField<Template>>;
        title: FormControl<string>;
        message: FormControl<TextAreaFormField>;
        messageNature: FormControl<MessageNature>;
        addressType: FormControl<AddressType>;
        messageMarket: FormControl<string>;
    }

    export enum MessageNature {
        MARKET = 'B',
        TECHNICAL = 'T',
        MARKET_AND_TECHNICAL = 'R'
    }

    export enum AddressType {
        BROKERS = 'TO',
        DATA_VENDORS = 'FI',
        BROKERS_AND_DATA_VENDORS = 'FT'
    }
}
