import {Getter} from '../../../../shared/plain-to-instance/getter';
import {FormControl} from '@angular/forms';
import {Excel} from '../../../../shared/to-excel-columns/excel';

@Getter
@Excel({fileName: 'origins'})
export class Origin {
    readonly id: string;

    readonly type: string;

    readonly priority: number;
}

export namespace Origin {
    export interface UpdatePriorityForm {
        newPriority: FormControl<number>;
    }
}
