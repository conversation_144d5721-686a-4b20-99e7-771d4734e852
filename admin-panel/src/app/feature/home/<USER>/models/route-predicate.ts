import {RouteTree} from './route-tree';
import {Getter} from '../../../../shared/plain-to-instance/getter';
import {FormControl} from '@angular/forms';

@Getter
export class RoutePredicate {
    readonly title: string;

    readonly routeTreeType: RouteTree.Type;

    readonly position: number;

    readonly length: number;
}

export namespace RoutePredicate {
    export class Create {
        title: FormControl<string>;
        routeTreeType: FormControl<string>;
        position: FormControl<string>;
        length: FormControl<string>;
    }

    export class Update extends Create {}
}
