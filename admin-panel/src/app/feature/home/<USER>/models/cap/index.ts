import {CapSimple} from './cap-simple';
import {CapCreate} from './cap-create';
import {CapDetails} from './cap-details';
import {CapAddCapForm} from './cap-add-cap';
import {CapAddCapGroupsForm} from './cap-add-cap-groups';
import {CapRouteBindingEntry} from './cap-route-binding-entry';
import {CapRouteNodeBindings} from './cap-route-node-bindings';

export namespace Cap {
    export type  Simple = CapSimple;
    export const Simple = CapSimple;

    export type  RouteBindingEntry = CapRouteBindingEntry;
    export const RouteBindingEntry = CapRouteBindingEntry;

    export type RouteNodeBindings = CapRouteNodeBindings;

    export type  Details = CapDetails;
    export const Details = CapDetails;

    export type AddCapForm = CapAddCapForm;

    export type AddCapGroupsForm = CapAddCapGroupsForm;

    export type Create = CapCreate;

    export class Update extends CapCreate {}
}
