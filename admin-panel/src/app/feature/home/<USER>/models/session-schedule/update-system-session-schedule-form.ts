import {Getter} from "../../../../../shared/plain-to-instance/getter";
import {FormArray, FormGroup} from "@angular/forms";
import {TemplateAction} from "@models/schedule-template/template-action";
import {States} from '@enums/states';
import System = States.System;

@Getter
export class UpdateSystemSessionScheduleForm {
    readonly actions: FormArray<FormGroup<TemplateAction.CreateForm<System>>>;
}
