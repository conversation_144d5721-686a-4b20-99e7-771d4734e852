import {Getter} from '../../../../../shared/plain-to-instance/getter';
import {Group} from '@models/group';
import {Transform} from '@shared/plain-to-instance/transform';
import {mapToGroup} from '@core/utils';
import {Type} from '@shared/plain-to-instance/type';
import {GroupStateTemplateAction} from '@models/schedule-template/group-state-template-action';

@Getter
export class GroupSessionSchedule {
    readonly date: string;

    @Type(GroupStateTemplateAction)
    readonly actions: GroupStateTemplateAction[];

    @Transform(mapToGroup)
    readonly group: Group;
}
