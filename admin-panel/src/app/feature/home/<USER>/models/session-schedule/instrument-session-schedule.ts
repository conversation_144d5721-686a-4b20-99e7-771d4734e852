import {Getter} from '../../../../../shared/plain-to-instance/getter';
import {Transform} from '@shared/plain-to-instance/transform';
import {mapToInstrument} from '@core/utils';
import {Type} from '@shared/plain-to-instance/type';
import {GroupStateTemplateAction} from '@models/schedule-template/group-state-template-action';
import {Instrument} from '@homeModels/instrument';

@Getter
export class InstrumentSessionSchedule {
    readonly date: string;

    @Type(GroupStateTemplateAction)
    readonly actions: GroupStateTemplateAction[];

    @Transform(mapToInstrument, 'securityId')
    readonly security: Instrument.Single;
}
