import {GroupSessionSchedule} from "@homeModels/session-schedule/group-session-schedule";
import {
    CreateBatchGroupSessionScheduleForm
} from "@homeModels/session-schedule/create-batch-group-session-schedule-form";
import {SessionScheduleFormDialog} from "@homeModels/session-schedule/session-schedule-form-dialog";
import {InstrumentSessionSchedule} from "@homeModels/session-schedule/instrument-session-schedule";
import {
    CreateInstrumentSessionScheduleForm
} from '@homeModels/session-schedule/create-instrument-session-schedule-form';
import {SystemSessionSchedule} from '@homeModels/session-schedule/system-session-schedule';
import {UpdateSystemSessionScheduleForm} from '@homeModels/session-schedule/update-system-session-schedule-form';

export namespace SessionSchedule {
    export type Group = GroupSessionSchedule;
    export const Group = GroupSessionSchedule;

    export type CreateGroupBatchForm = CreateBatchGroupSessionScheduleForm;

    export type UpdateSystemForm = UpdateSystemSessionScheduleForm;

    export const GroupScheduleFormDialog = SessionScheduleFormDialog<GroupSessionSchedule>;

    export const SystemScheduleFormDialog = SessionScheduleFormDialog<SystemSessionSchedule>;

    export type Instrument = InstrumentSessionSchedule;
    export const Instrument = InstrumentSessionSchedule;

    export type CreateInstrumentForm = CreateInstrumentSessionScheduleForm;

    export type System = SystemSessionSchedule;
    export const System = SystemSessionSchedule;
}
