import {Getter} from '../../../../../shared/plain-to-instance/getter';
import {Type} from '@shared/plain-to-instance/type';
import {RouteTreeValue} from './route-tree-value';
import {RouteTreeType} from './route-tree-type';

@Getter
export class RouteTreeBase {
    readonly nodeCode?: string;

    @Type(RouteTreeValue)
    readonly value?: RouteTreeValue;

    readonly name: string;

    readonly description: string;

    readonly affectedCapGroups: string[];

    readonly nodeType?: RouteTreeType;
}
