import {RouteTreeType} from './route-tree-type';
import {RouteTreeSimple} from './route-tree-simple';
import {RouteTreeUpdate} from './route-tree-update';
import {RouteTreeCreateNodeForm} from './route-tree-create-node-form';
import {RouteTreeValidRoot} from './route-tree-valid-root';
import {RouteTreeNode} from './route-tree-node';
import {RouteTreeLock} from '@homeModels/route-tree/route-tree-lock';
import {RouteTreeLocks} from '@homeModels/route-tree/route-tree-locks';

export namespace RouteTree {
    export type  Type = RouteTreeType;
    export const Type = RouteTreeType;

    export type  Simple = RouteTreeSimple;
    export const Simple = RouteTreeSimple;

    export type  Node = RouteTreeNode;

    export type Update = RouteTreeUpdate;

    export type ValidRoot = RouteTreeValidRoot;

    export type CreateNodeForm = RouteTreeCreateNodeForm;

    export type Lock = RouteTreeLock;

    export type Locks = RouteTreeLocks;
}
