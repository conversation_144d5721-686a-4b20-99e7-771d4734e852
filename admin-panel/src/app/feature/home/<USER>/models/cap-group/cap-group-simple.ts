import {Getter} from '../../../../../shared/plain-to-instance/getter';
import {Type} from '@shared/plain-to-instance/type';
import {Cap} from '@homeModels/cap';

@Getter
export class CapGroupSimple {
    readonly capGroupName: string;

    @Type(CapGroupSimple)
    readonly subGroups: CapGroupSimple[];

    readonly routeNodeBindings: Cap.RouteNodeBindings;

    @Type(Cap.Simple)
    readonly caps?: Cap.Simple[];
}
