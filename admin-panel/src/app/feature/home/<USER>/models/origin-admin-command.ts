import {TableDataProvider} from '@models/table-representation/table-representable';
import {SimpleTableData} from '@models/table-representation/simple-table-representation';
import {TranslateKey} from '@enums/translate-key';
import {Getter} from '../../../../shared/plain-to-instance/getter';

export namespace OriginAdminCommands {
    @Getter
    export class UpdatePriority extends TableDataProvider {
        readonly originId: string;

        readonly newPriority: string;

        getTableData(): SimpleTableData[] {
            return [{
                data: [
                    {
                        title: TranslateKey.originId,
                        value: this.originId
                    },
                    {
                        title: TranslateKey.priority,
                        value: this.newPriority
                    }
                ]
            }];
        }
    }
}
