import {Name} from '@shared/models/name';
import {Type} from '@shared/plain-to-instance/type';
import {Company} from '@models/company';
import {GhostStatus} from '@models/ghost-status';
import {Board} from '@models/board';
import {ProductType} from '@models/product-type';
import {ProductSubType} from '@models/product-sub-type';
import {Getter} from '@shared/plain-to-instance/getter';
import {Excel, ExcelColumns, ExcludeFromExcel} from '@shared/to-excel-columns';
import {Setter} from '@shared/plain-to-instance/setter';

@Getter
@Excel({fileName: 'products'})
export class ProductSimple {
    readonly productId: string;

    readonly ghostStatus: GhostStatus;

    @Type(Company)
    @ExcelColumns({title: 'Company Code', valuePath: 'company.code'})
    readonly company: Company;

    readonly boardCode: string;

    @Setter
    @ExcludeFromExcel
    board: Board;

    readonly productTypeCode: string;

    @Setter
    @ExcludeFromExcel
    productType: ProductType;

    readonly productSubTypeCode: string;

    @Setter
    @ExcludeFromExcel
    productSubType: ProductSubType;

    readonly marketFlowCode: string;

    readonly referencePrice: string;

    readonly totalShares: number;

    readonly maxOwnershipByShareholder: number;

    readonly adjustedTotalShares: number;

    readonly creationDate: string;

    readonly updateDate: string;

    @Type(ProductSimple)
    readonly ghostView: ProductSimple;

    @Type(Name)
    @ExcelColumns({title: 'EN name', valuePath: 'name.en'}, {title: 'FA name', valuePath: 'name.fa'})
    readonly name: Name;

    @Type(Name)
    @ExcelColumns({title: 'EN mnemonic', valuePath: 'mnemonic.en'}, {title: 'FA mnemonic', valuePath: 'mnemonic.fa'})
    readonly mnemonic: Name;

    readonly productCode: string;

    readonly tradingStartDate: string;

    readonly tradingEndDate: string;

    readonly maturityDate: string;

    @Setter
    availableForSell: boolean;

    readonly isPricePercentage: boolean;

    @Setter
    @ExcludeFromExcel
    searchExpression?: string;
}
