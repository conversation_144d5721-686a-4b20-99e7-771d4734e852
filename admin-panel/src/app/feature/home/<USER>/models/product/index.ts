import {ProductSpec} from './product-spec';
import {ProductSimple} from './product-simple';
import {ProductSingle} from './product-single';
import {ProductInstrument} from './product-instrument';
import {CreateProductForm} from './create-product-form';
import {UpdateProductForm} from './update-product-form';
import {ProductInstrumentForm} from './product-instrument-form';

export * from './product-spec';
export * from './product-simple';
export * from './product-single';
export * from './product-instrument';
export * from './create-product-form';
export * from './update-product-form';
export * from './product-instrument-form';

export namespace Product {
    export type  Simple = ProductSimple;
    export const Simple = ProductSimple;

    export type  Details = ProductSingle;
    export const Details = ProductSingle;

    export type CreateForm = CreateProductForm;

    export type InstrumentForm = ProductInstrumentForm;

    export type UpdateForm = UpdateProductForm;

    export type Instrument = ProductInstrument;

    export type Spec = ProductSpec;
}
