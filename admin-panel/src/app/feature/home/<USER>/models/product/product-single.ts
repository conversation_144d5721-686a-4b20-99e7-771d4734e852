import {Name} from '@shared/models/name';
import {Type} from '@shared/plain-to-instance/type';
import {Company} from '@models/company';
import {Getter} from '../../../../../shared/plain-to-instance/getter';
import {OrderPreference} from '@enums/order-preference';
import {GhostStatus} from '@models/ghost-status';
import {Nationality} from '@enums/nationality';
import {InvestorType} from '@enums/investor-type';


class OwnershipByNationality implements Record<Nationality, string> {
    NATIONAL: string;
    FOREIGNER: string;
}

class OwnershipByInvestorType implements Record<InvestorType, string> {
    INDIVIDUAL: string;
    NON_INDIVIDUAL: string;
    INDIVIDUAL_BOARD_DIRECTOR: string;
}

@Getter
export class ProductSingle {
    readonly productId: string;

    readonly ghostStatus: GhostStatus;

    @Type(Company)
    readonly company: Company;

    readonly boardCode: string;

    readonly boardName: string;

    readonly productTypeCode: string;

    readonly productTypeName: string;

    readonly productSubTypeCode: string;

    readonly productSubTypeName: string;

    readonly marketFlowCode: string;

    readonly marketFlowName: string;

    readonly parValue: string;

    readonly referencePrice: string;

    readonly totalShares: number;

    readonly maxOwnershipByShareholder: number;

    readonly adjustedTotalShares: number;

    readonly creationDate: string;

    readonly updateDate: string;

    @Type(Name)
    readonly name: Name;

    @Type(Name)
    readonly mnemonic: Name;

    readonly productCode: string;

    readonly matchingType: OrderPreference;

    readonly issuePrice: string;

    readonly tradingStartDate: string;

    readonly tradingEndDate: string;

    readonly maturityDate: string;

    readonly normalBlockSize: number;

    readonly strikePrice: number;

    readonly underlyingProductId: string;

    readonly availableForSell: boolean;

    readonly isPricePercentage: boolean;

    readonly currentOwnershipByNationality: OwnershipByNationality;

    readonly currentOwnershipByInvestorType: OwnershipByInvestorType;

    readonly maxOwnershipByNationality: OwnershipByNationality;

    readonly maxOwnershipByInvestorType: OwnershipByInvestorType;
}
