import {TestBed} from '@angular/core/testing';
import {InvestorSearchDetailsComponent} from './investor-search-details.component';
import {TranslateTestingModule} from '../../../../../../translate-testing.module';
import {SharedModule} from '../../../../../shared/shared.module';
import {SearchBoxService} from '../search-box.service';
import {Investor} from '@models/investor';
import {TestUtils} from '@test/test-utils';
import {ComponentHarness} from '@test/harness/component-harness';
import {ModalModule} from 'ngx-bootstrap/modal';
import {SharedDeclarations} from '@modules/shared-declarations/shared-declarations';

describe('InvestorSearchDetailsComponent', () => {
    let investor: Investor;
    let component: InvestorSearchDetailsComponent;
    let ha: ComponentHarness<InvestorSearchDetailsComponent>;
    let searchBoxService: SearchBoxService<Investor>;

    beforeEach(() => {
        ha = new ComponentHarness(InvestorSearchDetailsComponent, {
            declarations: [InvestorSearchDetailsComponent],
            imports: [
                TranslateTestingModule,
                SharedModule,
                SharedDeclarations,
                ModalModule.forRoot(),
            ],
            detectChanges: false
        });

        searchBoxService = TestBed.inject(SearchBoxService);
        investor = TestUtils.getInvestor();

        component = ha.component;

        searchBoxService.selectedItem.next(investor);
    });

    it('should create', () => {
        ha.detectChanges();

        expect(component).toBeTruthy();
    });

    it('should unsubscribe subjects when component is destroyed', () => {
        ha.detectChanges();

        expect(searchBoxService.selectedItem.observers.length).toEqual(1);

        component.ngOnDestroy();
        expect(searchBoxService.selectedItem.observers.length).toEqual(0);
    });

    it('should display shareholderIds', () => {
        ha.detectChanges();

        ha.get('details').nativeElement['open'] = true;

        expect(ha.get('ul')).toHaveContained(investor.shareholderIds[0]);
        expect(ha.get('ul')).toHaveContained(investor.shareholderIds[1]);
    });

    it('should get valid value of isBuySideBlocked property', () => {
        ha.detectChanges();
        expect(ha.component.isBuySideBlocked).toBeFalse();

        ha.component.isBuySideBlocked = true;
        expect(ha.component.isBuySideBlocked).toBeTrue();

        ha.component.isBuySideBlocked = false;
        expect(ha.component.isBuySideBlocked).toBeFalse();
    });

    it('should get valid value of isSellSideBlocked property', () => {
        ha.detectChanges();
        expect(ha.component.isSellSideBlocked).toBeFalse();

        ha.component.isSellSideBlocked = true;
        expect(ha.component.isSellSideBlocked).toBeTrue();

        ha.component.isSellSideBlocked = false;
        expect(ha.component.isSellSideBlocked).toBeFalse();
    });
});
