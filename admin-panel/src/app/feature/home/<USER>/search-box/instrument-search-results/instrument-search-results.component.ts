import {ChangeDetectionStrategy, Component, OnDestroy, OnInit} from '@angular/core';
import {SearchResults} from '../search-results';
import {IterableSearchItem} from '../iterable-search-item';
import {StoreService} from '@services/store.service';
import {LanguageLayout} from '@enums/language-layout';
import {Instrument} from '@homeModels/instrument';

class InstrumentWithSearchExpression extends Instrument.Simple implements IterableSearchItem {
    searchExpression: string;
}

@Component({
    selector: 'app-instrument-search-results',
    exportAs: 'instrumentSearchResults',
    templateUrl: './instrument-search-results.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class InstrumentSearchResultsComponent extends SearchResults<InstrumentWithSearchExpression> implements OnInit, OnDestroy {
    private _instruments: InstrumentWithSearchExpression[];

    ngOnInit(): void {
        this._setSearchExpressionOnInstruments();
        this._subscribeOnSearchStr();
    }

    ngOnDestroy(): void {
        this._onDestroy.next();
        this._onDestroy.complete();
    }

    override _getFilterData(searchStr: string): void {
        this._filteredItems = this._sortInstrumentsByPriority(this._filterInstruments(searchStr), searchStr);
        this._changeDetectorRef.detectChanges();
    }

    private _setSearchExpressionOnInstruments(): void {
        this._instruments = StoreService.instruments.map((instrument: InstrumentWithSearchExpression) => {
            instrument.searchExpression = (instrument.mnemonic.fa
                + instrument.mnemonic.en
                + instrument.securityId).toLowerCase();

            return instrument;
        }) as InstrumentWithSearchExpression[];
    }

    private _filterInstruments(searchStr: string): InstrumentWithSearchExpression[] {
        return this._instruments.filter(instrument => instrument.searchExpression.includes(searchStr));
    }

    private _sortInstrumentsByPriority(instruments: InstrumentWithSearchExpression[], searchStr: string): InstrumentWithSearchExpression[] {
        const symbolList = [];
        const persianSymbolList = [];
        const others = [];

        instruments.forEach((instrument: InstrumentWithSearchExpression) => {
            if (instrument.mnemonic?.fa.includes(searchStr)) {
                persianSymbolList.push(instrument);
            } else if (this._isSecurityIdIncludes(instrument, searchStr)) {
                symbolList.push(instrument);
            } else {
                others.push(instrument);
            }
        });

        this._sortArrayByPriority(persianSymbolList, LanguageLayout.fa, searchStr);
        this._sortArrayByPriority(symbolList, LanguageLayout.en, searchStr);
        return [...persianSymbolList, ...symbolList, ...others];
    }

    private _isSecurityIdIncludes(instrument: InstrumentWithSearchExpression, searchStr: string): boolean {
        return instrument.securityId?.toLowerCase().includes(searchStr);
    }

    private _sortArrayByPriority(array: Instrument.Single[], lang: LanguageLayout, searchStr: string): void {
        array.sort((a, b) => {
            const aIndex = a.mnemonic[lang].search(searchStr);
            const bIndex = b.mnemonic[lang].search(searchStr);
            const aLength = a.mnemonic[lang].length;
            const bLength = b.mnemonic[lang].length;

            // Sort by the position of the searchStr in the text (eg. Symbol, Company Name)
            if (aIndex < bIndex) {
                return -1
            } else if (aIndex > bIndex) {
                return 1
            }

            // Sort by the text (eg. Symbol, Company Name) length if the position of the
            // searchStr in it was the same
            if (aLength < bLength) {
                return -1
            } else if (aLength > bLength) {
                return 1
            }

            // If the position of the searchStr in the text and the length of text were equal
            return 0;
        })
    }
}
