import {ChangeDetectionStrategy, Component, OnDestroy, OnInit} from '@angular/core';
import {SearchResults} from '../search-results';
import {forkJoin} from 'rxjs';
import {ShareholderDataService} from '@dataServices/shareholder-data.service';
import {Shareholder} from '@models/shareholder';
import {Page} from '@models/page';
import {ShareholderWithPositions} from '../shareholder-with-positions';
import {takeUntil} from 'rxjs/operators';
import {BsModalService} from 'ngx-bootstrap/modal';
import {Patterns} from '@constants/patterns';

@Component({
    selector: 'app-shareholder-search-results',
    exportAs: 'shareholderSearchResults',
    templateUrl: './shareholder-search-results.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class ShareholderSearchResultsComponent extends SearchResults<ShareholderWithPositions> implements OnInit, OnDestroy {
    private _searchStr: string;

    constructor(
        private _modelService: BsModalService,
        private _shareholderDataService: ShareholderDataService
    ) {
        super();
    }

    ngOnInit(): void {
        this._subscribeOnSearchStr();
        this._subscribeOnModalClosed();
    }

    ngOnDestroy(): void {
        this._onDestroy.next();
        this._onDestroy.complete();
        this._searchStr = null;
    }

    override _getFilterData(searchStr: string): void {
        if (!searchStr || !searchStr.match(Patterns.SHAREHOLDER_MASK)) {
            this._filteredItems = [];
            this._changeDetectorRef.detectChanges();
            return;
        }

        this._searchStr = searchStr;

        this._getShareholderData();
    }

    private _onFetchingShareholderDataSuccess([shareholder, positions]: [Shareholder, Page<Shareholder.Position>]): void {
        this._filteredItems = [{shareholder, positions: positions.content}];
        this.selectItem(0);
    }

    private _onFetchingShareholderDataFailure(): void {
        this._filteredItems = [];
    }

    private _getShareholderData(): void {
        forkJoin([
            this._shareholderDataService.getShareholder(this._searchStr, {hasLocalErrorHandler: true}),
            this._shareholderDataService.positions(this._searchStr, {hasLocalErrorHandler: true})
        ])
            .pipe(takeUntil(this._onDestroy))
            .subscribe({
                next: this._onFetchingShareholderDataSuccess.bind(this),
                error: this._onFetchingShareholderDataFailure.bind(this)
            });
    }

    private _subscribeOnModalClosed(): void {
        this._modelService.onHide
            .pipe(takeUntil(this._onDestroy))
            .subscribe(() => {
                if (this._selectedItem.shareholder) {
                    this._getShareholderData();
                }
            });
    }
}
