import {TestBed} from '@angular/core/testing';
import {ShareholderSearchResultsComponent} from './shareholder-search-results.component';
import {TranslateTestingModule} from '../../../../../../translate-testing.module';
import {SearchBoxService} from '../search-box.service';
import {ShareholderWithPositions} from '../shareholder-with-positions';
import {ShareholderDataService} from '@dataServices/shareholder-data.service';
import {of, throwError} from 'rxjs';
import {TestUtils} from '@test/test-utils';
import {Page} from '@models/page';
import {SearchResultsComponent} from '../search-results/search-results.component';
import {ComponentHarness} from '@test/harness/component-harness';
import {SharedModule} from '../../../../../shared/shared.module';
import {BsModalService, ModalModule} from 'ngx-bootstrap/modal';

describe('ShareholderSearchResultsComponent', () => {
    let ha: ComponentHarness<ShareholderSearchResultsComponent>;
    let component: ShareholderSearchResultsComponent;
    let searchBoxService: SearchBoxService<ShareholderWithPositions>;
    let shareholderDataService: ShareholderDataService;

    beforeEach(async () => {
        ha = new ComponentHarness(ShareholderSearchResultsComponent, {
            declarations: [
                ShareholderSearchResultsComponent,
                SearchResultsComponent
            ],
            imports: [
                TranslateTestingModule,
                ModalModule.forRoot(),
                SharedModule
            ],
            providers: [BsModalService],
            detectChanges: false
        });

        searchBoxService = TestBed.inject(SearchBoxService);
        shareholderDataService = TestBed.inject(ShareholderDataService);
        component = ha.component;
    });

    it('should create', () => {
        ha.detectChanges();

        expect(component).toBeTruthy();
    });

    it('should subscribe to searchStr changes', () => {
        spyOn(component, '_getFilterData');
        ha.detectChanges();

        const searchExpression = 'test';
        searchBoxService.searchStr.next(searchExpression);
        expect(component._getFilterData).toHaveBeenCalledWith(searchExpression);
    });

    it('should subscribe to selectedItem changes', () => {
        spyOn(searchBoxService.selectedItem, 'next');
        ha.detectChanges();

        const selectedItem = null;
        searchBoxService.selectedItem.next(selectedItem);
        expect(searchBoxService.selectedItem.next).toHaveBeenCalledWith(selectedItem);
    });

    it('should unsubscribe subjects when component is destroyed', () => {
        ha.fixture.detectChanges();
        expect(searchBoxService.searchStr.observers.length).toEqual(1);
        expect(searchBoxService.selectedItem.observers.length).toEqual(1);

        component.ngOnDestroy();
        expect(searchBoxService.searchStr.observers.length).toEqual(0);
        expect(searchBoxService.selectedItem.observers.length).toEqual(0);
    });

    it('should select search result item by clicking on it', () => {
        initComponent();
        searchBoxService.searchStr.next('33193391771011');

        ha.get('.dropdown-item').thatContains('33193391771011').click();
        expect(ha.get('.dropdown-item').thatContains('33193391771011')).toHaveClass('active');
    });

    it('should display list of search results', () => {
        initComponent();
        searchBoxService.searchStr.next('33193391771011');

        expect(ha.get('.dropdown-item').thatContains('33193391771011')).toBeExists();
    });

    it('title of search results should be shareholderId', () => {
        initComponent();
        searchBoxService.searchStr.next('33193391771011');

        expect(ha.get('.dropdown-item').thatContains('33193391771011')).toHaveContained('33193391771011');
    });

    it('If shareholder is not found, the length of filteredItems should be zero', () => {
        spyOn(shareholderDataService, 'getShareholder').and.returnValue(throwError({}));
        spyOn(shareholderDataService, 'positions').and.returnValue(throwError({}));
        ha.fixture.detectChanges();

        searchBoxService.searchStr.next('33193391771011');
        expect(component.filteredItems.length).toEqual(0);
    });

    function initComponent(): void {
        const shareholder = TestUtils.getShareholder();
        (shareholder as any)._shareholderId = '33193391771011';
        spyOn(shareholderDataService, 'getShareholder').and.returnValue(of(shareholder))
        spyOn(shareholderDataService, 'positions').and.returnValue(of(new Page([TestUtils.getPositions()])));
        ha.fixture.detectChanges();
    }
});
