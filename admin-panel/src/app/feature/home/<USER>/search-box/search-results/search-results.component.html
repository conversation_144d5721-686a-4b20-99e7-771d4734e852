<div class="search-results-list">
    <div class="dropdown-item search-result-item text-nowrap text-truncate"
         *ngFor="let item of searchResults.filteredItems; let i = index"
         [class.active]="searchResults.isSelectedItem(item)"
         (click)="searchResults.selectItem(i)">

        <ng-container
            [ngTemplateOutlet]="searchResult"
            [ngTemplateOutletContext]="{searchResult: item}"></ng-container>
    </div>
</div>
