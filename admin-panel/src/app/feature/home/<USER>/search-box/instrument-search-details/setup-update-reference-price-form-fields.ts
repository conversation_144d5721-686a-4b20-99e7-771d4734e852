import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {Validators} from '@angular/forms';
import {CustomValidators} from '@constants/custom-validators';
import {InputMasks} from '@constants/input-masks';
import {FieldsPlaceholder} from '../../../../../shared/constants/fields-placeholder';
import {FormFields} from '@modules/dynamic-form/form-field';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';
import {SelectOptionFormField} from '@modules/dynamic-form/form-fields/select-option-field/select-option-form-field';

export function setupUpdateReferencePriceFormFields(referencePrice: string): FormFields  {
    return [
        new InputFormField({
            formControlName: 'referencePrice',
            label: TranslateKey.referencePrice,
            width: 12,
            validations: [Validators.required, CustomValidators.greaterThan(0), Validators.pattern(InputMasks.PRICE.pattern)],
            mask: InputMasks.FLOATING_NUMBER,
            placeholder: FieldsPlaceholder.DECIMAL_NUMBER,
            value: referencePrice
        }),

        new SelectOptionFormField({
            formControlName: 'shouldUpdateClosingPrice',
            label: TranslateKey.shouldUpdateClosingPrice,
            width: 12,
            validations: [Validators.required],
            options: [true, false],
            getTitle(data): string { return data.toString(); },
            value: true
        })
    ];
}
