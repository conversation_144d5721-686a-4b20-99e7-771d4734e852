import {ComponentFixture, TestBed} from '@angular/core/testing';
import {InstrumentSearchDetailsComponent} from './instrument-search-details.component';
import {TranslateTestingModule} from '../../../../../../translate-testing.module';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {OpenInstrumentInfoDialogDirective} from '@directives/open-instrument-info-dialog.directive';
import {BigNumberPipe} from '@pipes/big-number.pipe';
import {OpenInsertOrderDialogDirective} from '@directives/open-insert-order-dialog.directive';

describe('InstrumentSearchDetailsComponent', () => {
    let component: InstrumentSearchDetailsComponent;
    let fixture: ComponentFixture<InstrumentSearchDetailsComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [
                OpenInsertOrderDialogDirective,
                InstrumentSearchDetailsComponent,
                OpenInstrumentInfoDialogDirective,
                BigNumberPipe
            ],
            imports: [
                TranslateTestingModule
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA]
        })
            .compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(InstrumentSearchDetailsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
