<!-- Instrument symbol title -->
<div class="d-flex justify-content-between">
    <div class="d-flex align-items-center">
        <app-change-instrument-state
            *ngIf="!isCreated"
            class="position-relative d-inline-block"
            [instrumentDynamic]="instrumentDynamic">
        </app-change-instrument-state>

        <h5 class="font-weight-bold d-inline-block mb-0">{{ selectedInstrument.mnemonic | selectByLanguage }}</h5>
    </div>

    <div class="d-flex align-items-center" style="margin: 0 -20px">
        <mat-icon
            style="scale: .9"
            *ngIf="isNotPersisted"
            color="warn"
            [title]="selectedInstrument.ghostStatus | lowercase | translate">warning
        </mat-icon>
        <h6 class="font-weight-bold text-muted m-0 p-0">{{ selectedInstrument.securityId }}</h6>
        <button
            mat-icon-button
            [matMenuTriggerFor]="menu">
            <mat-icon>more_vert</mat-icon>
        </button>
        <mat-menu #menu="matMenu">
            <!-- Instant Edit Btn -->
            <button
                mat-menu-item
                enableIfTradingSession
                (click)="openUpdateImmediateInstrumentDialog()"
                [disabled]="isCreated">
                <mat-icon>electric_bolt</mat-icon>
                {{ translateKeys.immediateEdit | translate }}
            </button>
            <!-- Edit Btn -->
            <button
                mat-menu-item
                enableIfTradingSession
                (click)="openUpdateInstrumentDialog()"
                [disabled]="isNotPersisted">
                <mat-icon>edit</mat-icon>
                {{ translateKeys.edit | translate }}
            </button>
            <!-- Delete Btn -->
            <button
                *hasPermission="permissions.DELETE_INSTRUMENT_PERMISSION"
                mat-menu-item
                enableIfTradingSession
                (confirm)="deleteInstrument()"
                [disabled]="isNotPersisted">
                <mat-icon>delete</mat-icon>
                {{ translateKeys.delete | translate }}
            </button>
        </mat-menu>
    </div>
</div>

<!-- Instrument info table -->
<small>
    <div class="row">
        <div class="col-6 search-details-table-row">
            <span>{{ translateKeys.group | translate }}</span>
            &nbsp; &nbsp;
            <strong>{{ selectedInstrument.group?.code }}</strong>
        </div>
        <div class="col-6 search-details-table-row">
            <span>{{ translateKeys.market | translate }}</span>
            &nbsp; &nbsp;
            <strong>{{ selectedInstrument.market }}</strong>
        </div>

        <div class="col-6 search-details-table-row"
             *ngFor="let col of instrumentInfoColumns">
            <span>{{ col | translate }}</span>
            &nbsp; &nbsp;
            <strong
                [title]="selectedInstrument[col] | bigNumber:true">{{ selectedInstrument[col] | bigNumber }}</strong>
        </div>
    </div>
</small>

<!-- More button -->
<div class="d-flex justify-content-center pt-2">
    <div tabindex="0"
         class="badge badge-secondary badge-pill font-weight-normal px-3 py-1 cursor-pointer position-relative"
         #openInstrumentInfoDialog="openInstrumentInfoDialog"
         [openInstrumentInfoDialog]="selectedInstrument"
         (shortKeypress)="openInstrumentInfoDialog.onClick()"
         shortKeyHint="1">
        {{ translateKeys.more | translate }}
    </div>
</div>
