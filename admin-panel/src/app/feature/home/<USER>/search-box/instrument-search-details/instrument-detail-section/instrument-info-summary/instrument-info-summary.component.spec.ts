import {TestBed} from '@angular/core/testing';
import {InstrumentInfoSummaryComponent} from './instrument-info-summary.component';
import {BsModalService} from 'ngx-bootstrap/modal';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {BigNumberPipe} from '@pipes/big-number.pipe';
import {KeyHandlerService} from '@services/key-handler.service';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {Http} from '@http';
import {HttpClient} from '@angular/common/http';
import {TestUtils} from '@test/test-utils';
import {RouterTestingModule} from '@angular/router/testing';
import {SharedModule} from '../../../../../../../shared/shared.module';
import {HttpClientTestingModule} from '@angular/common/http/testing';
import {TranslateTestingModule} from '../../../../../../../../translate-testing.module';
import {OpenInstrumentInfoDialogDirective} from '@directives/open-instrument-info-dialog.directive';
import {ComponentHarness} from '@test/harness/component-harness';
import {TableGroupDialogComponent} from '../../../../../dialogs/table-group-dialog/table-group-dialog.component';
import {InstrumentDataService} from '@dataServices/instrument-data.service';
import {instrumentDataServiceHarness} from '@dataServices/instrument-data.service.harness';
import {of} from 'rxjs';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {HasPermissionService} from '@directives/has-permission.service';
import {hasPermissionServiceHarness} from '@directives/has-permission.service.harness';

describe('InstrumentInfoSummaryComponent', () => {
    let ha: ComponentHarness<InstrumentInfoSummaryComponent>;
    let modalService: BsModalService;

    beforeEach(() => {
        ha = new ComponentHarness(InstrumentInfoSummaryComponent, {
            declarations: [
                OpenInstrumentInfoDialogDirective,
                InstrumentInfoSummaryComponent,
                TableGroupDialogComponent,
                SelectByLanguagePipe,
                BigNumberPipe
            ],
            imports: [
                BrowserAnimationsModule,
                HttpClientTestingModule,
                TranslateTestingModule,
                RouterTestingModule,
                SharedModule
            ],
            providers: [
                BsModalService,
                KeyHandlerService,
                SelectByLanguagePipe,
                {provide: HasPermissionService, useValue: hasPermissionServiceHarness},
                {provide: InstrumentDataService, useValue: instrumentDataServiceHarness}
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
            detectChanges: false
        });

        Http.httpClient = TestBed.inject(HttpClient);
        modalService = TestBed.inject(BsModalService);

        ha.detectChanges();
    });

    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });

    it('should display symbol title', () => {
        // Given
        ha.component.instrument = TestUtils.getInstrument() as any; // TODO: remove any
        (ha.component as any)._changeDetectorRef.detectChanges();
        // Then
        expect(ha.get('h5')).toHaveContained(TestUtils.getInstrument().mnemonic.en);
    });

    it('should open update instrument dialog when clicking on edit instrument button', () => {
        spyOn(modalService, 'show');
        ha.detectChanges();

        ha.get('button.mat-mdc-menu-trigger').click();
        ha.get('button').thatContains('Edit').click();
        expect(modalService.show).toHaveBeenCalled();
    });

    it('should call #deleteInstrument function when clicking on delete instrument button', () => {
        spyOn(instrumentDataServiceHarness, 'deleteInstrument').and.returnValue(of(null));

        ha.get('button.mat-mdc-menu-trigger').click();
        ha.get('button').thatContains('Delete').confirm();
        expect(instrumentDataServiceHarness.deleteInstrument).toHaveBeenCalled();
    });
});
