import {ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, <PERSON><PERSON><PERSON>, On<PERSON>estroy, OnInit} from '@angular/core';
import {Router} from '@angular/router';
import {KeyHandlerService} from '@services/key-handler.service';
import {BsModalService} from 'ngx-bootstrap/modal';
import {CreateTradeDialogComponent} from '../../../../dialogs/create-trade-dialog/create-trade-dialog.component';
import {InstrumentDialog} from '@models/instrument-dialog';
import {TranslateKey} from '@enums/translate-key';
import {RoutingLayout} from '@constants/routing-layout';
import {Subject} from 'rxjs';
import {StoreService} from '@services/store.service';
import {InstrumentDataService} from '@dataServices/instrument-data.service';
import {takeUntil} from 'rxjs/operators';
import {ChangeDetectionService} from '@services/change-detection.service';
import {GhostStatus} from '@models/ghost-status';
import {InstrumentSearchDetailsService} from '../instrument-search-details.service';
import {setupUpdateReferencePriceFormFields} from '../setup-update-reference-price-form-fields';
import {setupUpdatePercentageStaticThresholdFormFields} from '../setup-update-percentage-static-threshold-form-fields';
import {DynamicFormDialogService} from '../../../../dialogs/dynamic-form-dialog/dynamic-form-dialog.service';
import {FormFields} from '@modules/dynamic-form/form-field';
import {groupFormFields} from '../../../../pages/instruments/group-form-fields';
import {FormGroup} from '@angular/forms';
import {AdminCommand} from '@models/admin-command';
import {AdminCommandCallback} from '@models/admin-command-callback';
import {Permissions} from '../../../../../../shared/constants/permissions.constant';
import {setupUpdateStaticPriceFormFields} from '../setup-update-static-price-form-fields';
import {getInstrumentById} from '@core/utils';
import {Instrument} from '@homeModels/instrument';
import {HomeRoutes} from '../../../../shared/constants/home-routing.constants';

@Component({
    selector: 'app-instrument-detail-section',
    templateUrl: './instrument-detail-section.component.html',
    styleUrls: [
        './instrument-detail-section.component.scss',
        '../../search-details.component.scss'
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class InstrumentDetailSectionComponent implements OnInit, OnDestroy {
    readonly translateKeys = TranslateKey;
    readonly routeLayout = HomeRoutes;
    readonly permissions = Permissions;

    private _instrument = new Instrument.Single();

    get instrument(): Instrument.Single { return this._instrument; }

    @Input()
    set instrument(value: Instrument.Single) { this._updateInstrument(value.securityId); }

    get isCreated(): boolean {
        return this._instrument.ghostStatus === GhostStatus.CREATED;
    }

    get orderBookPageUrl(): string {
        return this._getRouterLink(HomeRoutes.ORDER_BOOK);
    }

    get tradesPageUrl(): string {
        return this._getRouterLink(HomeRoutes.TRADES_INQUIRY);
    }

    get bestLimitsPageUrl(): string {
        return this._getRouterLink(HomeRoutes.BEST_LIMITS);
    }

    private _onDestroy = new Subject();

    constructor(
        private _router: Router,
        private _zone: NgZone,
        private _modalService: BsModalService,
        private _changeDetectorRef: ChangeDetectorRef,
        private _instrumentDataService: InstrumentDataService,
        private _dynamicFormDialogService: DynamicFormDialogService,
        private _instrumentSearchDetailsService: InstrumentSearchDetailsService
    ) { }

    ngOnInit(): void {
        this._modalService.onHide
            .pipe(takeUntil(this._onDestroy))
            .subscribe(KeyHandlerService.enable);

        ChangeDetectionService.onChange
            .pipe(takeUntil(this._onDestroy))
            .subscribe(options => {
                if (options.type === 'INSTRUMENT') {
                    this._updateInstrument(options.data);
                }
            });
    }

    ngOnDestroy(): void {
        this._onDestroy.next();
        this._onDestroy.complete();
    }

    changeRoute(page: string): void {
        this._zone.run(() =>
            this._router
                .navigate([`${RoutingLayout.HOME}/${page}`, this.instrument.securityId])).then();
    }

    openCreateTradeDialog(): void {
        KeyHandlerService.disable();

        this._modalService.show(CreateTradeDialogComponent, {
            initialState: new InstrumentDialog(this.instrument),
            class: 'modal-lg'
        })
    }

    openUpdateGroupDialog(): void {
        const formFields: FormFields<Instrument.ChangeGroupForm> = groupFormFields(this.instrument);

        this._dynamicFormDialogService.open({
            formTitle: TranslateKey.changeGroup,
            formFields,
            onSubmit: (formGroup) => this._updateGroup(this.instrument.securityId, formGroup)
        });
    }

    purgeOrderBySecurityId(): void {
        this._instrumentDataService
            .purgeOrders(this.instrument.securityId, {})
            .subscribe();
    }


    fetchReferencePriceAndOpenUpdateDialog(): void {
        this._instrumentDataService
            .getDynamicData(this.instrument.securityId)
            .subscribe(resp => {
                this._openUpdateReferencePriceDialog(resp.referencePrice);
            });
    }

    openUpdatePercentagePriceBandDialog(): void {
        KeyHandlerService.disable();

        this._dynamicFormDialogService.open({
            formTitle: TranslateKey.updatePercentagePriceBand,
            formFields: setupUpdatePercentageStaticThresholdFormFields(this._instrument.priceBandPercentage),
            onSubmit: (formGroup) =>
                this._instrumentSearchDetailsService
                    .updatePercentagePriceBand(this._instrument.securityId, formGroup)
        });
    }

    openSetPriceBandDialog(): void {
        KeyHandlerService.disable();

        this._dynamicFormDialogService.open({
            formTitle: TranslateKey.staticPriceBand,
            formFields: setupUpdateStaticPriceFormFields(this._instrument.staticPriceBandLowerBound, this._instrument.staticPriceBandUpperBound),
            onSubmit: (formGroup) =>
                this._instrumentSearchDetailsService
                    .updateStaticPriceBand(this._instrument.securityId, formGroup)
        });
    }

    private _updateGroup(securityId: string, formGroup: FormGroup): void {
        this._instrumentDataService.updateInstrumentGroup(securityId, formGroup.getRawValue()).subscribe((resp) => {
            this._modalService.hide();
            const adminCommandCallback = new AdminCommandCallback(
                resp.commandId, this._updateInstrumentInStoreService.bind(this)
            );
            StoreService.adminCommandCallBacks.push(adminCommandCallback);
        });
    }

    private _updateInstrumentInStoreService(adminCommand: AdminCommand): void {
        if (adminCommand.isSuccess) {
            ChangeDetectionService.onChange.next({type: 'INSTRUMENT', data: this.instrument.securityId});
        }
    }

    private _updateInstrument(securityId: string): void {
        this._instrumentDataService
            .getStaticData(securityId)
            .subscribe(resp => {
                getInstrumentById(securityId).update(resp);
                this._instrument = resp;
                this._changeDetectorRef.detectChanges();
            });
    }

    private _getRouterLink(routingLayout: RoutingLayout): string {
        return `/${RoutingLayout.HOME}/${routingLayout}/${this.instrument.securityId}`;
    }

    private _openUpdateReferencePriceDialog(referencePrice: string): void {
        KeyHandlerService.disable();

        this._dynamicFormDialogService.open({
            formTitle: TranslateKey.updateReferencePrice,
            formFields: setupUpdateReferencePriceFormFields(referencePrice),
            onSubmit: (formGroup) =>
                this._instrumentSearchDetailsService
                    .updateReferencePrice(this._instrument.securityId, formGroup)
        });
    }
}
