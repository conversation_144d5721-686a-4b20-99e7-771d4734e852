<div class="py-3 px-4 rounded transparent-box">
    <app-instrument-info-summary
        [instrument]="instrument"></app-instrument-info-summary>
</div>

<div class="my-3 d-flex justify-content-between" *ngIf="!isCreated">
    <!-- Insert Order -->
    <button
        *hasPermission="permissions.INSERT_ORDER_PERMISSION"
        mat-button
        enableIfTradingSession
        class="transparent-box search-box-main-button"
        #openInsertOrderDialog="openInsertOrderDialog"
        [openInsertOrderDialog]="instrument"
        (shortKeypress)="openInsertOrderDialog.onClick()"
        shortKeyHint="2">
        <div class="d-flex flex-column align-items-center">
            <mat-icon class="mb-2">note_add</mat-icon>
            <small>{{translateKeys.insertOrder | translate}}</small>
        </div>
    </button>

    <!-- Order Book -->
    <a
        mat-button
        class="transparent-box search-box-main-button text-decoration-none"
        [routerLink]="orderBookPageUrl"
        (shortKeypress)="changeRoute(routeLayout.ORDER_BOOK)"
        shortKeyHint="3">
        <div class="d-flex flex-column align-items-center">
            <mat-icon class="mb-2">file_copy</mat-icon>
            <small>{{translateKeys.orderBook | translate}}</small>
        </div>
    </a>

    <!-- Insert Trade -->
    <button
        *hasPermission="permissions.CREATE_TRADE_PERMISSION"
        mat-button
        enableIfTradingSession
        class="transparent-box search-box-main-button"
        (click)="openCreateTradeDialog()"
        (shortKeypress)="openCreateTradeDialog()"
        shortKeyHint="5">
        <div class="d-flex flex-column align-items-center">
            <mat-icon class="mb-2">difference</mat-icon>
            <small>{{translateKeys.createTrade | translate}}</small>
        </div>
    </button>

    <!-- Trades Inquiry -->
    <a
        mat-button
        class="transparent-box search-box-main-button text-decoration-none"
        [routerLink]="tradesPageUrl"
        (shortKeypress)="changeRoute(routeLayout.TRADES_INQUIRY)"
        shortKeyHint="4">
        <div class="d-flex flex-column align-items-center">
            <mat-icon class="mb-2">archive</mat-icon>
            <small>{{translateKeys.trades | translate}}</small>
        </div>
    </a>
</div>

<div class="mb-1 overflow-auto" style="height: 150px" *ngIf="!isCreated">
    <!-- Best Limits -->
    <a
        mat-button
        class="d-flex w-100 text-decoration-none"
        [routerLink]="bestLimitsPageUrl"
        (shortKeypress)="changeRoute(routeLayout.BEST_LIMITS)"
        shortKeyHint="4">
        <mat-icon class="mb-2">view_list</mat-icon>
        <span>{{translateKeys.bestLimits | translate}}</span>
    </a>

    <!-- Change Group -->
    <button
        *hasPermission="permissions.CHANGE_GROUP_PERMISSION"
        mat-button
        class="d-flex w-100"
        disableIfPostSession
        (click)="openUpdateGroupDialog()">
        <mat-icon>edit</mat-icon>
        {{translateKeys.changeGroup| translate}}
    </button>


    <!-- Change Reference Price -->
    <button
        mat-button
        class="d-flex w-100"
        enableIfTradingSession
        (click)="fetchReferencePriceAndOpenUpdateDialog()">
        <mat-icon>money</mat-icon>
        {{translateKeys.updateReferencePrice| translate}}
    </button>

    <!-- Change Percentage Price Band -->
    <button
        mat-button
        class="d-flex w-100"
        enableIfTradingSession
        (click)="openUpdatePercentagePriceBandDialog()">
        <mat-icon>price_change</mat-icon>
        {{translateKeys.updatePercentagePriceBand | translate}}
    </button>

    <!-- Change Absolute Price Band -->
    <button
        mat-button
        class="d-flex w-100"
        enableIfTradingSession
        (click)="openSetPriceBandDialog()">
        <mat-icon>price_change</mat-icon>
        {{translateKeys.updateStaticPriceBand | translate}}
    </button>

    <!-- Purge Order -->
    <button
        mat-button
        class="d-flex w-100"
        enableIfTradingSession
        (confirm)="purgeOrderBySecurityId()">
        <mat-icon>auto_delete</mat-icon>
        {{translateKeys.purgeOrders | translate}}
    </button>
</div>
