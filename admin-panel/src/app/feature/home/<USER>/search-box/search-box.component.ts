import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    EventEmitter,
    OnDestroy,
    OnInit,
    Output,
    ViewChild
} from '@angular/core';
import {filter, takeUntil} from 'rxjs/operators';
import {Subject} from 'rxjs';
import {KeyHandlerService} from '@services/key-handler.service';
import {ARROW_DOWN, ARROW_UP, ESCAPE} from 'mb-keycode';
import {NavigationEnd, Router} from '@angular/router';
import {TranslateKey} from '@enums/translate-key';
import {animate, state, style, transition, trigger} from '@angular/animations';
import {SearchFlags} from './search-flags';
import {SearchBoxService} from './search-box.service';

const INITIAL_STATE = 'initial';
const FINAL_STATE = 'final';

@Component({
    selector: 'app-search-box',
    templateUrl: './search-box.component.html',
    styleUrls: ['./search-box.component.scss'],
    host: {style: 'display: contents'},
    changeDetection: ChangeDetectionStrategy.OnPush,
    animations: [
        trigger('balloonEffect', [
            state(INITIAL_STATE, style({
                width: '384px',
                height: '56px',
                opacity: 0
            })),
            state(FINAL_STATE, style({
                width: '750px',
                height: '625px',
                opacity: 1
            })),
            transition(`${FINAL_STATE}=>${INITIAL_STATE}`, animate('100ms')),
            transition(`${INITIAL_STATE}=>${FINAL_STATE}`, animate('150ms'))
        ])
    ],
    standalone: false
})
export class SearchBoxComponent implements OnInit, OnDestroy {
    readonly translateKeys = TranslateKey;

    private _searchFlags = new SearchFlags();
    get searchFlags(): SearchFlags { return this._searchFlags; }

    private _onDestroy = new Subject();

    @ViewChild('inputElement')
    private _inputElement: ElementRef<HTMLInputElement>;

    private _searchBoxAnimationState = INITIAL_STATE;
    get searchBoxAnimationState(): string {
        return this._searchBoxAnimationState;
    }

    private _searchExpression: string;
    get searchExpression(): string { return this._searchExpression; }
    set searchExpression(value: string) {
        this._searchExpression = value;
        this._searchBoxService.searchStr.next(value.toLowerCase());
    }

    get isNotFound(): boolean {
        return (!this.getFirstItemOfSearchResult() && !!this._searchExpression);
    }

    @Output() close: EventEmitter<null> = new EventEmitter<null>();

    constructor(
        private _searchBoxService: SearchBoxService<any>,
        private _keyHandlerService: KeyHandlerService,
        private _changeDetectorRef: ChangeDetectorRef,
        private _router: Router
    ) {}

    ngOnInit(): void {
        this.initializeSearchFlags();

        this._addKeyEventListener();
        this._subscribeToRouteChangesForClosingSearchBox();
        this._subscribeToSelectedItemForApplyingChangeDetection();
        this._selectFirstItemOfSearchResult();
    }

    ngOnDestroy(): void {
        this._onDestroy.next(null);
        this._onDestroy.complete();
    }

    onInputFocus(): void {
        this._searchBoxAnimationState = FINAL_STATE;
        this._toggleBlurApplicationBody('blur(4px)');
    }

    closeSearchBox(): void {
        this._searchBoxAnimationState = INITIAL_STATE;
        this._toggleBlurApplicationBody('none');
        this._changeDetectorRef.detectChanges();

        setTimeout(() => {
            this.close.emit();
            this._inputElement.nativeElement.blur();
            this.initializeSearchFlags();
            this._toggleBlurApplicationBody('none');
            this._selectFirstItemOfSearchResult();
            this._scrollSearchResultToTop();
        }, 100);

        setTimeout(() => {
            this.searchExpression = '';
            this._selectFirstItemOfSearchResult();
        }, 1000);
    }

    onSearchInputChange(): void {
        this._scrollSearchResultToTop();
        this._selectFirstItemOfSearchResult();
    }

    onChangeSearchFlag(): void {
        this._searchBoxService.selectedItem.next(null);
        this.onSearchInputChange();
    }

    getFirstItemOfSearchResult(): HTMLDivElement {
        return document.querySelector<HTMLDivElement>('.search-results-list > .dropdown-item');
    }

    initializeSearchFlags(): void {
        const DEFAULT_SELECTED_FLAG = this._searchFlags.items[0];
        this._searchFlags.select(DEFAULT_SELECTED_FLAG);
    }

    removeSearchExpression(): void {
        this.searchExpression = '';
        this.initializeSearchFlags();
        this._scrollSearchResultToTop();

        setTimeout(() => {
            this._selectFirstItemOfSearchResult();
        }, 100);
    }

    private _subscribeToRouteChangesForClosingSearchBox(): void {
        this._router.events
            .pipe(filter(event => event instanceof NavigationEnd))
            .subscribe(() => {
                if (this.searchBoxAnimationState === FINAL_STATE) {
                    this.closeSearchBox();
                }
            });
    }

    private _addKeyEventListener(): void {
        this._keyHandlerService
            .addItem('closeSearchBox', [ESCAPE])
            .pipe(takeUntil(this._onDestroy))
            .subscribe(this.closeSearchBox.bind(this));

        this._keyHandlerService
            .addItem('selectNextItem', [ARROW_DOWN])
            .pipe(takeUntil(this._onDestroy))
            .subscribe(() => this._selectSearchResultItemByRelativeIndex(1));

        this._keyHandlerService
            .addItem('selectPreviousItem', [ARROW_UP])
            .pipe(takeUntil(this._onDestroy))
            .subscribe(() => this._selectSearchResultItemByRelativeIndex(-1))
    }

    private _selectSearchResultItemByRelativeIndex(relativeIndex: number): void {
        const searchResults: any[] = document.querySelectorAll('.search-results-list > .dropdown-item') as any;
        let currentIndex = relativeIndex === 1 ? -1 : 0;
        searchResults.forEach((item, i) => {
            if (item.className.includes('active')) {
                currentIndex = i;
            }
        });
        let selectedItemIndex = currentIndex + relativeIndex;
        if (selectedItemIndex < 0) {
            selectedItemIndex = searchResults.length - 1; // wrap around to the last item
        } else if (currentIndex + relativeIndex >= searchResults.length) {
            selectedItemIndex = 0; // wrap around to the first item
        }

        searchResults[selectedItemIndex].click();
        setTimeout(() => this._scrollResultList(), 50);
    }

    private _scrollResultList(): void {
        const resultList = document.querySelector('.search-results-list');
        const resultListPosition = resultList.getBoundingClientRect();
        const selectedElm = document.querySelector('.search-result-item.active');
        const selectedElmPosition = selectedElm.getBoundingClientRect();

        if (resultListPosition.bottom < selectedElmPosition.bottom) {
            resultList.scrollTop += selectedElmPosition.bottom - resultListPosition.bottom;
        } else if (resultListPosition.top > selectedElmPosition.top) {
            resultList.scrollTop += selectedElmPosition.top - resultListPosition.top;
        }
    }

    private _selectFirstItemOfSearchResult(): void {
        const firstItemOfSearchResult = this.getFirstItemOfSearchResult();
        if (firstItemOfSearchResult) {
            firstItemOfSearchResult.click();
        } else {
            this._searchBoxService.selectedItem.next(null);
        }
    }

    private _subscribeToSelectedItemForApplyingChangeDetection(): void {
        this._searchBoxService.selectedItem
            .subscribe(() => {
                this._changeDetectorRef.detectChanges()
            });
    }

    private _toggleBlurApplicationBody(value: string): void {
        (document.querySelector<HTMLDivElement>('app-page-layout > div')?.style
            || ({} as CSSStyleDeclaration)).filter = value;
    }

    private _scrollSearchResultToTop(): void {
        (document.querySelector('.search-results-list') || {} as any).scrollTop = 0;
    }
}
