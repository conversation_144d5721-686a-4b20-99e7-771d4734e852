import {TestBed} from '@angular/core/testing';
import {BrokerSearchResultsComponent} from './broker-search-results.component';
import {TranslateTestingModule} from '../../../../../../translate-testing.module';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {SearchBoxService} from '../search-box.service';
import {SearchResultsComponent} from '../search-results/search-results.component';
import {TestUtils} from '@test/test-utils';
import {StoreService} from '@shared/services/store.service';
import {ComponentHarness} from '@test/harness/component-harness';
import {LangService} from '@shared/services/lang.service';
import {Broker} from '@homeModels/broker';

describe('BrokerSearchResultsComponent', () => {
    let component: BrokerSearchResultsComponent;
    let harness: ComponentHarness<BrokerSearchResultsComponent>;
    let searchBoxService: SearchBoxService<Broker.Simple>;

    beforeEach(() => {
        harness = new ComponentHarness(BrokerSearchResultsComponent, {
            declarations: [
                BrokerSearchResultsComponent,
                SearchResultsComponent,
                SelectByLanguagePipe
            ],
            imports: [
                TranslateTestingModule
            ],
            detectChanges: false
        });

        TestBed.runInInjectionContext(() => {
            const langService = new LangService();
        });
        searchBoxService = TestBed.inject(SearchBoxService);
        StoreService.brokers = TestUtils.getBrokers().content;

        component = harness.fixture.componentInstance;
    });

    it('should create', () => {
        harness.detectChanges();
        expect(component).toBeTruthy();
    });

    it('should subscribe to searchStr changes', () => {
        spyOn(component, '_getFilterData');
        harness.fixture.detectChanges();

        const searchExpression = 'test';
        searchBoxService.searchStr.next(searchExpression);
        expect(component._getFilterData).toHaveBeenCalledWith(searchExpression);
    });

    it('should subscribe to selectedItem changes', () => {
        spyOn(searchBoxService.selectedItem, 'next');
        harness.fixture.detectChanges();

        const selectedItem = null;
        searchBoxService.selectedItem.next(selectedItem);
        expect(searchBoxService.selectedItem.next).toHaveBeenCalledWith(selectedItem);
    });

    it('should unsubscribe subjects when component is destroyed', () => {
        harness.fixture.detectChanges();
        expect(searchBoxService.searchStr.observers.length).toEqual(1);
        expect(searchBoxService.selectedItem.observers.length).toEqual(1);

        component.ngOnDestroy();
        expect(searchBoxService.searchStr.observers.length).toEqual(0);
        expect(searchBoxService.selectedItem.observers.length).toEqual(0);
    });

    it('title of search results should be group name', () => {
        harness.fixture.detectChanges();

        searchBoxService.searchStr.next('110');
        expect(harness.get('.dropdown-item').thatContains('شركت كارگزاري جهان سهم')).toBeExists();
    });

    it('If broker is not found, the length of filteredItems should be zero', () => {
        harness.fixture.detectChanges();

        searchBoxService.searchStr.next('1234567890');
        expect(component.filteredItems.length).toEqual(0);
    });
});
