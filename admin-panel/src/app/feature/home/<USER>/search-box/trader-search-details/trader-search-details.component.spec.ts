import {TraderSearchDetailsComponent} from './trader-search-details.component';
import {ComponentHarness} from '@test/harness/component-harness';
import {SearchBoxService} from '../search-box.service';
import {TranslateTestingModule} from '../../../../../../translate-testing.module';
import {RouterTestingModule} from '@angular/router/testing';
import {SharedModule} from '../../../../../shared/shared.module';
import {TestUtils} from '@test/test-utils';
import {Trader} from '@models/trader';
import {TraderDataService} from '@dataServices/trader-data.service';
import {of} from 'rxjs';
import {searchBoxServiceHarness} from '../search-box.service.harness';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {traderDataServiceHarness} from '@dataServices/trader-data.service.harness';
import {SearchBoxComponent} from '../search-box.component';
import {ChangeDetectorRef} from '@angular/core';
import {Router} from '@angular/router';
import {KeyHandlerService} from '@services/key-handler.service';
import {ModalModule} from 'ngx-bootstrap/modal';
import {AdminCommand} from '@models/admin-command';
import {HasPermissionService} from '@directives/has-permission.service';
import {hasPermissionServiceHarness} from '@directives/has-permission.service.harness';

describe('TraderSearchDetailsComponent', () => {
    let component: TraderSearchDetailsComponent;
    let ha: ComponentHarness<TraderSearchDetailsComponent>;
    let trader: Trader;

    beforeEach(() => {
        ha = new ComponentHarness(TraderSearchDetailsComponent, {
            declarations: [
                TraderSearchDetailsComponent,
                SelectByLanguagePipe
            ],
            imports: [
                TranslateTestingModule,
                RouterTestingModule,
                ModalModule.forRoot(),
                SharedModule
            ],
            providers: [
                Router,
                ChangeDetectorRef,
                KeyHandlerService,
                SearchBoxComponent,
                {provide: SearchBoxService, useValue: searchBoxServiceHarness},
                {provide: TraderDataService, useValue: traderDataServiceHarness},
                {provide: HasPermissionService, useValue: hasPermissionServiceHarness}
            ],
            detectChanges: false
        });

        trader = TestUtils.getTrader();
        (trader as any).traderId = '11298001';
        (trader as any).broker = TestUtils.getBrokers().content[0];

        const deleteAdminCommand = jasmine.createSpyObj<AdminCommand>({
            commandId: 1,
        })

        spyOn(traderDataServiceHarness, 'deleteTrader').and.returnValue(of(deleteAdminCommand))

        component = ha.component;
        searchBoxServiceHarness.selectedItem.next(trader);
        ha.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should display traderId', () => {
        expect(ha.get('h6').thatContains('11298001')).toBeExists();
    });

    it('should call #deleteTrader function when Delete Trader button is clicked', () => {
        spyOn(searchBoxServiceHarness.selectedItem, 'next');
        spyOn(searchBoxServiceHarness.searchStr, 'next');

        ha.detectChanges();

        ha.get('button').thatContains('Delete Trader').confirm();

        expect(traderDataServiceHarness.deleteTrader).toHaveBeenCalled();
        expect(searchBoxServiceHarness.selectedItem.next).toHaveBeenCalled();
        expect(searchBoxServiceHarness.searchStr.next).toHaveBeenCalled();
    });
});
