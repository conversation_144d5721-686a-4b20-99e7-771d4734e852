import {Component, Injector, OnInit} from '@angular/core';
import {SearchDetails} from '../search-details';
import {Trader} from '@models/trader';
import {TraderDataService} from '@dataServices/trader-data.service';
import {SearchBoxComponent} from '../search-box.component';
import {StoreService} from '@shared/services/store.service';
import {plainToInstance} from '../../../../../shared/plain-to-instance/plain-to-instance';
import {AdminCommandCallback} from '@models/admin-command-callback';
import {ChangeDetectionService} from '@services/change-detection.service';

@Component({
    selector: 'app-trader-search-details',
    templateUrl: './trader-search-details.component.html',
    standalone: false
})
export class TraderSearchDetailsComponent extends SearchDetails<Trader> implements OnInit {

    constructor(
        private _injector: Injector,
        private _traderDataService: TraderDataService
    ) {
        super();
    }

    ngOnInit() {
        this._onSearchItemChange();
    }

    deleteTrader(): void {
        this._traderDataService.deleteTrader(this._selectedItem.traderId)
            .subscribe((resp) => {
                const searchBoxComponent: SearchBoxComponent = this._injector.get(SearchBoxComponent);
                this._searchBoxService.selectedItem.next(new Trader());
                this._searchBoxService.searchStr.next('');
                searchBoxComponent.getFirstItemOfSearchResult()?.click();
                searchBoxComponent.searchExpression = '';

                const adminCommandCallBack = new AdminCommandCallback(resp.commandId, this._updateTraders.bind(this));
                StoreService.adminCommandCallBacks.push(adminCommandCallBack);
            });
    }

    private _updateTraders(): void {
        this._traderDataService
            .getAllTraders()
            .subscribe(resp => {
                StoreService.traders = plainToInstance(Trader, resp.content);
                StoreService.traders.forEach(item => StoreService.tradersObj[item.traderId] = item);
                ChangeDetectionService.onChange.next({type: 'TRADERS'});
            })
    }

    purgeOrderByTraderId(): void {
        this._traderDataService
            .purgeOrders(this.selectedItem.traderId, {})
            .subscribe();
    }
}
