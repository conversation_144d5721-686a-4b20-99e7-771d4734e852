import {Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {SearchDetails} from '../search-details';
import {InstrumentSearchDetailsComponent} from '../instrument-search-details/instrument-search-details.component';
import {GroupSearchDetailsComponent} from '../group-search-details/group-search-details.component';
import {BrokerSearchDetailsComponent} from '../broker-search-details/broker-search-details.component';
import {InvestorSearchDetailsComponent} from '../investor-search-details/investor-search-details.component';
import {ShareholderSearchDetailsComponent} from '../shareholder-search-details/shareholder-search-details.component';
import {Schedulable} from '@models/schedule-template/schedulable';
import {TraderSearchDetailsComponent} from '../trader-search-details/trader-search-details.component';

@Component({
    selector: 'app-all-search-details',
    templateUrl: './all-search-details.component.html',
    standalone: false
})
export class AllSearchDetailsComponent extends SearchDetails<any> implements OnInit, On<PERSON><PERSON>roy {
    get detailComponent() {
        if (this.selectedItem?.type === Schedulable.Type.SECURITY) {
            return InstrumentSearchDetailsComponent;
        } else if (this.selectedItem?.type === Schedulable.Type.GROUP) {
            return GroupSearchDetailsComponent;
        } else if (this.selectedItem?.id) {
            return BrokerSearchDetailsComponent;
        } else if (this.selectedItem?.shareholderIds) {
            return InvestorSearchDetailsComponent;
        } else if (this.selectedItem?.shareholder) {
            return ShareholderSearchDetailsComponent;
        } else if (this.selectedItem?.traderId) {
            return TraderSearchDetailsComponent
        }

        return null;
    }

    ngOnInit(): void {
        this._onSearchItemChange();
    }

    ngOnDestroy(): void {
        this._onDestroy.next();
        this._onDestroy.complete();
    }
}
