import {TestBed} from '@angular/core/testing';
import {InvestorSearchResultsComponent} from './investor-search-results.component';
import {TranslateTestingModule} from '../../../../../../translate-testing.module';
import {SearchBoxService} from '../search-box.service';
import {Investor} from '@models/investor';
import {SearchResultsComponent} from '../search-results/search-results.component';
import {of, throwError} from 'rxjs';
import {InvestorDataService} from '@dataServices/investor-data.service';
import {TestUtils} from '@test/test-utils';
import {ComponentHarness} from '@test/harness/component-harness';

const INVESTOR_ID = '1234567890';

describe('InvestorSearchResultsComponent', () => {
    let component: InvestorSearchResultsComponent;
    let ha: ComponentHarness<InvestorSearchResultsComponent>;
    let searchBoxService: SearchBoxService<Investor>;
    let investorDataService: InvestorDataService;

    beforeEach(() => {
        ha = new ComponentHarness(InvestorSearchResultsComponent, {
            declarations: [
                InvestorSearchResultsComponent,
                SearchResultsComponent
            ],
            imports: [
                TranslateTestingModule
            ],
            detectChanges: false
        });

        searchBoxService = TestBed.inject(SearchBoxService);
        investorDataService = TestBed.inject(InvestorDataService);

        component = ha.component;
    });

    it('should create', () => {
        ha.detectChanges();
        expect(component).toBeTruthy();
    });

    it('should subscribe to searchStr changes', () => {
        spyOn(component, '_getFilterData');
        ha.fixture.detectChanges();

        const searchExpression = 'test';
        searchBoxService.searchStr.next(searchExpression);
        expect(component._getFilterData).toHaveBeenCalledWith(searchExpression);
    });

    it('should subscribe to selectedItem changes', () => {
        spyOn(searchBoxService.selectedItem, 'next');
        ha.fixture.detectChanges();

        const selectedItem = null;
        searchBoxService.selectedItem.next(selectedItem);
        expect(searchBoxService.selectedItem.next).toHaveBeenCalledWith(selectedItem);
    });

    it('title of search results should be investorId', () => {
        initComponent();
        searchBoxService.searchStr.next(INVESTOR_ID);

        expect(ha.get('.dropdown-item').thatContains(INVESTOR_ID)).toBeExists();
    });

    it('If investorId is not found, the length of filteredItems should be zero', () => {
        spyOn(investorDataService, 'getInvestor').and.returnValue(throwError({}));
        ha.fixture.detectChanges();

        searchBoxService.searchStr.next(INVESTOR_ID);
        expect(component.filteredItems.length).toEqual(0);
    });

    function initComponent(): void {
        const investor = TestUtils.getInvestor();
        investor.investorId = INVESTOR_ID;
        spyOn(investorDataService, 'getInvestor').and.returnValue(of(investor))
        ha.fixture.detectChanges();
    }
});
