import {TestBed} from '@angular/core/testing';
import {GroupSearchResultsComponent} from './group-search-results.component';
import {TranslateTestingModule} from '../../../../../../translate-testing.module';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {SearchBoxService} from '../search-box.service';
import {Group} from '@models/group';
import {SearchResultsComponent} from '../search-results/search-results.component';
import {StoreService} from '@shared/services/store.service';
import {TestUtils} from '@test/test-utils';
import {ComponentHarness} from '@test/harness/component-harness';

describe('GroupSearchResultsComponent', () => {
    let component: GroupSearchResultsComponent;
    let ha: ComponentHarness<GroupSearchResultsComponent>;
    let searchBoxService: SearchBoxService<Group>;

    beforeEach(() => {
        ha = new ComponentHarness(GroupSearchResultsComponent, {
            declarations: [
                GroupSearchResultsComponent,
                SearchResultsComponent,
                SelectByLanguagePipe
            ],
            imports: [
                TranslateTestingModule
            ],
            detectChanges: false
        });

        searchBoxService = TestBed.inject(SearchBoxService);
        StoreService.groups = TestUtils.getGroups().content;

        component = ha.fixture.componentInstance;
    });

    it('should create', () => {
        ha.detectChanges();
        expect(component).toBeTruthy();
    });

    it('should subscribe to searchStr changes', () => {
        spyOn(component, '_getFilterData');
        ha.fixture.detectChanges();

        const searchExpression = 'test';
        searchBoxService.searchStr.next(searchExpression);
        expect(component._getFilterData).toHaveBeenCalledWith(searchExpression);
    });

    it('should subscribe to selectedItem changes', () => {
        spyOn(searchBoxService.selectedItem, 'next');
        ha.fixture.detectChanges();

        const selectedItem = null;
        searchBoxService.selectedItem.next(selectedItem);
        expect(searchBoxService.selectedItem.next).toHaveBeenCalledWith(selectedItem);
    });

    it('should unsubscribe subjects when component is destroyed', () => {
        ha.fixture.detectChanges();
        expect(searchBoxService.searchStr.observers.length).toEqual(1);
        expect(searchBoxService.selectedItem.observers.length).toEqual(1);

        component.ngOnDestroy();
        expect(searchBoxService.searchStr.observers.length).toEqual(0);
        expect(searchBoxService.selectedItem.observers.length).toEqual(0);
    });

    it('title of search results should be group name', () => {
        ha.fixture.detectChanges();

        searchBoxService.searchStr.next('n1');
        expect(ha.get('.dropdown-item').thatContains('N1')).toBeExists();
    });

    it('If group is not found, the length of filteredItems should be zero', () => {
        ha.fixture.detectChanges();

        searchBoxService.searchStr.next('1234567890');
        expect(component.filteredItems.length).toEqual(0);
    });
});
