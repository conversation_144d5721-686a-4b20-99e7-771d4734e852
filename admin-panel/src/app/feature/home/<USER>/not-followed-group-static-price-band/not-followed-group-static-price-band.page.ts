import {AfterViewInit, Component, Element<PERSON>ef, OnD<PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {TranslateKey} from '@shared/enums/translate-key';
import {Observable} from 'rxjs';
import {MatPaginator} from '@angular/material/paginator';
import {PaginatedDataTable} from '@models/paginated-data-table';
import {map, takeUntil} from 'rxjs/operators';
import {
    notFollowedGroupStaticPriceBandDataTableColumns
} from './not-followed-group-static-price-band-data-table-columns';
import {RequestOptions} from '@models/request-options';
import {PagesSharedModule} from '../../shared/modules/pages-shared.module';
import {GroupDataService} from '@dataServices/group-data.service';
import {ActivatedRoute} from '@angular/router';
import {NotFollowingGroupInstrument} from '@homeModels/instrument';


@Component({
    selector: 'app-not-followed-group-static-price-band.page',
    templateUrl: './not-followed-group-static-price-band.page.html',
    imports: [
        PagesSharedModule
    ]
})
export class NotFollowedGroupStaticPriceBandPage extends PaginatedDataTable<NotFollowingGroupInstrument> implements OnInit, OnDestroy, AfterViewInit {
    readonly translateKeys = TranslateKey;

    readonly columns = notFollowedGroupStaticPriceBandDataTableColumns();

    @ViewChild('tableContainer')
    override _tableContainer: ElementRef<HTMLDivElement>;

    @ViewChild(MatPaginator)
    override _paginator: MatPaginator;

    private _groupCode: string;
    get groupCode(): string { return this._groupCode; }

    constructor(
        private _groupDataService: GroupDataService,
        private _activatedRoute: ActivatedRoute
    ) {
        super();

        this.filterProperty.refreshPage = this._refreshPageData.bind(this);
        this.filterProperty.callback = this._filterPageData.bind(this);
        this.filterProperty.isShownFilter = false;
    }

    ngOnInit(): void {
        this._subscribeRouteParams();
    }

    ngAfterViewInit(): void {
        this._changeDetectorRef.detectChanges();
        this._refreshPageData();
        this._subscribeOnPageChange();
    }

    ngOnDestroy(): void {
        this._onDestroy.next();
        this._onDestroy.complete();
    }

    override fetchExcelData(): Observable<NotFollowingGroupInstrument[]> {
        const requestOptions: RequestOptions = {
            hasLocalErrorHandler: true,
            params: this.getFilterQueryParams()
        }

        return this._groupDataService
            .notFallowedStaticThresholdInstruments(this._groupCode, requestOptions)
            .pipe(takeUntil(this._onDestroy))
            .pipe(map(resp => resp.content));
    }

    override getDataTableColumns(): string[] {
        return this.columns.map(column => column.title);
    }


    override _fetchPageData(): void {
        const requestOptions: RequestOptions = {
            params: this.getFilterQueryParams()
        }

        this._groupDataService
            .notFallowedStaticThresholdInstruments(this._groupCode, requestOptions)
            .pipe(takeUntil(this._onDestroy))
            .subscribe(resp => {
                this._pageData = resp;
                this._changeDetectorRef.detectChanges();
            });
    }

    private _subscribeRouteParams(): void {
        this._activatedRoute.params
            .pipe(takeUntil(this._onDestroy))
            .subscribe(params => {
                this._groupCode = params.groupCode;
            });
    }
}
