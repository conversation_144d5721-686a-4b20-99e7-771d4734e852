import {TestBed} from '@angular/core/testing';
import {NotFollowedGroupStaticPriceBandPage} from './not-followed-group-static-price-band.page';
import {ComponentHarness} from '@test/harness/component-harness';
import {BsModalService} from 'ngx-bootstrap/modal';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {RouterTestingModule} from '@angular/router/testing';
import {PageTemplateModule} from '@modules/page-template/page-template.module';
import {SharedModule} from '../../../../shared/shared.module';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {BrokerDataService} from '@dataServices/broker-data.service';
import {brokerDataServiceHarness} from '@dataServices/broker-data.service.harness';
import {DataTableModule} from '@modules/data-table/data-table.module';

describe('NotFollowedGroupStaticPriceBandPage', () => {
    let ha: ComponentHarness<NotFollowedGroupStaticPriceBandPage>;
    let modalServiceSpy: BsModalService;

    beforeEach(() => {
        ha = new ComponentHarness(NotFollowedGroupStaticPriceBandPage, {
            declarations: [
                SelectByLanguagePipe
            ],
            imports: [
                NotFollowedGroupStaticPriceBandPage,
                TranslateTestingModule,
                RouterTestingModule,
                PageTemplateModule,
                SharedModule,
                DataTableModule
            ],
            providers: [
                BsModalService,
                {
                    provide: BrokerDataService,
                    useValue: brokerDataServiceHarness
                }
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
            detectChanges: false
        });

        modalServiceSpy = TestBed.inject(BsModalService);
    });

    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });
});
