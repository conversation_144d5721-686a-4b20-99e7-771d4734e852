import {WeekendDialogComponent} from './weekend-dialog.component';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {ComponentHarness} from '@test/harness/component-harness';
import {NO_ERRORS_SCHEMA} from '@angular/core';
import {FormControl, FormGroup} from '@angular/forms';
import {calendarDataServiceHarness} from '@dataServices/calendar-data.service.harness';
import {CalendarDataService} from '@dataServices/calendar-data.service';
import {MatCheckboxChange} from '@angular/material/checkbox';
import {Weekend} from '@models/weekend';
import {of} from 'rxjs';
import {MbDatePipe} from '@modules/datepicker/mb-date.pipe';
import {StoreService} from '@shared/services/store.service';
import {DayOfWeek} from '@models/day-of-week';

describe('WeekendDialogComponent', () => {
    let ha: ComponentHarness<WeekendDialogComponent>;
    let bsModalRefSpy: jasmine.SpyObj<BsModalRef>;

    beforeEach(() => {
        bsModalRefSpy = jasmine.createSpyObj('BsModalRef', ['hide']);

        ha = new ComponentHarness(WeekendDialogComponent, {
            declarations: [
                WeekendDialogComponent,
                MbDatePipe
            ],
            imports: [
                TranslateTestingModule,
            ],
            providers: [
                BsModalRef,
                { provide: CalendarDataService, useValue: calendarDataServiceHarness },
                { provide: BsModalRef, useValue: bsModalRefSpy }
            ],
            schemas: [NO_ERRORS_SCHEMA],
            detectChanges: false
        });
    });

    afterEach(() => {
        StoreService.adminCommandCallBacks.splice(0);
    })

    it('should create', () => {
        ha.detectChanges();
        expect(ha.component).toBeTruthy();
    });

    it('should initialize the form group', () => {
        ha.component.ngOnInit();

        expect(ha.component.formGroup).toBeDefined();
        expect(ha.component.formGroup instanceof FormGroup).toBeTrue();
        expect(ha.component.formGroup.controls.applyDate instanceof FormControl).toBeTrue();
        expect(ha.component.formGroup.controls.daysOfWeek instanceof FormControl).toBeTrue();
    });

    it('should update the selected days of week', async () => {
        await ha.detectChanges();

        // set initial value of daysOfWeek control to an empty array
        ha.component.formGroup.controls.daysOfWeek.setValue([]);

        // Simulate a checkbox change event
        const event = new MatCheckboxChange();
        event.checked = true;
        ha.component.updateDaysOfWeek('Monday', event);

        // Expect daysOfWeek control to have a single value of 'Monday'
        expect(ha.component.formGroup.controls.daysOfWeek.value.length).toBe(1);
        expect(ha.component.formGroup.controls.daysOfWeek.value[0]).toBe('Monday');

        // Simulate another checkbox change event
        event.checked = false;
        ha.component.updateDaysOfWeek('Monday', event);

        // Expect daysOfWeek control to be empty
        expect(ha.component.formGroup.controls.daysOfWeek.value.length).toBe(0);
    });

    it('should close the dialog', () => {
        ha.detectChanges();

        ha.component.closeDialog();
        expect(bsModalRefSpy.hide).toHaveBeenCalled();
    });

    it('should fetch weekends on init', () => {
        const weekends = [new Weekend()];
        spyOn(calendarDataServiceHarness, 'getWeekends').and.returnValue(of(weekends));
        ha.detectChanges();

        expect(calendarDataServiceHarness.getWeekends).toHaveBeenCalled();
    });

    it('should submit form and close dialog when submitForm() is called', () => {
        const formValue = {
            applyDate: new Date(),
            daysOfWeek: [DayOfWeek.SATURDAY, DayOfWeek.SATURDAY]
        };
        spyOn(calendarDataServiceHarness, 'addWeekends').and.returnValue(of(null));
        spyOn(calendarDataServiceHarness, 'getWeekends').and.returnValue(of([]));
        spyOn(ha.component, 'closeDialog');
        spyOn(ha.component as any, '_subscribeApplyDate');
        ha.detectChanges();

        ha.component.formGroup.setValue(formValue);
        ha.component.submitForm();

        expect(calendarDataServiceHarness.addWeekends).toHaveBeenCalledWith(new Weekend.Create(formValue));
        expect(ha.component.closeDialog).toHaveBeenCalled();
    });

    it('should delete weekend and fetch weekends again when deleteWeekend() is called', () => {
        const weekend = new Weekend();
        const response = { commandId: '123' };
        StoreService.adminCommandCallBacks.splice(0);
        spyOn(calendarDataServiceHarness, 'deleteWeekends').and.returnValue(of(response as any));

        ha.component.deleteWeekend(weekend);

        expect(calendarDataServiceHarness.deleteWeekends).toHaveBeenCalledWith(weekend.applyDate);
        expect(StoreService.adminCommandCallBacks.length).toBe(1);
    });
});
