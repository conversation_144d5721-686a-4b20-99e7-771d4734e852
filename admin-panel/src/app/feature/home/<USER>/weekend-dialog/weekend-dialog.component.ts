import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {FormControl, FormGroup} from '@angular/forms';
import {Weekend} from '@models/weekend';
import {DateTriggerForDirective} from '@modules/datepicker/date-trigger-for.directive';
import {MatCheckboxChange} from '@angular/material/checkbox';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {CalendarDataService} from '@dataServices/calendar-data.service';
import {takeUntil} from 'rxjs/operators';
import {Subject} from 'rxjs';
import {AdminCommandCallback} from '@models/admin-command-callback';
import {StoreService} from '@shared/services/store.service';
import {DayOfWeek} from '@models/day-of-week';

@Component({
    selector: 'app-weekend-dialog',
    templateUrl: './weekend-dialog.component.html',
    standalone: false
})
export class WeekendDialogComponent implements OnI<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> {
    get translateKeys() { return TranslateKey; }

    get daysOfWeek() { return Object.keys(DayOfWeek) }

    private _formGroup: FormGroup<Weekend.Form>;
    get formGroup(): FormGroup<Weekend.Form> { return this._formGroup; }

    private _weekends: Weekend[];
    get weekends(): Weekend[] { return this._weekends; }

    @ViewChild(DateTriggerForDirective)
    private _dateTriggerFor: DateTriggerForDirective;

    private _onDestroy = new Subject();


    get daysOfWeeks(): string[] {
        return this._formGroup.controls.daysOfWeek.value
    }

    constructor(
        private _modalRef: BsModalRef,
        private _calendarDataService: CalendarDataService
    ) { }

    ngOnInit(): void {
        this._fetchWeekends();
        this._setupFormGroup();
        this._subscribeApplyDate();
    }

    ngOnDestroy() {
        this._onDestroy.next();
        this._onDestroy.complete();
    }

    updateDaysOfWeek(day, event: MatCheckboxChange): void {
        const daysOfWeek = this.daysOfWeeks;
        if (event.checked) {
            daysOfWeek.push(day);
        } else {
            const index = daysOfWeek.findIndex(item => item === day);
            daysOfWeek.splice(index, 1);
        }
    }

    closeDialog(): void {
        this._modalRef.hide();
    }

    submitForm(): void {
        this._calendarDataService
            .addWeekends(new Weekend.Create(this._formGroup.getRawValue()))
            .pipe(takeUntil(this._onDestroy))
            .subscribe(this.closeDialog.bind(this));
    }

    deleteWeekend(weekend: Weekend): void {
        this._calendarDataService
            .deleteWeekends(weekend.applyDate)
            .subscribe(resp => {
                const adminCommandCallback = new AdminCommandCallback(resp.commandId, this._fetchWeekends.bind(this));
                StoreService.adminCommandCallBacks.push(adminCommandCallback);
            });
    }

    private _setupFormGroup(): void {
        this._formGroup = new FormGroup({
            applyDate: new FormControl(StoreService.systemDateTime),
            daysOfWeek: new FormControl([])
        });
    }

    private _subscribeApplyDate(): void {
        this.formGroup.controls.applyDate.valueChanges
            .subscribe(resp => {
                this._dateTriggerFor.setValue(resp)
            });
    }

    private _fetchWeekends(): void {
        this._calendarDataService
            .getWeekends()
            .subscribe(resp => {
                this._weekends = resp;
            })
    }
}
