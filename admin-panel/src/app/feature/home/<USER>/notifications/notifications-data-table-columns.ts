import {DataTableColumn} from '@models/data-table';
import {TranslateKey} from '@enums/translate-key';
import {TranslatePipe} from '@ngx-translate/core';
import {MbDatePipe} from '@modules/datepicker/mb-date.pipe';
import {UtilConstants} from '@constants/util-constants';
import {InputFilter} from '@modules/filter/input/input-filter';
import {AdminCommand} from '@models/admin-command';
import {TimeFilter} from '@modules/filter/time/time-filter';
import {AutoCompleteFilter} from '@modules/filter/auto-complete-filter/auto-complete-filter';
import {Group} from '@models/group';
import {StoreService} from '@services/store.service';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {inject} from '@angular/core';
import {HasPermissionService} from '@directives/has-permission.service';
import {Permissions} from '../../../../shared/constants/permissions.constant';
import {enumToKeyValue, toCamelCase} from '@core/utils';

export function NotificationsDataTableColumns(): DataTableColumn<AdminCommand>[] {

    const hasPermissionService = inject(HasPermissionService);

    let columns: DataTableColumn<AdminCommand>[] = [
        {
            title: TranslateKey.type,
            pipeToken: TranslatePipe,
            isSticky: true,
            value(data) { return toCamelCase(data.type) },
            filter: new AutoCompleteFilter({
                options: enumToKeyValue(AdminCommand.Type),
                queryParam: 'type',
                filterField: 'key',
                formatOptionTitle(data) { return toCamelCase(data.key); }
            })
        },
        {
            title: TranslateKey.status,
            pipeToken: TranslatePipe,
            value(data) { return toCamelCase(data.status) },
            filter: new AutoCompleteFilter({
                options: enumToKeyValue(AdminCommand.Status),
                queryParam: 'status',
                filterField: 'key',
                formatOptionTitle(data) { return toCamelCase(data.key); }
            })
        },
        {
            title: TranslateKey.time,
            pipeToken: MbDatePipe,
            pipeArgs: [UtilConstants.TIME_FORMAT],
            value(data) { return data.timestamp },
            filter: new TimeFilter({
                queryParam: 'timestamp'
            }),
        },
        {
            title: TranslateKey.security,
            class: 'monospace',
            value(data) { return data.securityId },
            filter: new InputFilter({
                queryParam: 'securityId'
            }),
        },
        {
            title: TranslateKey.group,
            pipeToken: SelectByLanguagePipe,
            value(data) { return data.group?.uniqueName },
            minWidth: 100,
            filter: new AutoCompleteFilter<Group>({
                queryParam: 'groupCode',
                filterField: 'code',
                options: StoreService.groups,
                formatOptionTitle(option): string { return option.code; }
            })
        }
    ];

    if (hasPermissionService.hasPermission(Permissions.NOTIFICATIONS_PERMISSION)) {
        const sender = {
            title: TranslateKey.sender,
            value(data) { return data.sender },
            filter: new InputFilter<string>({
                queryParam: 'sender'
            })
        };

        columns.splice(1, 0, sender);
        columns.join();
    }

    return columns;
}
