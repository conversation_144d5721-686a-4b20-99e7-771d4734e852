import {Component} from '@angular/core';
import {CommandQueueDetail} from '../../pages/queued-commands/command-queue-detail';
import {TranslateKey} from '@enums/translate-key';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {TableDataProvider} from '@models/table-representation/table-representable';

@Component({
    selector: 'app-command-queue-detail-dialog',
    templateUrl: './command-queue-detail-dialog.component.html',
    standalone: false
})
export class CommandQueueDetailDialogComponent implements CommandQueueDetail {
    readonly translateKeys = TranslateKey;

    rejectionCause: string[];

    commandRequest: TableDataProvider;

    title: string;

    constructor(public modalRef: BsModalRef) {}
}
