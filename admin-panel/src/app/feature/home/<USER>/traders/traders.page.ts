import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {TranslateKey} from '@shared/enums/translate-key';
import {MatPaginator} from '@angular/material/paginator';
import {PaginatedDataTable} from '@models/paginated-data-table';
import {tradersDataTableColumns} from './traders-data-table-columns';
import {RequestOptions} from '@models/request-options';
import {TraderDataService} from '@dataServices/trader-data.service';
import {Trader} from '@models/trader';
import {plainToInstance} from '../../../../shared/plain-to-instance/plain-to-instance';
import {AdminCommandCallback} from '@models/admin-command-callback';
import {StoreService} from '@shared/services/store.service';
import {AddTraderDialogComponent} from '../../dialogs/add-trader-dialog/add-trader-dialog.component';
import {BsModalService} from 'ngx-bootstrap/modal';
import {map, takeUntil} from 'rxjs/operators';
import {Observable} from 'rxjs';
import {SystemState} from '@models/system-state';
import {Permissions} from '../../../../shared/constants/permissions.constant';
import {PagesSharedModule} from '../../shared/modules/pages-shared.module';


@Component({
    selector: 'app-traders',
    templateUrl: './traders.page.html',
    imports: [PagesSharedModule]
})
export class TradersPage extends PaginatedDataTable<Trader> implements OnInit {
    readonly translateKeys = TranslateKey;

    readonly permissions = Permissions;

    readonly columns = tradersDataTableColumns();

    get isPostSession(): boolean {
        return this._currentState === SystemState.State.POST_SESSION;
    }

    private _currentState: SystemState.State;

    @ViewChild('tableContainer')
    override _tableContainer: ElementRef<HTMLDivElement>;

    @ViewChild(MatPaginator)
    override _paginator: MatPaginator;

    constructor(
        private _modalService: BsModalService,
        private _traderDataService: TraderDataService
    ) {
        super();

        this.filterProperty.actionBtns.unshift(...[
            {
                title: TranslateKey.addTrader,
                icon: 'add',
                isDisabled: () => this.isPostSession,
                callback: this._openAddTraderDialog.bind(this),
                permissions: this.permissions.CREATE_TRADER_PERMISSION
            }
        ]);
        this.filterProperty.refreshPage = this._refreshPageData.bind(this);
        this.filterProperty.callback = this._filterPageData.bind(this);
    }

    ngOnInit(): void {
        this._fetchSystemState();

        this._modalService.onHide
            .pipe(takeUntil(this._onDestroy))
            .subscribe(this._fetchPageData.bind(this));
    }

    deleteTrader(row: Trader): void {
        this._traderDataService
            .deleteTrader(row.traderId)
            .subscribe((resp) => {
                const adminCommandCallBack = new AdminCommandCallback(resp.commandId, this._fetchPageData.bind(this));
                StoreService.adminCommandCallBacks.push(adminCommandCallBack);
            });
    }

    override fetchExcelData(): Observable<Trader[]> {
        const requestOptions: RequestOptions = {
            params: this.getFilterQueryParams()
        }

        return this._traderDataService
            .getAllTraders(requestOptions)
            .pipe(map(resp => resp.content));
    }

    override _fetchPageData(): void {
        const requestOptions: RequestOptions = {
            hasLocalErrorHandler: true,
            params: this.getFilterQueryParams()
        }

        this._traderDataService
            .getAllTraders(requestOptions)
            .subscribe(resp => {
                StoreService.traders = plainToInstance(Trader, resp.content);
                StoreService.traders.forEach(item => StoreService.tradersObj[item.traderId] = item);

                resp.content = StoreService.traders;
                this._pageData = resp;
                this._changeDetectorRef.detectChanges();
            });
    }

    private _openAddTraderDialog(): void {
        this._modalService.show(AddTraderDialogComponent);
    }

    private _fetchSystemState(): void {
        StoreService.systemState.subscribe(resp => {
            this._currentState = resp;
            this._changeDetectorRef.detectChanges();
        });
    }
}
