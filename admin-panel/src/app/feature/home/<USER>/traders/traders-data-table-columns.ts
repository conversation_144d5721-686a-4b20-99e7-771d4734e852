import {DataTableColumn} from '@models/data-table';
import {TranslateKey} from '@shared/enums/translate-key';
import {InputFilter} from '@modules/filter/input/input-filter';
import {Trader} from '@models/trader';
import {InputMasks} from '@constants/input-masks';

export function tradersDataTableColumns(): DataTableColumn<Trader>[] {
    return [
        {
            title: TranslateKey.traderId,
            value(data) { return data.traderId },
            filter: new InputFilter({
                queryParam: 'traderId',
                imask: InputMasks.TRADER
            })
        },
        {
            title: TranslateKey.brokerId,
            value(data) { return data.broker.id },
            filter: new InputFilter({
                queryParam: 'brokerId',
                imask: InputMasks.BROKER_ID
            })
        },
        {
            title: TranslateKey.townCode,
            value(data) { return data.townCode }
        },
        {
            title: TranslateKey.firmCode,
            value(data) { return data.firmCode }
        },
        {
            title: TranslateKey.counterCode,
            value(data) { return data.counterCode }
        }
    ];
}
