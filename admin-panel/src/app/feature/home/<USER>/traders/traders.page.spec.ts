import {TradersPage} from './traders.page';
import {DataTableFunctionality} from '@test/data-table-functionality';
import {ComponentHarness} from '@test/harness/component-harness';
import {BsModalService} from 'ngx-bootstrap/modal';
import {SelectByLanguagePipe} from '@modules/shared-declarations/select-by-language/select-by-language.pipe';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {PageTemplateModule} from '@modules/page-template/page-template.module';
import {SharedModule} from '../../../../shared/shared.module';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {DataTableModule} from '@modules/data-table/data-table.module';
import {RouterTestingModule} from '@angular/router/testing';
import {TraderDataService} from '@dataServices/trader-data.service';
import {traderDataServiceHarness} from '@dataServices/trader-data.service.harness';

describe('TradersPage', () => {
    let ha: ComponentHarness<TradersPage>;

    beforeEach(() => {
        ha = new ComponentHarness(TradersPage, {
            declarations: [
                SelectByLanguagePipe
            ],
            imports: [
                TranslateTestingModule,
                RouterTestingModule,
                PageTemplateModule,
                DataTableModule,
                SharedModule,
                TradersPage
            ],
            providers: [
                BsModalService,
                {
                    provide: TraderDataService,
                    useValue: traderDataServiceHarness
                }
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
            detectChanges: false
        });
    });

    it('should create', () => {
        ha.detectChanges();

        expect(ha.component).toBeTruthy();
    });

    describe('DataTable functionality', () => {
        it('when the total number of rows exceeds the page size, pagination should be displayed', () => {
            ha.detectChanges();
            DataTableFunctionality.when_the_total_number_of_rows_exceeds_the_page_size_pagination_should_be_displayed(ha);
        });

        it('pagination should be hidden when the total number of rows is less than the page size', () => {
            ha.detectChanges();
            DataTableFunctionality.pagination_should_be_hidden_when_the_total_number_of_rows_is_less_than_the_page_size(ha);
        });
    });
});
