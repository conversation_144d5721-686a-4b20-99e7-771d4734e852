import {Component, OnD<PERSON>roy, OnInit} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {FormGroup} from '@angular/forms';
import {Subject} from 'rxjs';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {InstrumentDataService} from '@dataServices/instrument-data.service';
import {SnackBarService} from '@services/snack-bar.service';
import {setupCreateInstrumentFormFields} from './setup-create-instrument-form-fields';
import {InstrumentFormFieldSets} from './instrument-form-field-sets';
import {takeUntil} from 'rxjs/operators';
import {StoreService} from '@shared/services/store.service';
import {ChangeDetectionService} from '@services/change-detection.service';
import {FormFields} from '@modules/dynamic-form/form-field';
import {FormFieldSet} from '@modules/dynamic-form/form-field-sets';
import {Instrument} from '@homeModels/instrument';

@Component({
    selector: 'app-create-instrument-dialog',
    templateUrl: './create-instrument-dialog.component.html',
    standalone: false
})
export class CreateInstrumentDialogComponent implements OnInit, OnDestroy {
    readonly translateKeys = TranslateKey;

    readonly formGroup = new FormGroup<Instrument.CreateForm>({} as any);

    private _formFields: FormFields<Instrument.CreateForm>;
    get formFields(): FormFields { return this._formFields; }

    private _formFieldSets: FormFieldSet[] = [
        {title: TranslateKey.basicSpec, id: InstrumentFormFieldSets.BASIC_SPEC},
        {title: TranslateKey.instrumentSpec, id: InstrumentFormFieldSets.SECURITY_SPEC},
        {title: TranslateKey.orderSpec, id: InstrumentFormFieldSets.ORDER_SPEC}
    ];
    get formFieldSets(): FormFieldSet[] { return this._formFieldSets; }

    private _onDestroy = new Subject();

    constructor(
        public modalRef: BsModalRef,
        private _instrumentDataService: InstrumentDataService,
        private _snackBarService: SnackBarService
    ) {}

    ngOnInit(): void {
        this._setupFormGroup();
    }

    ngOnDestroy(): void {
        this._onDestroy.next();
        this._onDestroy.complete();
    }

    private _subscribeOnFormControls(): void {
        const marketControl = this.formGroup.controls.securitySpec.controls.market;
        const groupControl = this.formGroup.controls.securitySpec.controls.groupId;

        marketControl.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe((marketValue: any) => {
                const groupFormField = this._formFields.rawValue.securitySpec.groupId;
                const groupOptions = marketValue ? StoreService.groups.filter(group => group.market === marketValue) : StoreService.groups;
                const groupCode = typeof groupFormField.value === 'string' ? groupFormField.value : groupFormField.value?.code;
                groupFormField.value = StoreService.groupsObj[groupCode]?.market === marketValue ? StoreService.groupsObj[groupCode] : '';
                groupFormField.options.set(groupOptions);
                ChangeDetectionService.onChange.next({type: 'FORM'});
            });

        groupControl.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe((groupValue: any) => {
                if (groupValue && !marketControl.value) {
                    marketControl.setValue(StoreService.groupsObj[groupValue].market as any);
                    ChangeDetectionService.onChange.next({type: 'FORM'});
                }
            });
    }

    onSubmit(): void {
        this._removePercentagePriceBandIfEmpty();
        this._instrumentDataService
            .addInstrument(this.formGroup.value)
            .subscribe(() => {
                this._snackBarService.open(TranslateKey.successfullySubmitted);
                this.modalRef.hide();
            });
    }

    private _removePercentagePriceBandIfEmpty(): void {
        if (
            !this.formGroup.value.priceBandPercentage?.lowerBoundPercentage &&
            !this.formGroup.value.priceBandPercentage?.upperBoundPercentage
        ) {
            this.formGroup.removeControl('priceBandPercentage');
        }
    }

    private _setupFormGroup(): void {
        this._formFields = setupCreateInstrumentFormFields();
        setTimeout(this._subscribeOnFormControls.bind(this), 1000);
    }
}
