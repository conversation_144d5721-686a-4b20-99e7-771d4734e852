import {FormFields} from "@modules/dynamic-form/form-field";
import {InputFormField} from "@modules/dynamic-form/form-fields/input-field/input-form-field";
import {TranslateKey} from "@enums/translate-key";
import {Validators} from "@angular/forms";
import {FieldsPlaceholder} from "../../../../shared/constants/fields-placeholder";
import {SelectOptionFormField} from "@modules/dynamic-form/form-fields/select-option-field/select-option-form-field";
import {enumToKeyValue, toCamelCase} from "@core//utils";
import {ActionButtonField} from "@modules/dynamic-form/form-fields/action-button-field/action-button-field";
import {ScheduleTemplateFormFieldSets} from "../../shared/constants/schedule-template-form-field-sets";
import {InputMasks} from "@constants/input-masks";
import {SystemState} from "@models/system-state";

export function SetupCreateScheduleTemplateFormFields(): FormFields {
    return [
        new InputFormField({
            formControlName: 'code',
            label: TranslateKey.code,
            width: 4,
            validations: [Validators.required],
            placeholder: FieldsPlaceholder.TEMPLATE_CODE
        }),
        {
            formGroupName: 'name',
            formFields: [
                new InputFormField({
                    formControlName: 'fa',
                    label: TranslateKey.persianName,
                    width: 4,
                    validations: [Validators.required],
                    placeholder: FieldsPlaceholder.TEMPLATE_NAME_FA
                }),

                new InputFormField({
                    formControlName: 'en',
                    label: TranslateKey.englishName,
                    width: 4,
                    validations: [Validators.required],
                    placeholder: FieldsPlaceholder.TEMPLATE_NAME_EN
                })
            ]
        },

        {
            formArrayName: 'systemActions',
            formFields: []
        }
    ];
}

export function SystemActionFields(onRemove: () => void): FormFields {
    return [
        new ActionButtonField({
            title: '-',
            width: 1,
            fieldSetId: ScheduleTemplateFormFieldSets.ACTIONS,
            onClick: onRemove,
            style: {marginTop: '28px'}
        }),

        new InputFormField({
            formControlName: 'runAt',
            label: TranslateKey.runAt,
            width: 5,
            validations: [Validators.required],
            fieldSetId: ScheduleTemplateFormFieldSets.ACTIONS,
            mask: InputMasks.TIME_MASK,
            unmask: false,
            value: '00:00:00'
        }),

        new SelectOptionFormField({
            formControlName: 'targetState',
            label: TranslateKey.targetState,
            width: 6,
            validations: [Validators.required],
            options: enumToKeyValue(SystemState.State),
            optionFieldName: 'key',
            fieldSetId: ScheduleTemplateFormFieldSets.ACTIONS,
            getTitle(state): string { return toCamelCase(state.value); },
        })

    ];
}
