import {<PERSON><PERSON><PERSON>w<PERSON><PERSON><PERSON>, ChangeDetector<PERSON><PERSON>, <PERSON>mponent, ElementRef, On<PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {OrderRequestReport} from '@models/order/order-request-report';
import {TranslateKey} from '@shared/enums/translate-key';
import {orderRequestsReportDataTableColumns} from '../order-requests-report/order-requests-report-data-table-columns';
import {OrderRequestReportDataService} from '@dataServices/order-request-report-data.service';
import {TranslateService} from '@ngx-translate/core';
import {BsModalService} from 'ngx-bootstrap/modal';
import {takeUntil} from 'rxjs/operators';
import {ActivatedRoute} from '@angular/router';
import {DataTable} from '@models/data-table';
import {RoutingLayout} from '@constants/routing-layout';
import {TableGroupDialogComponent} from '../../dialogs/table-group-dialog/table-group-dialog.component';
import {TableGroupDialog} from '../../dialogs/table-group-dialog/table-group-dialog';
import {OrderRequestsReportTableData} from '../../shared/constants/order-requests-report-table-data';
import {HomeRoutes} from '../../shared/constants/home-routing.constants';
import {PagesSharedModule} from '../../shared/modules/pages-shared.module';

@Component({
    selector: 'app-order-track',
    templateUrl: './order-trace.page.html',
    imports: [PagesSharedModule]
})
export class OrderTracePage extends DataTable<OrderRequestReport> implements OnInit, OnDestroy, AfterViewInit {

    readonly translateKeys = TranslateKey;

    readonly columns = orderRequestsReportDataTableColumns();

    private _orderId: string;

    @ViewChild('tableContainer')
    _tableContainer: ElementRef<HTMLDivElement>;

    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _orderRequestReportDataService: OrderRequestReportDataService,
        private _translateService: TranslateService,
        private _activatedRoute: ActivatedRoute,
        protected modalService: BsModalService
    ) {
        super();
    }

    ngOnInit(): void {
        this._subscribeRouteParams();
        this.setFilterProperty();
    }

    ngOnDestroy(): void {
        this._onDestroy.next();
        this._onDestroy.complete();
    }

    ngAfterViewInit(): void {
        this._changeDetectorRef.detectChanges();
        this._fetchPageData();
    }

    setFilterProperty(): void {
        this.filterProperty.isShownFilter = false;
        this.filterProperty.actionBtns = [
            {
                title: TranslateKey.orderRequestsReport,
                icon: 'list_alt',
                navigationUrl: `/${RoutingLayout.HOME}/${HomeRoutes.ORDER_REQUESTS_REPORT}`
            }
        ];
        this.filterProperty.refreshPage = this.refreshPage.bind(this);
        this.filterProperty.callback = this.refreshPage.bind(this);
    }

    openOrderDetailDialog(row: OrderRequestReport): void {
        this._orderRequestReportDataService.getOrder(row.id).subscribe(resp => {
            const dialogTitle = this._translateService.instant(TranslateKey.orderDetails);
            const tableGroupData = OrderRequestsReportTableData(resp);

            this.modalService.show(TableGroupDialogComponent, {
                initialState: new TableGroupDialog(dialogTitle, tableGroupData),
                class: 'modal-lg'
            })
        });
    }

    refreshPage(): void {
        this._fetchPageData();
    }

    override _fetchPageData(): void {
        this._orderRequestReportDataService
            .getOrderHistory(this._orderId)
            .pipe(takeUntil(this._onDestroy))
            .subscribe(resp => {
                this._data = resp;
                this.isPageDataLoaded.set(true);
                this._changeDetectorRef.detectChanges();
            });
    }

    private _subscribeRouteParams(): void {
        this._activatedRoute.params
            .pipe(takeUntil(this._onDestroy))
            .subscribe(params => {
                this._orderId = params.orderId;
                if (this.filterProperty.filterParams.size) {
                    this._fetchPageData();
                }
            });
    }
}
