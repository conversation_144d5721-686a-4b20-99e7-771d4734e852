import {FormFields} from '@modules/dynamic-form/form-field';
import {AutoCompleteFormField} from '@modules/dynamic-form/form-fields/auto-complete-field/auto-complete-form-field';
import {TranslateKey} from '@enums/translate-key';
import {Validators} from '@angular/forms';
import {FieldsPlaceholder} from '../../../../shared/constants/fields-placeholder';
import {Group} from '@models/group';
import {StoreService} from '@shared/services/store.service';
import {ReadOnlyFormField} from '@modules/dynamic-form/form-fields/read-only-field/read-only-form-field';
import {AccessMatrix} from '@homeModels/access-matrix';
import {LangService} from '@shared/services/lang.service';

export function copyAccessMatrixFormFields(entryType: AccessMatrix.EntryType): FormFields {
    const width = 12;

    return [
        new AutoCompleteFormField<Group>({
            formControlName: 'sourceGroupCode',
            label: TranslateKey.sourceGroupCode,
            width,
            validations: [Validators.required],
            placeholder: FieldsPlaceholder.GROUP_CODE,
            searchExpField: 'searchExpression',
            options: StoreService.groups,
            getTitle(option): string {
                return option
                    ? option.code + ' - ' + LangService.getName(option.name)
                    : '';
            },
            optionFieldName: 'code'
        }),

        new AutoCompleteFormField<Group>({
            formControlName: 'targetGroupCode',
            label: TranslateKey.targetGroupCode,
            width,
            validations: [Validators.required],
            placeholder: FieldsPlaceholder.GROUP_CODE,
            searchExpField: 'searchExpression',
            options: StoreService.groups,
            getTitle(option): string {
                return option
                    ? option.code + ' - ' + LangService.getName(option.name)
                    : '';
            },
            optionFieldName: 'code'
        }),

        new ReadOnlyFormField({
            formControlName: 'entryType',
            value: entryType
        })

    ];
}
