import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {Valida<PERSON>} from '@angular/forms';
import {CustomValidators} from '@constants/custom-validators';
import {InputMasks} from '@constants/input-masks';
import {InstrumentAdminCommands} from '@homeModels/instrument/instrument-admin-commands';
import {UpdateInstrumentFormFieldSets} from '../../dialogs/update-instrument-dialog/update-instrument-form-field-sets';
import {FieldsPlaceholder} from '../../../../shared/constants/fields-placeholder';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';
import {CheckboxFormField} from '@modules/dynamic-form/form-fields/checkbox-field/checkbox-form-field';
import {InstrumentFormFieldSets} from '../../dialogs/create-instrument-dialog/instrument-form-field-sets';
import {FormFields} from '@modules/dynamic-form/form-field';

export function updateInstrumentFormFields(commandRequest: InstrumentAdminCommands.Update): FormFields {
    const width = 6;

    return [
        new InputFormField({
            formControlName: 'baseVolume',
            label: TranslateKey.baseVolume,
            width,
            validations: [Validators.required, Validators.min(0), Validators.maxLength(12)],
            value: commandRequest.updateSpec.baseVolume,
            mask: InputMasks.COMMA_SEPERATED_NUMBER,
            placeholder: FieldsPlaceholder.DECIMAL_NUMBER
        }),

        new InputFormField({
            formControlName: 'settlementDelay',
            label: TranslateKey.settlementDelay,
            width,
            validations: [Validators.required, Validators.min(0), Validators.maxLength(2)],
            value: commandRequest.updateSpec.settlementDelay,
            mask: InputMasks.COMMA_SEPERATED_NUMBER,
            placeholder: FieldsPlaceholder.SETTLEMENT_DELAY
        }),

        {
            formGroupName: 'orderSpec',
            formFields: [
                new InputFormField({
                    formControlName: 'priceTick',
                    label: TranslateKey.priceTick,
                    width,
                    validations: [Validators.required, CustomValidators.greaterThan(0), Validators.pattern(InputMasks.PRICE.pattern)],
                    value: commandRequest.updateSpec.orderSpec.priceTick,
                    fieldSetId: UpdateInstrumentFormFieldSets.ORDER_SPEC,
                    mask: InputMasks.FLOATING_NUMBER,
                    placeholder: FieldsPlaceholder.PRICE_TICK
                }),

                new InputFormField({
                    formControlName: 'lotSize',
                    label: TranslateKey.lotSize,
                    width,
                    validations: [Validators.required, CustomValidators.greaterThan(0), Validators.maxLength(12)],
                    value: commandRequest.updateSpec.orderSpec.lotSize,
                    fieldSetId: UpdateInstrumentFormFieldSets.ORDER_SPEC,
                    mask: InputMasks.COMMA_SEPERATED_NUMBER,
                    placeholder: FieldsPlaceholder.LOT_SIZE
                }),

                new InputFormField({
                    formControlName: 'minQuantity',
                    label: TranslateKey.minQuantity,
                    width,
                    validations: [Validators.required, Validators.min(0), Validators.maxLength(12)],
                    value: commandRequest.updateSpec.orderSpec.minQuantity,
                    fieldSetId: UpdateInstrumentFormFieldSets.ORDER_SPEC,
                    mask: InputMasks.FLOATING_NUMBER,
                    placeholder: FieldsPlaceholder.DECIMAL_NUMBER
                }),

                new InputFormField({
                    formControlName: 'maxBuyQuantity',
                    label: TranslateKey.maxBuyQuantity,
                    width,
                    validations: [Validators.required, Validators.min(0), Validators.maxLength(12)],
                    value: commandRequest.updateSpec.orderSpec.maxBuyQuantity,
                    fieldSetId: UpdateInstrumentFormFieldSets.ORDER_SPEC,
                    mask: InputMasks.COMMA_SEPERATED_NUMBER,
                    placeholder: FieldsPlaceholder.DECIMAL_NUMBER
                }),

                new InputFormField({
                    formControlName: 'maxSellQuantity',
                    label: TranslateKey.maxSellQuantity,
                    width,
                    validations: [Validators.required, Validators.min(0), Validators.maxLength(12)],
                    value: commandRequest.updateSpec.orderSpec.maxSellQuantity,
                    fieldSetId: UpdateInstrumentFormFieldSets.ORDER_SPEC,
                    mask: InputMasks.COMMA_SEPERATED_NUMBER,
                    placeholder: FieldsPlaceholder.DECIMAL_NUMBER
                })
            ]
        },

        new CheckboxFormField({
            formControlName: 'isShortSellAllowed',
            label: TranslateKey.isShortSellAllowed,
            width: 6,
            value: commandRequest.updateSpec.isShortSellAllowed,
            style: {paddingTop: '25px'},
            fieldSetId: InstrumentFormFieldSets.ORDER_SPEC
        })
    ];
}
