import {Valida<PERSON>} from '@angular/forms';
import {CustomValidators} from '@constants/custom-validators';
import {InputMasks} from '@constants/input-masks';
import {ProductFormFieldSets} from './product-form-field-sets';
import {Product} from '@homeModels/product';
import {Board} from '@shared/models/board';
import {MarketFlow} from '@shared/models/market-flow';
import {FormFields} from '@shared/modules/dynamic-form/form-field';
import {InputFormField} from '@shared/modules/dynamic-form/form-fields/input-field/input-form-field';
import {TranslateKey} from '@shared/enums/translate-key';
import {FieldsPlaceholder} from '@shared/constants/fields-placeholder';
import {ReadOnlyFormField} from '@shared/modules/dynamic-form/form-fields/read-only-field/read-only-form-field';
import {
    SelectOptionFormField
} from '@shared/modules/dynamic-form/form-fields/select-option-field/select-option-form-field';
import {OrderPreference} from '@shared/enums/order-preference';
import {DatePickerFormField} from '@shared/modules/dynamic-form/form-fields/date-picker-field/date-picker-form-field';

export function updateProductFormFields(product: Product.Details, boards: Board[], marketFlow: MarketFlow[]): FormFields {
    const mnemonic = {
        formGroupName: 'mnemonic',
        formFields: [
            new InputFormField({
                formControlName: 'fa',
                label: TranslateKey.faMnemonic,
                width: 6,
                value: product?.mnemonic?.fa,
                validations: [Validators.required, Validators.maxLength(17)],
                placeholder: FieldsPlaceholder.SHORT_NAME_FA,
                fieldSetId: ProductFormFieldSets.PRODUCT_SPEC
            }),

            new InputFormField({
                formControlName: 'en',
                label: TranslateKey.enMnemonic,
                width: 6,
                value: product?.mnemonic?.en,
                validations: [Validators.required, CustomValidators.fixedLength(4)],
                placeholder: FieldsPlaceholder.SHORT_NAME_EN,
                fieldSetId: ProductFormFieldSets.PRODUCT_SPEC
            }),
        ]
    };

    const formFields = commonFormFields(product, boards, marketFlow);
    formFields.unshift(mnemonic)

    return formFields;
}

export function updateProductCommandQueueFormFields(product: Product.Details, boards: Board[], marketFlow: MarketFlow[]): FormFields {
    const mnemonic = {
        formGroupName: 'mnemonic',
        formFields: [
            new ReadOnlyFormField({
                formControlName: 'fa',
                value: product?.mnemonic.fa
            }),

            new ReadOnlyFormField({
                formControlName: 'en',
                value: product?.mnemonic.en
            })
        ]
    };

    const formFields = commonFormFields(product, boards, marketFlow);
    formFields.unshift(mnemonic)

    return formFields;
}

export function commonFormFields(product: Product.Details, boards: Board[], marketFlow: MarketFlow[]): FormFields {
    const width = 6;
    return [
        new SelectOptionFormField({
            formControlName: 'marketFlowCode',
            label: TranslateKey.marketFlowCode,
            width,
            value: product?.marketFlowCode,
            validations: [Validators.required],
            placeholder: FieldsPlaceholder.MARKET_FLOW_CODE,
            options: marketFlow,
            getTitle(marketFlow: MarketFlow): string { return marketFlow ? marketFlow.code + ' - ' + marketFlow.name : ''; },
            optionFieldName: 'code',
            fieldSetId: ProductFormFieldSets.MARKET_SEC
        }),

        new SelectOptionFormField({
            formControlName: 'boardCode',
            label: TranslateKey.board,
            width,
            value: product?.boardCode,
            placeholder: FieldsPlaceholder.BOARD,
            validations: [Validators.required],
            options: boards,
            getTitle(board: Board): string { return board ? board.code + ' - ' + board.name : ''; },
            optionFieldName: 'code',
            fieldSetId: ProductFormFieldSets.MARKET_SEC
        }),

        {
            formGroupName: 'name',
            formFields: [
                new InputFormField({
                    formControlName: 'fa',
                    label: TranslateKey.persianName,
                    width,
                    value: product?.name?.fa,
                    validations: [Validators.required],
                    placeholder: FieldsPlaceholder.NAME_FA,
                    fieldSetId: ProductFormFieldSets.PRODUCT_SPEC
                }),

                new InputFormField({
                    formControlName: 'en',
                    label: TranslateKey.englishName,
                    width,
                    value: product?.name?.en,
                    validations: [Validators.required],
                    placeholder: FieldsPlaceholder.NAME_EN,
                    fieldSetId: ProductFormFieldSets.PRODUCT_SPEC
                }),
            ]
        },

        new SelectOptionFormField({
            formControlName: 'matchingType',
            label: TranslateKey.matchingAlgo,
            width,
            value: product?.matchingType,
            validations: [Validators.required],
            options: Object.keys(OrderPreference),
            placeholder: FieldsPlaceholder.MATCHING_TYPE,
            fieldSetId: ProductFormFieldSets.PRODUCT_SPEC
        }),

        new InputFormField({
            formControlName: 'normalBlockSize',
            label: TranslateKey.normalBlockSize,
            width,
            value: product?.normalBlockSize,
            validations: [Validators.required, Validators.maxLength(12), CustomValidators.greaterThan(0)],
            placeholder: FieldsPlaceholder.NORMAL_BLOCK_SIZE,
            mask: InputMasks.COMMA_SEPERATED_NUMBER,
            fieldSetId: ProductFormFieldSets.PRODUCT_SPEC
        }),

        new InputFormField({
            formControlName: 'parValue',
            label: TranslateKey.parValue,
            width,
            value: product?.parValue,
            validations: [Validators.required, Validators.pattern(InputMasks.PRICE.pattern)],
            placeholder: FieldsPlaceholder.DECIMAL_NUMBER,
            mask: InputMasks.FLOATING_NUMBER,
            fieldSetId: ProductFormFieldSets.PRODUCT_SPEC
        }),

        new SelectOptionFormField({
            formControlName: 'isPricePercentage',
            label: TranslateKey.percentagePrice,
            validations: [Validators.required],
            value: product?.isPricePercentage,
            width,
            options: [true, false],
            getTitle(option: boolean): string { return option.toString(); },
            placeholder: FieldsPlaceholder.YES,
            fieldSetId: ProductFormFieldSets.PRODUCT_SPEC
        }),

        new InputFormField({
            formControlName: 'underlyingProductId',
            label: TranslateKey.underlyingProductId,
            width,
            value: product.underlyingProductId,
            validations: [CustomValidators.fixedLength(12)],
            placeholder: FieldsPlaceholder.ISIN,
            fieldSetId: ProductFormFieldSets.DERIVATIVE_SPEC
        }),

        new DatePickerFormField({
            formControlName: 'tradingStartDate',
            label: TranslateKey.tradingStartDate,
            width,
            value: product?.tradingStartDate,
            fieldSetId: ProductFormFieldSets.DERIVATIVE_SPEC
        }),

        new DatePickerFormField({
            formControlName: 'tradingEndDate',
            label: TranslateKey.tradingEndDate,
            width,
            value: product?.tradingEndDate,
            fieldSetId: ProductFormFieldSets.DERIVATIVE_SPEC
        }),

        new DatePickerFormField({
            formControlName: 'maturityDate',
            label: TranslateKey.maturityDate,
            width,
            value: product?.maturityDate,
            fieldSetId: ProductFormFieldSets.DERIVATIVE_SPEC
        }),

        new InputFormField({
            formControlName: 'strikePrice',
            label: TranslateKey.strikePrice,
            width,
            value: product?.strikePrice,
            validations: [Validators.min(1)],
            mask: InputMasks.COMMA_SEPERATED_NUMBER,
            placeholder: FieldsPlaceholder.DECIMAL_NUMBER,
            fieldSetId: ProductFormFieldSets.DERIVATIVE_SPEC
        }),

        new InputFormField({
            formControlName: 'issuePrice',
            label: TranslateKey.issuePrice,
            width,
            value: product?.issuePrice,
            validations: [Validators.pattern(InputMasks.PRICE.pattern)],
            mask: InputMasks.FLOATING_NUMBER,
            placeholder: FieldsPlaceholder.DECIMAL_NUMBER,
            fieldSetId: ProductFormFieldSets.DERIVATIVE_SPEC
        })
    ];
}
