import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {TranslatePipe} from '@ngx-translate/core';
import {DecimalPipe} from '@angular/common';
import {OrderRequestReportSingle} from '@models/order/order-request-report';
import {OrderType} from '@enums/order-type';
import {MbDatePipe} from '@modules/datepicker/mb-date.pipe';
import {UtilConstants} from '@constants/util-constants';
import {AdvancedTableData} from '@models/table-representation/advanced-table-representation';
import {toCamelCase} from '@core/utils';

export function OrderRequestsReportTableData(orderRequestReport: OrderRequestReportSingle): AdvancedTableData[] {
    const tableData: AdvancedTableData[] = [
        {
            groupName: TranslateKey.basicInfo,
            float: 'start',
            data: [
                {
                    title: TranslateKey.securityId,
                    value: orderRequestReport.securityId
                },
                {
                    title: TranslateKey.capName,
                    value: orderRequestReport.capName
                },
                {
                    title: TranslateKey.orderId,
                    value: orderRequestReport.sequenceId
                },
                {
                    title: TranslateKey.previousSequenceId,
                    value: orderRequestReport.previousSequenceId
                },
                {
                    title: TranslateKey.internalSubscriberReference,
                    value: orderRequestReport.internalSubscriberReference
                },
                {
                    title: TranslateKey.orderType,
                    value: toCamelCase(orderRequestReport.type),
                    pipeToken: TranslatePipe
                },
                {
                    title: TranslateKey.orderSide,
                    value: orderRequestReport.side?.toLowerCase(),
                    pipeToken: TranslatePipe
                },
                {
                    title: TranslateKey.price,
                    value: orderRequestReport.price,
                    pipeToken: DecimalPipe
                },
                {
                    title: TranslateKey.disclosedQuantity,
                    value: orderRequestReport.disclosedQuantity,
                    pipeToken: DecimalPipe
                },
                {
                    title: TranslateKey.minimumQuantity,
                    value: orderRequestReport.minimumQuantity,
                    pipeToken: DecimalPipe
                },
                {
                    title: TranslateKey.shareholder,
                    value: orderRequestReport.shareholderId
                },
                {
                    title: TranslateKey.brokerId,
                    value: orderRequestReport.brokerId
                },
                {
                    title: TranslateKey.origin,
                    value: orderRequestReport.originId?.toLowerCase(),
                    pipeToken: TranslatePipe
                },
                {
                    title: TranslateKey.technicalOrigin,
                    value: orderRequestReport.technicalOrigin?.toLowerCase(),
                    pipeToken: TranslatePipe
                },
                {
                    title: TranslateKey.validityQualifierType,
                    value: orderRequestReport.validityQualifier?.toLowerCase(),
                    pipeToken: TranslatePipe
                },
                {
                    title: TranslateKey.validityDate,
                    value: orderRequestReport.validityDate,
                    pipeToken: MbDatePipe,
                    pipeArgs: [UtilConstants.DATE_FORMAT]
                },
                {
                    title: TranslateKey.time,
                    value: orderRequestReport.timestamp,
                    pipeToken: MbDatePipe,
                    pipeArgs: [UtilConstants.DATE_TIME_FORMAT]
                }
            ]
        },
        {
            groupName: TranslateKey.currentStateInfo,
            float: 'end',
            data: [
                {
                    title: TranslateKey.state,
                    value: toCamelCase(orderRequestReport.state),
                    pipeToken: TranslatePipe
                },
                {
                    title: TranslateKey.routeNodeCode,
                    value: orderRequestReport.routeNodeCode
                },
                {
                    title: TranslateKey.orderEntryRuleId,
                    value: orderRequestReport.brokerRequestRuleId
                },
                {
                    title: TranslateKey.mmtpMessageCode,
                    value: orderRequestReport.mmtpMessageCode
                },
                {
                    title: TranslateKey.rejectionCause,
                    value: orderRequestReport.rejectionCause
                },
                {
                    title: TranslateKey.quantity,
                    value: orderRequestReport.quantity,
                    pipeToken: DecimalPipe
                },
                {
                    title: TranslateKey.initialQuantity,
                    value: orderRequestReport.initialQuantity,
                    pipeToken: DecimalPipe
                },
                {
                    title: TranslateKey.expectedRemainingQuantity,
                    value: orderRequestReport.expectedRemainingQuantity,
                    pipeToken: DecimalPipe
                },
                {
                    title: TranslateKey.priorityDateTime,
                    value: orderRequestReport.priorityDateTime,
                    pipeToken: MbDatePipe,
                    pipeArgs: [UtilConstants.EXACT_DATE_TIME_FORMAT]
                }
            ]
        },
        {
            groupName: TranslateKey.clearingData,
            float: 'end',
            data: [
                {
                    title: TranslateKey.giveUpBroker,
                    value: orderRequestReport.clearingData?.giveUpBrokerId
                },
                {
                    title: TranslateKey.traderOrderNumber,
                    value: orderRequestReport.clearingData?.traderOrderNumber
                },
                {
                    title: TranslateKey.brokerOrderEntryDateTime,
                    value: orderRequestReport.clearingData?.brokerOrderEntryDateTime,
                    pipeToken: MbDatePipe,
                    pipeArgs: [UtilConstants.DATE_TIME_FORMAT]
                },
                {
                    title: TranslateKey.trader,
                    value: orderRequestReport.clearingData?.traderId
                },
                {
                    title: TranslateKey.freeText,
                    value: orderRequestReport.clearingData?.freeText
                },
                {
                    title: TranslateKey.bankCode,
                    value: orderRequestReport.clearingData?.brokerBusinessIdentificationCode.bankCode
                },
                {
                    title: TranslateKey.branchCode,
                    value: orderRequestReport.clearingData?.brokerBusinessIdentificationCode.branchCode
                },
                {
                    title: TranslateKey.townCode,
                    value: orderRequestReport.clearingData?.brokerBusinessIdentificationCode.townCode
                }
            ]
        }
    ]

    if (orderRequestReport.type === OrderType.CROSS) {
        const secondaryClearingData: AdvancedTableData =  {
            groupName: TranslateKey.secondaryClearingData,
            float: 'end',
            data: [
                {
                    title: TranslateKey.secondaryShareholderId,
                    value: orderRequestReport.secondaryShareholderId
                },
                {
                    title: TranslateKey.secondaryOrigin,
                    value: orderRequestReport.secondaryOriginId?.toLowerCase(),
                    pipeToken: TranslatePipe
                },
                {
                    title: TranslateKey.giveUpBroker,
                    value: orderRequestReport.secondaryClearingData?.giveUpBrokerId
                },
                {
                    title: TranslateKey.traderOrderNumber,
                    value: orderRequestReport.secondaryClearingData?.traderOrderNumber
                },
                {
                    title: TranslateKey.brokerOrderEntryDateTime,
                    value: orderRequestReport.secondaryClearingData?.brokerOrderEntryDateTime,
                    pipeToken: MbDatePipe,
                    pipeArgs: [UtilConstants.DATE_TIME_FORMAT]
                },
                {
                    title: TranslateKey.trader,
                    value: orderRequestReport.secondaryClearingData?.traderId
                },
                {
                    title: TranslateKey.freeText,
                    value: orderRequestReport.secondaryClearingData?.freeText
                },
                {
                    title: TranslateKey.bankCode,
                    value: orderRequestReport.secondaryClearingData?.brokerBusinessIdentificationCode.bankCode
                },
                {
                    title: TranslateKey.branchCode,
                    value: orderRequestReport.secondaryClearingData?.brokerBusinessIdentificationCode.branchCode
                },
                {
                    title: TranslateKey.townCode,
                    value: orderRequestReport.secondaryClearingData?.brokerBusinessIdentificationCode.townCode
                }
            ]
        };
        tableData.push(secondaryClearingData)
    }

    return tableData;
}
