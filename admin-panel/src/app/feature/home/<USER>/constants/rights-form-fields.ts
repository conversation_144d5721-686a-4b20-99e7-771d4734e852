import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {Valida<PERSON>} from '@angular/forms';
import {CustomValidators} from '@constants/custom-validators';
import {InputMasks} from '@constants/input-masks';
import {CorporateActionAdminCommands} from '@models/corporate-action-admin-commands';
import {FieldsPlaceholder} from '../../../../shared/constants/fields-placeholder';
import {FormFields} from '@modules/dynamic-form/form-field';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';

export function rightsFormFields(commandRequest: CorporateActionAdminCommands.Rights): FormFields {
    const width = 6;

    return [
        new InputFormField({
            formControlName: 'offeredShares',
            label: TranslateKey.offeredShares,
            width,
            validations: [Validators.required, Validators.maxLength(13), CustomValidators.greaterThan(0)],
            mask: InputMasks.COMMA_SEPERATED_NUMBER,
            placeholder: FieldsPlaceholder.SHARES,
            value: commandRequest.inputs.offeredShares
        }),
        new InputFormField({
            formControlName: 'heldShares',
            label: TranslateKey.heldShares,
            width,
            validations: [Validators.required, Validators.maxLength(13), CustomValidators.greaterThan(0)],
            mask: InputMasks.COMMA_SEPERATED_NUMBER,
            placeholder: FieldsPlaceholder.SHARES,
            value: commandRequest.inputs.heldShares
        }),
        new InputFormField({
            formControlName: 'discountedPrice',
            label: TranslateKey.discountedPrice,
            width,
            validations: [Validators.required, Validators.maxLength(13), CustomValidators.greaterThan(0)],
            mask: InputMasks.COMMA_SEPERATED_NUMBER,
            placeholder: FieldsPlaceholder.DECIMAL_NUMBER,
            value: commandRequest.inputs.discountedPrice
        })
    ];
}
