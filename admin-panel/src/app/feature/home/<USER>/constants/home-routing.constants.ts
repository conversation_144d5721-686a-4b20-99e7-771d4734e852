import {RoutingLayout} from '@constants/routing-layout';

export class HomeRoutes {
    static readonly APPLIED_CORPORATE_ACTIONS = 'appliedCorporateActions';
    static readonly ASSOCIATED_SCHEDULE = 'associatedSchedule';
    static readonly BEST_LIMITS = 'bestLimits';
    static readonly BOARDS = 'boards';
    static readonly BROKERS = 'brokers';
    static readonly BROKER_GROUP_ACCESS_MATRIX = 'brokerAccessMatrix';
    static readonly CALENDAR = 'calendar';
    static readonly CANCEL_REPORTS = 'cancelReports';
    static readonly CAPS = 'caps';
    static readonly CAP_GROUPS = 'capGroups';
    static readonly CLEARING_REPORT = 'clearingReport';
    static readonly COMPANIES = 'companies';
    static readonly CORPORATE_ACTIONS = 'corporateActions';
    static readonly FALLOWED_GROUP_STATIC_PRICE_BAND = 'fallowedStaticThresholdInstruments';
    static readonly GROUPS = 'groups';
    static readonly INSTRUMENTS = 'instruments';
    static readonly NOTIFICATIONS = 'notifications';
    static readonly NOTIFY_MARKET_TEMPLATE = 'notifyMarketTemplate';
    static readonly NOT_FALLOWED_GROUP_STATIC_PRICE_BAND = 'notFallowedStaticThresholdInstruments';
    static readonly ORDER_REQUESTS_REPORT = 'orderRequestSReport';
    static readonly ORIGINS = 'origins';
    static readonly ORDER_BOOK = 'orderBook';
    static readonly PRODUCTS = 'products';
    static readonly PRODUCT_TYPES = 'productTypes';
    static readonly PRODUCT_SUBTYPES = 'productSubTypes';
    static readonly QUEUED_COMMANDS = 'queuedCommands';
    static readonly ROUTE_TREE = 'routeTree';
    static readonly ROUTE_PREDICATES = 'routePredicates'
    static readonly RULE_ENGINE = 'ruleEngine';
    static readonly SHAREHOLDER_POSITIONS = 'shareholderPositions';
    static readonly SCHEDULE_TEMPLATES = 'scheduleTemplates'
    static readonly SESSION_SCHEDULES = 'sessionSchedules';
    static readonly SECTORS = 'sectors';
    static readonly SUB_SECTORS = 'subSectors';
    static readonly TOWNS = 'towns';
    static readonly TRADERS = 'traders';
    static readonly TRADER_GROUP_ACCESS_MATRIX = 'traderAccessMatrix';
    static readonly TRADES_INQUIRY = 'tradesInquiry';
    static readonly GROUP_SCHEDULES = 'groupSchedules';
    static readonly SYSTEM_SCHEDULES = 'systemSchedules';
    static readonly GROUP_SESSION_SCHEDULES = 'groupSessionSchedules';
    static readonly INSTRUMENT_SESSION_SCHEDULES = 'instrumentSessionSchedules';
    static readonly SYSTEM_SESSION_SCHEDULES = 'systemSessionSchedules';
    static readonly EVENT_REPORTS = 'eventReports'

    static getFullPath(route: string): string {
        return `${RoutingLayout.HOME}/${route}`;
    }
}
