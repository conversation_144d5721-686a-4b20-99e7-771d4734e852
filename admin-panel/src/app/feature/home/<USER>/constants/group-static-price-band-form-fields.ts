import {Translate<PERSON><PERSON>} from '@enums/translate-key';
import {Validators} from '@angular/forms';
import {InputMasks} from '@constants/input-masks';
import {FieldsPlaceholder} from '../../../../shared/constants/fields-placeholder';
import {FormFields} from '@modules/dynamic-form/form-field';
import {InputFormField} from '@modules/dynamic-form/form-fields/input-field/input-form-field';
import {GroupAdminCommands} from '@models/group-admin-commands';
import {CustomValidators} from '@constants/custom-validators';

export function groupStaticPriceBandFormFields(commandRequest: GroupAdminCommands.ChangePriceBand): FormFields {
    const width = 12;

    return [
        new InputFormField({
            formControlName: 'upperBoundPercentage',
            label: TranslateKey.upperPriceBoundPercentage,
            width,
            validations: [Validators.required, CustomValidators.exclusiveRange(0, 100)],
            mask: InputMasks.PERCENT,
            placeholder: FieldsPlaceholder.PERCENTAGE,
            value: commandRequest.priceBandPercentage.upperBoundPercentage
        }),

        new InputFormField({
            formControlName: 'lowerBoundPercentage',
            label: TranslateKey.lowerPriceBoundPercentage,
            width,
            validations: [Validators.required, CustomValidators.exclusiveRange(0, 100)],
            mask: InputMasks.PERCENT,
            placeholder: FieldsPlaceholder.PERCENTAGE,
            value: commandRequest.priceBandPercentage.lowerBoundPercentage
        })
    ];
}
