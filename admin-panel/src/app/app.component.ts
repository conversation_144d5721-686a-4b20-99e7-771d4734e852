import {Component, OnInit} from '@angular/core';
import {TranslateService} from '@ngx-translate/core';
import {startWith, switchMap, take} from 'rxjs/operators';
import {TranslateKey} from '@shared/enums/translate-key';
import {UtilConstants} from '@constants/util-constants';
import {Language} from '@constants/language';
import {StoreService} from '@shared/services/store.service';
import {SystemDataService} from '@dataServices/system-data.service';
import {interval} from 'rxjs';

@Component({
    selector: 'app-root',
    template: `
        <router-outlet></router-outlet>`,
    standalone: false
})
export class AppComponent implements OnInit {
    readonly translateKeys = TranslateKey;

    constructor(
        private _systemDateDataService: SystemDataService,
        private _translateService: TranslateService
    ) { }

    ngOnInit(): void {
        this._setAppLanguage();
        this._setSystemDate();
    }

    private _setAppLanguage(): void {
        const currentLang = localStorage.getItem(UtilConstants.CURRENT_LANG_KEY) || Language.layouts.EN;

        this._translateService
            .use(currentLang)
            .pipe(take(1))
            .subscribe(() => document.body.dir = Language.direction[currentLang.toUpperCase()]);
    }

    private _setSystemDate(): void {
        interval(UtilConstants.SYSTEM_TIME_UPDATE_INTERVAL)
            .pipe(
                startWith(0),
                switchMap(this._systemDateDataService.getDate)
            )
            .subscribe((resp) => StoreService.systemDateTime = resp);
    }
}
