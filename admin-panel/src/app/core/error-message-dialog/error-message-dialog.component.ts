import {Component, OnInit} from '@angular/core';
import {ErrorMessageDialog} from '@models/error-message-dialog';
import {ApiError} from '@models/api-error';
import {TranslateKey} from '@shared/enums/translate-key';
import {BsModalRef} from 'ngx-bootstrap/modal';

@Component({
    selector: 'app-error-message-dialog',
    templateUrl: './error-message-dialog.component.html',
    standalone: false
})
export class ErrorMessageDialogComponent implements OnInit, ErrorMessageDialog {
    readonly translateKeys = TranslateKey;
    apiError: ApiError;

    constructor(public modalRef: BsModalRef) { }

    ngOnInit(): void {
    }

}
