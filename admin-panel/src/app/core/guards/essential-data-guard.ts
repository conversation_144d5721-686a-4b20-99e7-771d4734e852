import {CanActivateFn} from '@angular/router';
import {InstrumentDataService} from '@dataServices/instrument-data.service';
import {TraderDataService} from '@dataServices/trader-data.service';
import {BrokerDataService} from '@dataServices/broker-data.service';
import {GroupDataService} from '@dataServices/group-data.service';
import {StoreService} from '@shared/services/store.service';
import {plainToInstance} from '../../shared/plain-to-instance/plain-to-instance';
import {forkJoin} from 'rxjs';
import {Group} from '@models/group';
import {Trader} from '@models/trader';
import {inject} from '@angular/core';
import {OriginDataService} from '@dataServices/origin-data.service';
import {Origin} from '@homeModels/origin';
import {Instrument} from '@homeModels/instrument';
import {Broker} from '@homeModels/broker';

export const EssentialDataGuard: CanActivateFn = async () => {
    const instrumentDataService = inject(InstrumentDataService);
    const traderDataService = inject(TraderDataService);
    const brokerDataService = inject(BrokerDataService);
    const groupDataService = inject(GroupDataService);
    const originDataService = inject(OriginDataService)

    const [instruments, groups, brokers, traders, origins] = await forkJoin([
        instrumentDataService.getAllStaticData(),
        groupDataService.getAllGroups(),
        brokerDataService.getAllBrokers(),
        traderDataService.getAllTraders(),
        originDataService.getOrigins()
    ]).toPromise();

    StoreService.instruments = plainToInstance(Instrument.Simple, instruments);
    StoreService.instruments.forEach(item => StoreService.instrumentsObj[item.securityId] = item);

    StoreService.groups = plainToInstance(Group, groups.content);
    StoreService.groups.forEach(group => StoreService.groupsObj[group.code] = group);

    StoreService.brokers = plainToInstance(Broker.Simple, brokers);
    StoreService.brokers.forEach(item => StoreService.brokersObj[item.id] = item);

    StoreService.traders = plainToInstance(Trader, traders.content);
    StoreService.traders.forEach(item => StoreService.tradersObj[item.traderId] = item);

    StoreService.origins = plainToInstance(Origin, origins.content);

    return true;
}

