import {inject} from '@angular/core';
import {CanActivateFn, Router, RouterStateSnapshot} from '@angular/router';
import {LoginDataService} from '@dataServices/login-data.service';
import {TranslateService} from '@ngx-translate/core';
import {UtilConstants} from '@constants/util-constants';

export const AuthGuard: CanActivateFn = (_, state: RouterStateSnapshot) => {
    const router = inject(Router);
    const authService = inject(LoginDataService);
    const translateService = inject(TranslateService);

    if (authService.isAuthenticated()) {
        return true;
    }

    // Store current url so after authing we can move them back to the page they requested
    localStorage.setItem(UtilConstants.REDIRECT_URL_KEY, state.url);

    // Navigate to login page
    router
        .navigate(['/login'])
        .catch(error => {
            console.log(error);

            translateService
                .get('publicErrorWithCode', {errorCode: '002'})
                .subscribe((res: string) => alert(res + '(Error 002)'));
        });

    return false;
}
