import {inject} from '@angular/core';
import {CanActivateFn} from '@angular/router';
import {PermissionDataService} from '@dataServices/permission-data.service';
import {HasPermissionService} from '@directives/has-permission.service';

export const UserPermissionsGuard: CanActivateFn = async () => {
    const permissionDataService = inject(PermissionDataService);

    HasPermissionService.permissions = await permissionDataService
        .getMyPermission()
        .toPromise();

    return true;
}

