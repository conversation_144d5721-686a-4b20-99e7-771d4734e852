import {ComponentFixture, TestBed, waitForAsync} from '@angular/core/testing';
import {UnauthorizedDialogComponent} from './unauthorized-dialog.component';
import {RouterTestingModule} from '@angular/router/testing';
import {ModalHeaderComponent} from '@modules/shared-declarations/modal-header/modal-header.component';
import {provideHttpClientTesting} from '@angular/common/http/testing';
import {TranslateTestingModule} from '../../../translate-testing.module';
import {SharedModule} from '../../shared/shared.module';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {provideHttpClient, withInterceptorsFromDi} from '@angular/common/http';

describe('UnauthorizedDialogComponent', () => {
    let component: UnauthorizedDialogComponent;
    let fixture: ComponentFixture<UnauthorizedDialogComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            declarations: [
                UnauthorizedDialogComponent,
                ModalHeaderComponent
            ],
            imports: [RouterTestingModule,
                TranslateTestingModule,
                SharedModule],
            providers: [BsModalRef, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()]
        })
            .compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(UnauthorizedDialogComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
