<app-modal-header>{{translateKeys.permissionDenied | translate}}</app-modal-header>

<!-- Body -->
<div class="modal-body">
    <alert type="danger" class="d-block mt-3">
        {{permissionDeniedMsg | translate}}
    </alert>
</div>

<div class="modal-footer">
    <button
        *ngIf="isNotHome"
        type="button"
        mat-flat-button
        color="accent"
        (click)="navigateToHome()">
        {{homeTitle| translate}}
    </button>

    <button
        type="button"
        mat-flat-button
        color="primary"
        (click)="closeButton()">
        {{closeButtonTitle | translate}}
    </button>
</div>
