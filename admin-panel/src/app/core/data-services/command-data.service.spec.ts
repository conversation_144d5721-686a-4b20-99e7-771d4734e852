import {CommandDataService} from './command-data.service';
import {ServiceHarness} from '@test/harness/service-harness';
import {AdminCommand} from '@models/admin-command';
import {TestUtils} from '@test/test-utils';

describe('CommandDataService', () => {
    let ha: ServiceHarness<CommandDataService>;

    beforeEach(() => {
        ha = new ServiceHarness(CommandDataService);
    });

    it('should be created', () => {
        expect(ha.service).toBeTruthy();
    });

    it('#getAdminCommandById should retrieve command', () => {
        const adminCommand = TestUtils.getPlainAdminCommands().content[0];
        ha.service.getMyAdminCommandById(adminCommand.commandId).subscribe(resp => {
            expect(resp).toBeInstanceOf(AdminCommand);
            expect(resp.commandId).toEqual(adminCommand.commandId);
        });

        ha.get(`/commands/me/${adminCommand.commandId}`, adminCommand);
    });

    it('#notifyMarket should retrieve AdminCommand', () => {
        ha.service
            .notifyMarket({} as any)
            .subscribe(resp => {
                expect(resp).toBeInstanceOf(AdminCommand);
            });

        ha.post('/commands/market-notification', new AdminCommand());
    });
});
