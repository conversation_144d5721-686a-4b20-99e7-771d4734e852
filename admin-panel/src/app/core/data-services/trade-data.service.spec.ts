import {TradeDataService} from './trade-data.service';
import {ServiceHarness} from '@test/harness/service-harness';
import {TestUtils} from '@test/test-utils';
import {StoreService} from '@shared/services/store.service';
import {Trade} from '@models/trade';
import {AdminCommand} from '@models/admin-command';

describe('TradeDataService', () => {
    let ha: ServiceHarness<TradeDataService>;

    beforeEach(() => {
        ha = new ServiceHarness(TradeDataService);

        StoreService.origins = TestUtils.getOrigins();
        StoreService.instrumentsObj = TestUtils.getInstrumentsObj();
        StoreService.instruments = TestUtils.getInstruments().content;
    });

    it('should be created', () => {
        expect(ha.service).toBeTruthy();
    });

    it('#getTrades should retrieve all trades', () => {
        const trades = TestUtils.getTrades()
        const instrumentId = trades.content[0].buyOrder.securityId;

        ha.service
            .getTrades(instrumentId, {})
            .subscribe(resp => {
                expect(resp.content[0]).toBeInstanceOf(Trade);
                expect(resp.content[0].buyOrder.instrument.securityId).toEqual(instrumentId);
            });

        ha.get(`/securities/${instrumentId}/trades`, trades);
    });

    it('#createTrade should retrieve AdminCommand', () => {
        const instrumentId = 'SPY';

        ha.service
            .createTrade(instrumentId, {} as any)
            .subscribe(resp => {
                expect(resp).toBeInstanceOf(AdminCommand);
            });

        ha.post(`/securities/${instrumentId}/trades`, {});
    });
});
