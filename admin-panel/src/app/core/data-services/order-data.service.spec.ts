import {OrderDataService} from './order-data.service';
import {ServiceHarness} from '@test/harness/service-harness';
import {TestUtils} from '@test/test-utils';
import {InitialOrderBook, OrderBook} from '@models/order/order-book';
import {AdminCommand} from '@models/admin-command';
import {StoreService} from '@shared/services/store.service';

describe('OrderDataService', () => {
    let ha: ServiceHarness<OrderDataService>;

    beforeEach(() => {
        ha = new ServiceHarness(OrderDataService);
        StoreService.origins = TestUtils.getOrigins();
        StoreService.instrumentsObj = TestUtils.getInstrumentsObj();
    });

    it('should be created', () => {
        expect(ha.service).toBeTruthy();
    });

    it('#getCurrentStateOfOrders should retrieve current state of orders', () => {
        const orders = TestUtils.getPlainOrders();
        const securityId = orders.content[0].securityId;

        ha.service
            .getCurrentStateOfOrders(securityId, {})
            .subscribe(resp => {
                expect(resp.content[0]).toBeInstanceOf(OrderBook);
                expect(resp.content[0].instrument.securityId).toEqual(securityId);
            });

        ha.get(`/securities/${securityId}/orders`, orders);
    });

    it('#getInitialStateOfOrders should retrieve initial state of orders', () => {
        const orders = TestUtils.getPlainInitialStateOfOrders();
        const securityId = orders.content[0].securityId;

        ha.service
            .getInitialStateOfOrders(securityId, {})
            .subscribe(resp => {
                expect(resp.content[0]).toBeInstanceOf(InitialOrderBook);
                expect(resp.content[0].instrument.securityId).toEqual(securityId);
            });

        ha.get(`/securities/${securityId}/order-requests`, orders);
    });

    it('#getStatusOfUploadFile should retrieve status of uploaded file', () => {
        const status = TestUtils.getPlainOrderUploadStatus();

        ha.service
            .getStatusOfUploadFile()
            .subscribe(resp => {
                expect(resp).toEqual(status);
            });

        ha.get('/orders/upload', status);
    });

    it('#removeOrder should retrieve AdminCommand', () => {
        const adminCommand = TestUtils.getCommands().content[3];
        const order = TestUtils.getPlainOrders().content[0];
        const securityId = order.securityId;

        ha.service
            .removeOrder(securityId, order)
            .subscribe(resp => {
                expect(resp).toBeInstanceOf(AdminCommand);
            });

        ha.delete(`/securities/${securityId}/orders/${order.sequenceId}?side=BUY`, adminCommand);
    });

    it('#insertOrder should retrieve AdminCommand', () => {
        const adminCommand = TestUtils.getCommands().content[3];
        const securityId = 'TEST';

        ha.service
            .insertOrder(securityId, {} as any)
            .subscribe(resp => {
                expect(resp).toBeInstanceOf(AdminCommand);
            });

        ha.post(`/securities/${securityId}/orders`, adminCommand);
    })
});
