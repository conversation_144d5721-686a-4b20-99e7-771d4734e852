import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {Http} from '@http';
import {OffDay} from '@models/off-day';
import {map} from 'rxjs/operators';
import {plainToInstance} from '../../shared/plain-to-instance/plain-to-instance';
import {Weekend} from '@models/weekend';
import {AdminCommand} from '@models/admin-command';
import {Holiday} from '@models/holiday';
import {Page} from '@models/page';
import {IsWorkingDay} from '@models/is-working-day';
import {toEndpointDateFormat} from '@core/utils';

@Injectable({
    providedIn: 'root'
})
export class CalendarDataService {
    offDays(fromDate: Date, toDate: Date): Observable<OffDay[]> {
        return Http.get<OffDay[]>('/calendar/off-days', {
            params: {from: toEndpointDateFormat(fromDate), to: toEndpointDateFormat(toDate)}
        })
            .pipe(map(resp => plainToInstance(OffDay, resp)))
    }

    offDay(date: Date): Observable<OffDay> {
        return Http.get<OffDay>(`/calendar/off-days/${toEndpointDateFormat(date)}`)
            .pipe(map(resp => plainToInstance(OffDay, resp)))
    }

    getWeekends(): Observable<Weekend[]> {
        return Http.get<Page<Weekend>>('/calendar/weekends')
            .pipe(map(resp => resp.content = plainToInstance(Weekend, resp.content)));
    }

    addWeekends(body: Weekend.Create): Observable<AdminCommand<Weekend>> {
        return Http.post('/calendar/weekends', body);
    }

    deleteWeekends(date: string): Observable<AdminCommand> {
        return Http.delete(`/calendar/weekends/${date}`);
    }

    addHolidays(body: Holiday.Create): Observable<AdminCommand<Holiday>> {
        return Http.post('/calendar/holidays', {holidays: [body]});
    }

    deleteHolidays(date: string): Observable<AdminCommand<Holiday>> {
        return Http.delete(`/calendar/holidays/${date}`);
    }

    updateHolidays(date: string, body: Holiday.Update): Observable<AdminCommand<Holiday>> {
        return Http.put(`/calendar/holidays/${date}`, body);
    }

    isWorkingDay(): Observable<IsWorkingDay> {
        return Http.get('/calendar/is-working-day');
    }
}
