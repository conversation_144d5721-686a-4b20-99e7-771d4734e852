import {EventReportDataService} from './event-report-data.service';
import {TestUtils} from '@test/test-utils';
import {StoreService} from '@shared/services/store.service';
import {ServiceHarness} from '@test/harness/service-harness';

describe('EventReportDataService', () => {
    let ha: ServiceHarness<EventReportDataService>;

    beforeEach(() => {
        ha = new ServiceHarness(EventReportDataService);
        StoreService.instruments = TestUtils.getInstruments().content;
    });

    it('should be created', () => {
        expect(ha.service).toBeTruthy();
    });
});
