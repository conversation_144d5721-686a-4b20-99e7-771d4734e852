import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {map} from 'rxjs/operators';
import {Http} from '@http';
import {plainToInstance} from 'src/app/shared/plain-to-instance/plain-to-instance';
import {RequestOptions} from '@models/request-options';
import {OrderRequestReport, OrderRequestReportSingle} from '@models/order/order-request-report';
import {Page} from '@models/page';

@Injectable({
    providedIn: 'root'
})
export class OrderRequestReportDataService {

    getAllOrders(requestOptions?: RequestOptions): Observable<Page<OrderRequestReport>> {
        return Http.get<Page<OrderRequestReport>>(`/order-request-reports`, requestOptions)
            .pipe(map(resp => {
                resp.content = plainToInstance(OrderRequestReport, resp.content);

                return resp;
            }));
    }

    getOrder(orderId: string): Observable<OrderRequestReportSingle> {
        return Http.get<OrderRequestReportSingle>(`/order-request-reports/${orderId}`)
            .pipe(map<OrderRequestReportSingle, OrderRequestReportSingle>(resp => plainToInstance(OrderRequestReportSingle, resp)));
    }

    getOrderHistory(orderId: string): Observable<OrderRequestReport[]> {
        return Http.get<OrderRequestReport[]>(`/order-request-reports/history/${orderId}`)
            .pipe(map(resp => {
                resp = plainToInstance(OrderRequestReport, resp);
                return resp;
            }));
    }
}
