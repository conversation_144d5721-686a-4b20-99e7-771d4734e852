import {ProductDataService} from './product-data.service';
import {ServiceHarness} from '@test/harness/service-harness';
import {FormGroupRawValue} from '@models/form-group-raw-value';
import {Product} from '@homeModels/product';
import {FormGroup} from '@angular/forms';
import {TestUtils} from '@test/test-utils';
import {CorporateActions} from '@enums/corporate-actions';
import {QueuedCommand} from '@models/queued-command';

describe('ProductDataService', () => {
    let ha: ServiceHarness<ProductDataService>;

    beforeEach(() => {
        ha = new ServiceHarness(ProductDataService);
    });

    it('should be created', () => {
        expect(ha.service).toBeTruthy();
    });

    it('#getAllProducts should retrieve ang object', () => {
        const requestOptions = {
            params:  {
                page: '0',
                size: '25'
            }
        };

        const products = TestUtils.getProducts();

        ha.service.getAllProducts(requestOptions).subscribe(resp => {
            expect(resp.content[0].productId).toEqual(products.content[0].productId);
        });

        ha.get('/products?page=0&size=25', products);
    });

    it('#getAllProducts should add searchExpression field to the response', () => {
        const requestOptions = {
            params:  {
                page: '0',
                size: '25'
            }
        };

        const products = TestUtils.getProducts();
        const product = products.content[0];
        const searchExpression = product.name.fa + product.name.en + product.productCode + product.productId;

        ha.service.getAllProducts(requestOptions).subscribe(resp => {
            expect(resp.content[0].searchExpression).toEqual(searchExpression);
        });

        ha.get('/products?page=0&size=25', products);
    });

    it('#createProduct should retrieve an object', () => {
        const body: FormGroupRawValue<Product.CreateForm> = (new FormGroup<Product.CreateForm>({} as any)).getRawValue();
        ha.service.createProduct(body).subscribe(resp => {
            expect(resp).toBeInstanceOf(Object);
        });

        ha.post('/products', body);
    });

    it('#setCorporateAction should retrieve QueuedCommand after being posted', () => {
        const mockQueuedCommand = TestUtils.getQueuedCommand();
        const productId = 'IRB5GACH0046';
        const actionType = CorporateActions.RIGHTS;
        const body = {}

        ha.service
            .setCorporateAction(productId, actionType, body)
            .subscribe(resp => {
                expect(resp).toEqual(mockQueuedCommand);
                expect(resp).toBeInstanceOf(QueuedCommand);
            });

        ha.post(`/products/${productId}/${actionType}-corporate-action`, mockQueuedCommand);
    });
});
