import {ShareholderDataService} from './shareholder-data.service';
import {ServiceHarness} from '@test/harness/service-harness';
import {TestUtils} from '@test/test-utils';
import {Shareholder} from '@models/shareholder';
import {AdminCommand} from '@models/admin-command';

describe('ShareholderDataService', () => {
    let ha: ServiceHarness<ShareholderDataService>;

    beforeEach(() => {
        ha = new ServiceHarness(ShareholderDataService);
    });

    it('should be created', () => {
        expect(ha.service).toBeTruthy();
    });

    it('#getShareholder should retrieve shareholder data', () => {
        const shareholder = TestUtils.getPlainShareholder();
        const shareholderId = '123456';

        ha.service
            .getShareholder(shareholderId)
            .subscribe(resp => {
                expect(resp.shareholderId).toEqual(shareholderId);
                expect(resp.isBuyBlocked).toEqual(shareholder.isBuyBlocked);
            });

        ha.get(`/shareholders/${shareholderId}`, shareholder);
    });

    it('#blockShareholder should retrieve AdminCommand', () => {
        const adminCommand = {};
        const shareholderId = '123456';

        ha.service
            .blockShareholder(shareholderId, {} as any)
            .subscribe(resp => {
                expect(resp).toBeInstanceOf(AdminCommand);
            });

        ha.post(`/shareholders/${shareholderId}/block`, adminCommand);
    });

    it('#transferShare should retrieve AdminCommand', () => {
        const adminCommand = {};

        ha.service
            .transferShare({} as any)
            .subscribe(resp => {
                expect(resp).toBeInstanceOf(AdminCommand);
            });

        ha.post('/shareholders/transfer', adminCommand);
    });

    it('#positions should retrieve shareholder positions', () => {
        const shareholderPositions = TestUtils.getPlainPositions();
        const shareholderId = '1234';

        ha.service
            .positions(shareholderId)
            .subscribe(resp => {
                expect(resp.content[0]).toBeInstanceOf(Shareholder.Position);
            });

        ha.get(`/shareholders/${shareholderId}/positions`, shareholderPositions);
    })
});
