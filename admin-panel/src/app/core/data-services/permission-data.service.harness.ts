import {Observable, Subject} from 'rxjs';
import {Permission} from '@models/permission';
import {Page} from '@models/page';
import {PermissionDataService} from '@dataServices/permission-data.service';

export const permissionDataServiceHarness: PermissionDataService = {
    getPermissions(): Observable<Page<Permission>> { return new Subject(); },

    removePermission(): Observable<any> { return new Subject(); },

    addPermission(): Observable<Permission> { return new Subject(); },

    editPermission(): Observable<any> { return new Subject(); },

    getMyPermission(): Observable<Permission[]> { return new Subject(); }
}
