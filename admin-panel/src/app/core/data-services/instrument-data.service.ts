import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {Page} from '@models/page';
import {catchError, map} from 'rxjs/operators';
import {Http} from '@http';
import {AdminCommand} from '@models/admin-command';
import {plainToInstance} from '../../shared/plain-to-instance/plain-to-instance';
import {FormGroupRawValue} from '@models/form-group-raw-value';
import {StaticThreshold} from '@models/static-threshold';
import {QueryParams} from '@models/query-params';
import {RequestOptions} from '@models/request-options';
import {BestLimits} from '@models/best-limits';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '@models/api-error';
import {Instrument} from '@homeModels/instrument';

@Injectable({
    providedIn: 'root'
})
export class InstrumentDataService {

    getAllInstruments(requestOptions?: RequestOptions): Observable<Page<Instrument.Simple>> {
        return Http.get<Page<Instrument.Simple>>(`/securities`, requestOptions)
            .pipe(map(resp => {
                resp.content = plainToInstance(Instrument.Simple, resp.content);
                return resp;
            }));
    }

    getDynamicData(securityId: string): Observable<Instrument.DynamicData> {
        return Http.get<Instrument.DynamicData>(`/securities/${securityId}/dynamic`)
            .pipe(map(resp => {
                (resp as any).securityId = securityId;
                return plainToInstance(Instrument.DynamicData, resp);
            }));
    }

    changeStatus(securityId: string, body: Instrument.ChangeStateForm): Observable<AdminCommand> {
        return Http.post(`/securities/${securityId}/state`, body)
            .pipe(map(resp => plainToInstance(AdminCommand, resp)));
    }

    getAllStaticData(): Observable<Instrument.Single[]> {
        return Http.get<Page<Instrument.Single>>(`/securities`)
            .pipe(map(resp => resp.content));
    }

    getStaticData(securityId: string): Observable<Instrument.Single> {
        return Http.get(`/securities/${securityId}`)
            .pipe(map<Instrument.Single, Instrument.Single>(resp => plainToInstance(Instrument.Single, resp)));
    }

    updatePercentageStaticThreshold(securityId: string, body: FormGroupRawValue<StaticThreshold.PercentageForm>): Observable<AdminCommand> {
        return Http.post(`/securities/${securityId}/static-threshold/percentage`, body)
            .pipe(map(resp => plainToInstance(AdminCommand, resp)));
    }

    updateStaticPriceBand(securityId: string, body: FormGroupRawValue<StaticThreshold.staticPriceBandForm>): Observable<AdminCommand> {
        return Http.post(`/securities/${securityId}/static-threshold/price-band`, body)
            .pipe(map(resp => plainToInstance(AdminCommand, resp)));
    }

    updateReferencePrice(securityId: string, body: FormGroupRawValue<StaticThreshold.ReferencePriceForm>): Observable<AdminCommand> {
        return Http.post(`/securities/${securityId}/reference-price`, body)
            .pipe(map(resp => plainToInstance(AdminCommand, resp)));
    }

    deleteInstrument(securityId: string): Observable<any> {
        return Http.delete(`/securities/${securityId}`);
    }

    editInstrument(securityId: string, body): Observable<AdminCommand> {
        return Http.put(`/securities/${securityId}`, body)
            .pipe(map(resp => plainToInstance(AdminCommand, resp)));
    }

    editQueueInstrument(securityId: string, body): Observable<any> {
        return Http.put(`/securities/${securityId}/queued`, body);
    }

    addInstrument(body): Observable<any> {
        return Http.post(`/securities`, body);
    }

    updateInstrumentGroup(securityId: string, body: FormGroupRawValue<Instrument.ChangeGroupForm>): Observable<AdminCommand> {
        return Http.post(`/securities/${securityId}/group`, body)
            .pipe(map(resp => plainToInstance(AdminCommand, resp)));
    }

    purgeOrders(securityId: string, queryParams: QueryParams): Observable<AdminCommand> {
        return Http.delete(`/securities/${securityId}/orders`, {params: queryParams})
    }

    declareRemainingOrders(): Observable<any> {
        return Http.post(`/securities/declare-remaining-orders-for-aramis`, null);
    }

    getDeclareRemainingOrdersStatus(): Observable<boolean> {
        return Http.get(`/securities/declare-remaining-orders-for-aramis-status`);
    }

    getInstrumentBesLimits(securityId: string): Observable<BestLimits> {
        return Http.get(`/securities/${securityId}/best-limits`)
            .pipe(map(resp => plainToInstance(BestLimits, resp)));
    }

    toggleAllowShortSell(securityId: string, body: Instrument.ChangeShortSellStateForm): Observable<AdminCommand> {
        return Http.put(`/securities/${securityId}/allow-short-sell`, body);
    }

    uploadAllowShortSell(file: File): Observable<AdminCommand> {
        const formData = new FormData();
        formData.append('file', file);

        return Http.put<any>(`/securities/batch-allow-short-sell`, formData)
            .pipe(
                catchError((error: HttpErrorResponse) => {
                    throw plainToInstance(ApiError, error.error);
                }),
                map(resp => plainToInstance(AdminCommand, resp))
            );
    }

    uploadReferencePrices(file: File): Observable<any> {
        const formData = new FormData();
        formData.append('file', file);

        return Http.post<any>(`/securities/batch-reference-price`, formData)
            .pipe(
                catchError((error: HttpErrorResponse) => {
                    throw plainToInstance(ApiError, error.error);
                }),
                map(resp => plainToInstance(AdminCommand, resp))
            );
    }

    uploadStaticThresholdPercentage(file: File): Observable<any> {
        const formData = new FormData();
        formData.append('file', file);

        return Http.post<any>(`/securities/batch-static-threshold/percentage`, formData)
            .pipe(
                catchError((error: HttpErrorResponse) => {
                    throw plainToInstance(ApiError, error.error);
                }),
                map(resp => plainToInstance(AdminCommand, resp))
            );
    }

    uploadStaticThresholdAbsolute(file: File): Observable<any> {
        const formData = new FormData();
        formData.append('file', file);

        return Http.post<any>(`/securities/batch-static-threshold/absolute`, formData)
            .pipe(
                catchError((error: HttpErrorResponse) => {
                    throw plainToInstance(ApiError, error.error);
                }),
                map(resp => plainToInstance(AdminCommand, resp))
            );
    }

    uploadImmediateUpdate(file: File): Observable<any> {
        const formData = new FormData();
        formData.append('file', file);

        return Http.put<any>(`/securities/batch`, formData)
            .pipe(
                catchError((error: HttpErrorResponse) => {
                    throw plainToInstance(ApiError, error.error);
                }),
                map(resp => plainToInstance(AdminCommand, resp))
            );
    }

    uploadState(file: File): Observable<any> {
        const formData = new FormData();
        formData.append('file', file);

        return Http.post<any>(`/securities/batch-state`, formData)
            .pipe(
                catchError((error: HttpErrorResponse) => {
                    throw plainToInstance(ApiError, error.error);
                }),
                map(resp => plainToInstance(AdminCommand, resp))
            );
    }
}
