import {BrokerDataService} from './broker-data.service';
import {Subject} from 'rxjs';

export const brokerDataServiceHarness: Pick<BrokerDataService, 'setCredit' | 'toggleBlock' | 'getBrokersPage' | 'getAllBrokers'> = {
    setCredit() { return new Subject(); },

    toggleBlock() { return new Subject(); },

    getBrokersPage() { return new Subject(); },

    getAllBrokers() { return new Subject(); }
}
