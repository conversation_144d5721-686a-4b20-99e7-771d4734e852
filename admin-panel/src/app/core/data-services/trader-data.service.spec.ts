import {TraderDataService} from './trader-data.service';
import {ServiceHarness} from '@test/harness/service-harness';
import {TestUtils} from '@test/test-utils';
import {Trader} from '@models/trader';
import {FormGroupRawValue} from '@models/form-group-raw-value';
import {FormGroup} from '@angular/forms';

describe('TraderDataService', () => {

    let ha: ServiceHarness<TraderDataService>;

    beforeEach(() => {
        ha = new ServiceHarness(TraderDataService);
    });

    it('should be created', () => {
        expect(ha.service).toBeTruthy();
    });

    it('#getTrader should retrieve Trader', () => {
        const trader = TestUtils.getPlainTrader();
        const traderId = trader.traderId;

        ha.service
            .getTrader(traderId)
            .subscribe(resp => {
                expect(resp).toBeInstanceOf(Trader);
                expect(resp.traderId).toEqual(traderId);
            });

        ha.get(`/traders/${traderId}`, trader);
    });

    it('#addTrader should retrieve an object', () => {
        const body: FormGroupRawValue<Trader.Form> = (new FormGroup<Trader.Form>({} as any)).getRawValue();
        ha.service.addTrader(body).subscribe(resp => {
            expect(resp).toBeInstanceOf(Object);
        });

        ha.post('/traders', body);
    });
});
