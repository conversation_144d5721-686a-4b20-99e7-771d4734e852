import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {Http} from '@http';
import {Page} from '@models/page';
import {map} from 'rxjs/operators';
import {CreateRuleEngineDto} from '@models/create-rule-engine-dto';
import {AdminCommand} from '@models/admin-command';
import {Rule} from '@models/rule';
import {plainToInstance} from '../../shared/plain-to-instance/plain-to-instance';
import {RequestOptions} from '@models/request-options';

@Injectable({
    providedIn: 'root'
})
export class RuleEngineDataService {
    getAllRules(requestOptions: RequestOptions): Observable<Page<Rule>> {
        return Http.get<Page<Rule>>('/broker-requests/rules', requestOptions)
            .pipe(map(resp => {
                resp.content = plainToInstance(Rule, resp.content);

                return resp;
            }));
    }

    addRule(createRuleEngineDto: CreateRuleEngineDto): Observable<AdminCommand> {
        return Http.post('/broker-requests/rules', createRuleEngineDto)
            .pipe(map(resp => plainToInstance(AdminCommand, resp)));
    }

    updateRule(ruleId: string, createRuleEngineDto: CreateRuleEngineDto): Observable<AdminCommand> {
        return Http.put(`/broker-requests/rules/${ruleId}`, createRuleEngineDto)
            .pipe(map(resp => plainToInstance(AdminCommand, resp)));
    }

    deleteRule(jobId: string): Observable<AdminCommand> {
        return Http.delete(`/broker-requests/rules/${jobId}`)
            .pipe(map(resp => plainToInstance(AdminCommand, resp)));
    }

    resendRules(): Observable<null> {
        return Http.put(`/broker-requests/rules/resend`, null);
    }

    deleteAllRules(): Observable<AdminCommand> {
        return Http.delete(`/broker-requests/rules`)
            .pipe(map(resp => plainToInstance(AdminCommand, resp)));
    }
}
