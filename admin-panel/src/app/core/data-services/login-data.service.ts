import {Injectable} from '@angular/core';
import {Observable, Subject} from 'rxjs';
import {StartAssertion, UserCredential} from '@models/user-credential';
import {Auth} from '@models/auth';
import {Http} from '@http';
import {UtilConstants} from '@constants/util-constants';
import {map, switchMap} from 'rxjs/operators';
import {environment} from '../../../environments/environment';
import {base64urlToUint8array, uint8arrayToBase64url} from '@core/utils';

@Injectable({
    providedIn: 'root'
})
export class LoginDataService {
    startAssertion(userCredential: UserCredential): Observable<Auth> {
        return Http.post(`/users/start-assertion`, userCredential.username).pipe(
            map(this._toCredentialRequestOptions),
            switchMap(credentialGetOptions => {
                delete credentialGetOptions.publicKey.extensions.appid;
                return this._getCredential(credentialGetOptions);
            }),
            switchMap(encodedResult => {
                const body = {
                    username: userCredential.username,
                    password: userCredential.password,
                    credential: encodedResult.credential
                };
                return this._login(body);
            })
        );
    }

    logout(): Observable<Auth> {
        localStorage.removeItem(UtilConstants.REDIRECT_URL_KEY);
        return Http.post<Auth>('/logout', null);
    }

    isAuthenticated(): boolean {
        return (environment as any).isNoAuth || !!this._getAuthToken();
    }

    invalidateAuthToken(): void {
        localStorage.removeItem(UtilConstants.AUTH_USER_KEY);
        localStorage.removeItem(UtilConstants.USER_INFO_KEY);
    }

    storeAuthToken(auth: Auth): void {
        localStorage.setItem(UtilConstants.AUTH_USER_KEY, auth.username);
        localStorage.setItem(UtilConstants.USER_INFO_KEY, JSON.stringify(auth));
    }

    getUserInfo(): Auth {
        const userInfo = localStorage.getItem(UtilConstants.USER_INFO_KEY);

        return userInfo ? JSON.parse(userInfo) : null;
    }

    private _getAuthToken() {
        return localStorage.getItem(UtilConstants.AUTH_USER_KEY);
    }

    private _toCredentialRequestOptions(resp: StartAssertion): CredentialRequestOptions {
        const requestOptions = resp.publicKeyCredentialRequestOptions;

        return requestOptions.allowCredentials.length ?
            {
                publicKey: {
                    allowCredentials: requestOptions.allowCredentials
                        && requestOptions.allowCredentials.map(credential => ({
                            ...credential,
                            id: base64urlToUint8array(credential.id),
                            transports: []
                        })),
                    challenge: base64urlToUint8array(requestOptions.challenge),
                    extensions: requestOptions.extensions,
                }
            } :
            {};
    }

    private _getCredential(credentialOptions: CredentialRequestOptions): Observable<any> {
        const subject = new Subject();

        navigator.credentials.get(credentialOptions)
            .then((publicKeyCredential: PublicKeyCredential) => {
                const response = publicKeyCredential.response;

                return {
                    credential: {
                        type: publicKeyCredential.type,
                        id: publicKeyCredential.id,
                        response: {
                            clientDataJSON: uint8arrayToBase64url(new Uint8Array(response['clientDataJSON'])),
                            authenticatorData: uint8arrayToBase64url((new Uint8Array(response['authenticatorData']))),
                            signature: uint8arrayToBase64url(new Uint8Array(response['signature'])),
                            userHandle: response['userHandle'] && uint8arrayToBase64url(response['userHandle']),
                        },
                        clientExtensionResults: publicKeyCredential.getClientExtensionResults()
                    }
                }
            })
            .then((encodedResult) => subject.next(encodedResult));

        return subject;
    }

    private _login(body: UserCredential): Observable<Auth> {
        return Http.post<Auth>('/login', body);
    }
}
