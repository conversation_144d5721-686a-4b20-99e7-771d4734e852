import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {Http} from '@http';
import {Page} from '@models/page';
import {Board} from '@models/board';
import {map} from 'rxjs/operators';
import {plainToInstance} from '../../shared/plain-to-instance/plain-to-instance';
import {RequestOptions} from '@models/request-options';
import {FormGroupRawValue} from '@models/form-group-raw-value';
import {AdminCommand} from '@models/admin-command';

@Injectable({
  providedIn: 'root'
})
export class BoardDataService {
    getBoards(requestOptions?: RequestOptions): Observable<Page<Board>> {
        return Http.get<Page<Board>>('/boards', requestOptions)
            .pipe(map(resp => {
                resp.content = plainToInstance(Board, resp.content);
                return resp;
        }));
    }

    createBoard(body: FormGroupRawValue<Board.Create>): Observable<AdminCommand> {
        return Http.post<AdminCommand>('/boards', body);
    }

    updateBoard(boardCode: string, body: FormGroupRawValue<Board.Create>): Observable<AdminCommand> {
        return Http.put<AdminCommand>(`/boards/${boardCode}`, body);
    }

    deleteBoard(boardCode: string): Observable<AdminCommand> {
        return Http.delete<AdminCommand>(`/boards/${boardCode}`);
    }

    getBoardQueuedProducts(boardCode: string): Observable<string[]> {
        return Http.get(`/boards/${boardCode}/queued-products`);
    }
}
