import {Subject} from 'rxjs';
import {ScheduleDataService} from '@dataServices/schedule-data.service';

export const scheduleDataServiceHarness: ScheduleDataService = {
    getGroupSchedules() { return new Subject(); },

    deleteGroupSchedule() { return new Subject(); },

    getSystemSchedules() { return new Subject(); },

    deleteSystemSchedule() { return new Subject(); },

    getGroupSessionSchedules() { return new Subject(); },

    getInstrumentSessionSchedules() { return new Subject(); },

    getSystemSessionSchedules() { return new Subject(); },

    setScheduleOnBatchGroup() { return new Subject(); },

    setScheduleOnSystem() { return new Subject(); },

    updateGroupSchedule() { return new Subject(); },

    updateSystemSchedule() { return new Subject(); },

    updateGroupSessionSchedule() { return new Subject(); },

    updateDeferredInstrumentSchedule() { return new Subject(); },

    uploadDeferredInstrumentSchedule() { return new Subject(); },

    deleteDeferredInstrumentSchedule() { return new Subject(); },

    updateSystemSessionSchedule() { return new Subject(); }

}
