import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {Http} from '@http';
import {SystemState} from '@models/system-state';
import {map} from 'rxjs/operators';
import {AdminCommand} from '@models/admin-command';
import {plainToInstance} from '../../shared/plain-to-instance/plain-to-instance';

@Injectable({
  providedIn: 'root'
})
export class SystemStateDataService {
    getCurrentState(): Observable<SystemState> {
        return Http.get(`/system/state`)
            .pipe(map(resp => plainToInstance(SystemState, resp)));
    }

    updateSystemState(targetState: SystemState.State): Observable<AdminCommand> {
        return Http.post(`/system/state/${targetState}`, null)
            .pipe(map(resp => plainToInstance(AdminCommand, resp)));
    }

    getSystemStateChecking(): Observable<boolean> {
        return Http.get(`/system/state/checking`);
    }

    updateSystemStateStatus(status: boolean): Observable<null> {
        return Http.post(`/system/state/checking/${status ? 'enable' : 'disable'}`, null);
    }
}
