import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {Http} from '@http';
import {Page} from '@models/page';
import {Sector} from '@models/sector';
import {map} from 'rxjs/operators';
import {SubSector} from '@models/sub-sector';
import {RequestOptions} from '@models/request-options';
import {FormGroupRawValue} from '@models/form-group-raw-value';
import {AdminCommand} from '@models/admin-command';
import {plainToInstance} from '@shared/plain-to-instance';

@Injectable({
    providedIn: 'root'
})
export class SectorDataService {
    getAllSectors(requestOptions?: RequestOptions): Observable<Page<Sector>> {
        return Http.get<Page<Sector>>('/sectors', requestOptions)
            .pipe(map(resp => {
                resp.content = plainToInstance(Sector, resp.content);
                return resp;
            }));
    }

    createSector(body: FormGroupRawValue<Sector.Create>): Observable<AdminCommand> {
        return Http.post<AdminCommand>('/sectors', body);
    }

    updateSector(sectorCode: string, body: FormGroupRawValue<Sector.Create>): Observable<AdminCommand> {
        return Http.put<AdminCommand>(`/sectors/${sectorCode}`, body);
    }

    deleteSector(sectorCode: string): Observable<AdminCommand> {
        return Http.delete<AdminCommand>(`/sectors/${sectorCode}`);
    }

    getSubSectors(sectorCode: string): Observable<Page<SubSector>> {
        return Http.get<Page<SubSector>>(`/sub-sectors`, { params: {sectorCode} })
            .pipe(map(resp => {
                resp.content = plainToInstance(SubSector, resp.content);
                return resp;
            }));
    }

    getAllSubSectors(requestOptions?: RequestOptions): Observable<Page<SubSector>> {
        return Http.get<Page<SubSector>>(`/sub-sectors`, requestOptions)
            .pipe(map(resp => {
                resp.content = plainToInstance(SubSector, resp.content);
                return resp;
            }));
    }

    createSubSector(body: FormGroupRawValue<SubSector.Create>): Observable<AdminCommand> {
        return Http.post<AdminCommand>('/sub-sectors', body);
    }

    updateSubSector(subSectorCode: string, body: FormGroupRawValue<SubSector.Update>): Observable<AdminCommand> {
        return Http.put<AdminCommand>(`/sub-sectors/${subSectorCode}`, body);
    }

    deleteSubSector(subSectorCode: string): Observable<AdminCommand> {
        return Http.delete<AdminCommand>(`/sub-sectors/${subSectorCode}`);
    }
}
