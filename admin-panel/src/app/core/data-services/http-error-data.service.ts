import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {Http} from '@http';
import {Page} from '@models/page';
import {HttpError} from '@models/http-error';
import {map} from 'rxjs/operators';
import {plainToInstance} from 'src/app/shared/plain-to-instance/plain-to-instance';

@Injectable({
    providedIn: 'root'
})
export class HttpErrorDataService {
    getErrors(): Observable<HttpError[]> {
        return Http.get<Page<HttpError>>('/errors')
            .pipe(map(resp => plainToInstance(HttpError, resp.content)));
    }
}
