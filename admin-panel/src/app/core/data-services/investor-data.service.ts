import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {Http} from '@http';
import {RequestOptions} from '@models/request-options';
import {Investor} from '@models/investor';
import {map} from 'rxjs/operators';
import {AdminCommand} from '@models/admin-command';
import {plainToInstance} from '../../shared/plain-to-instance/plain-to-instance';
import {QueryParams} from '@models/query-params';

@Injectable({
    providedIn: 'root'
})
export class InvestorDataService {

    getInvestor(investorId: string, options?: RequestOptions): Observable<Investor> {
        return Http.get<Investor>(`/investors/${investorId}`, options)
            .pipe(map(resp => {
                resp.investorId = investorId;
                return plainToInstance(Investor, resp);
            }));
    }

    blockInvestor(investorId: string, investorBlockDto: Investor.Block): Observable<AdminCommand> {
        return Http.post<AdminCommand>(`/investors/${investorId}/block`, investorBlockDto)
            .pipe(map(resp => plainToInstance(AdminCommand, resp)));
    }

    purgeOrders(investorId: string, queryParams: QueryParams): Observable<AdminCommand> {
        return Http.delete(`/investors/${investorId}/orders`, {params: queryParams})
    }
}
