import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {Page} from '@models/page';
import {Http} from '@http';
import {map} from 'rxjs/operators';
import {plainToInstance} from '../../shared/plain-to-instance/plain-to-instance';
import {AdminCommand} from '@models/admin-command';
import {RequestOptions} from '@models/request-options';
import {Origin} from '@homeModels/origin';

@Injectable({
    providedIn: 'root'
})
export class OriginDataService {

    getOrigins(requestOptions?: RequestOptions): Observable<Page<Origin>> {
        return Http.get<Page<Origin>>('/origins', requestOptions)
            .pipe(map(resp => {
                resp.content = plainToInstance(Origin, resp.content);
                return resp;
            }));
    }

    updatePriority(originId: string, body: Origin.UpdatePriorityForm): Observable<AdminCommand> {
        return Http.put(`/origins/${originId}`, body);
    }
}
