import {ChangePasswordDataService} from './change-password-data.service';
import {ChangePasswordDto} from '@models/change-password';
import {ServiceHarness} from '@test/harness/service-harness';

describe('ChangePasswordDataService', () => {
    let ha: ServiceHarness<ChangePasswordDataService>;

    beforeEach(() => {
        ha = new ServiceHarness(ChangePasswordDataService);
    });

    it('should be created', () => {
        expect(ha.service).toBeTruthy();
    });

    it('#changePassword should update the password', () => {
        const testData = new ChangePasswordDto({} as any);

        ha.service
            .changePassword(testData)
            .subscribe(resp => expect(resp).toEqual(null));

        ha.put('/users/me/password', null);
    });
});
