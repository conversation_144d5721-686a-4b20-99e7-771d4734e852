import {BrokerDataService} from './broker-data.service';
import {TestUtils} from '@test/test-utils';
import {ServiceHarness} from '@test/harness/service-harness';

describe('BrokerDataService', () => {
    let ha: ServiceHarness<BrokerDataService>;

    beforeEach(() => {
        ha = new ServiceHarness(BrokerDataService);
    });

    it('should be created', () => {
        expect(ha.service).toBeTruthy();
    });

    it('#getAllBrokers should return all Brokers', () => {
        const mockData = TestUtils.getBrokers();

        ha.service
            .getAllBrokers()
            .subscribe(resp => {
                expect(resp).toEqual(mockData.content);
            });

        ha.get('/brokers', mockData);
    });
});
