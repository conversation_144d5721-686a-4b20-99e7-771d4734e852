import {CompanyDataService} from './company-data.service';
import {ServiceHarness} from '@test/harness/service-harness';
import {TestUtils} from '@test/test-utils';
import {Company} from '@models/company';
import {FormGroup} from '@angular/forms';
import {FormGroupRawValue} from '@models/form-group-raw-value';
import {Http} from '@http';
import {TestBed} from '@angular/core/testing';
import {HttpClient} from '@angular/common/http';

describe('CompanyDataService', () => {
    let ha: ServiceHarness<CompanyDataService>;

    beforeEach(() => {
        ha = new ServiceHarness(CompanyDataService);

        Http.httpClient = TestBed.inject(HttpClient);
    });

    it('should be created', () => {
        expect(ha.service).toBeTruthy();
    });

    it('#getAllCompanies should retrieve companies', () => {
        const companies = TestUtils.getPlainCompanies();

        ha.service
            .getAllCompanies({})
            .subscribe(resp => {
                expect(resp.content[0]).toBeInstanceOf(Company);
            });

        ha.get(`/companies`, companies);
    });

    it('#addCompany should add an object', () => {
        const body: FormGroupRawValue<Company.NewForm> = (new FormGroup<Company.NewForm>({} as any)).getRawValue();
        ha.service.addCompany(body).subscribe(resp => {
            expect(resp).toBeInstanceOf(Object);
        });

        ha.post('/companies', body);
    });
});
