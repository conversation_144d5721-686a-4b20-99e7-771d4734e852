import {InitializationDataService} from './initialization-data.service';
import {ServiceHarness} from '@test/harness/service-harness';
import {TestUtils} from '@test/test-utils';
import {AdminCommand} from '@models/admin-command';

describe('InitializationDataService', () => {
    let ha: ServiceHarness<InitializationDataService>;

    beforeEach(() => {
        ha = new ServiceHarness(InitializationDataService);
    });

    it('should be created', () => {
        expect(ha.service).toBeTruthy();
    });

    it('#ShouldInitializeMarket should retrieve a boolean', () => {
        ha.service
            .ShouldInitializeMarket()
            .subscribe(resp => {
                expect(resp).toBeInstanceOf(Boolean);
                expect(resp).toBeTruthy();
            });

        ha.get(`/initialization`, true);
    });

    it('#startMarket should retrieve AdminCommand', () => {
        const adminCommand = TestUtils.getStartMarketCommand();

        ha.service
            .startMarket()
            .subscribe(resp => {
                expect(resp).toBeInstanceOf(AdminCommand);
            });

        ha.post(`/initialization`, adminCommand);
    });
});
