import {Injectable} from '@angular/core';
import {RequestOptions} from '@models/request-options';
import {Observable} from 'rxjs';
import {Http} from '@http';
import {map} from 'rxjs/operators';
import {plainToInstance} from '../../shared/plain-to-instance/plain-to-instance';
import {Trader} from '@models/trader';
import {FormGroupRawValue} from '@models/form-group-raw-value';
import {QueryParams} from '@models/query-params';
import {AdminCommand} from '@models/admin-command';
import {Page} from '@models/page';

@Injectable({
    providedIn: 'root'
})
export class TraderDataService {

    getAllTraders(requestOptions?: RequestOptions): Observable<Page<Trader>> {
        return Http.get<Page<Trader>>(`/traders`, requestOptions)
            .pipe(map(resp => {
                resp.content = plainToInstance(Trader, resp.content);
                return resp;
            }));
    }

    getTrader(traderId: string, options?: RequestOptions): Observable<Trader> {
        return Http.get<Trader>(`/traders/${traderId}`, options)
            .pipe(map(resp => {
                resp.traderId = traderId;
                return plainToInstance(Trader, resp);
            }));
    }

    addTrader(body: FormGroupRawValue<Trader.Form>): Observable<AdminCommand> {
        return Http.post('/traders', body);
    }

    deleteTrader(traderId: string): Observable<AdminCommand> {
        return Http.delete(`/traders/${traderId}`);
    }

    purgeOrders(traderId: string, queryParams: QueryParams): Observable<AdminCommand> {
        return Http.delete(`/traders/${traderId}/orders`, {params: queryParams})
    }
}
