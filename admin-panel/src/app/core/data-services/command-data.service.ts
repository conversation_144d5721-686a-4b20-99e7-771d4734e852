import {Injectable} from '@angular/core';
import {empty, Observable} from 'rxjs';
import {Http} from '@http';
import {Page} from '@models/page';
import {delay, expand, filter, map} from 'rxjs/operators';
import {AdminCommand} from '@models/admin-command';
import {plainToInstance} from '../../shared/plain-to-instance/plain-to-instance';
import {RequestOptions} from '@models/request-options';
import {FormGroupRawValue} from '@models/form-group-raw-value';
import {NotifyMarket} from '@homeModels/notify-market';

@Injectable({
    providedIn: 'root'
})
export class CommandDataService {
    getMyCommands(requestOptions: RequestOptions): Observable<Page<AdminCommand>> {
        return Http.get<Page<AdminCommand>>('/commands/me', requestOptions)
            .pipe(map(resp => {
                resp.content = plainToInstance(AdminCommand, resp.content);

                return resp;
            }));
    }

    getCommands(requestOptions: RequestOptions): Observable<Page<AdminCommand>> {
        return Http.get<Page<AdminCommand>>('/commands', requestOptions)
            .pipe(map(resp => {
                resp.content = plainToInstance(AdminCommand, resp.content);

                return resp;
            }));
    }

    getAdminCommandRecursiveById(commandId: number): Observable<AdminCommand> {
        const INITIAL_DELAY = 250;
        const GROWTH_RATE_OF_DELAY = 2;
        let delayTime = INITIAL_DELAY;

        return this.getMyAdminCommandById(commandId)
            .pipe(
                expand(resp => {
                    if (!resp.isProgress) {
                        return empty()
                    } else {
                        delayTime *= GROWTH_RATE_OF_DELAY;
                        if (delayTime > 10000) {
                            delayTime = 10000;
                        }
                        return this._getAdminCommandByIdWithDelay(commandId, delayTime);
                    }
                }),
                filter(resp => !resp.isProgress)
            );
    }

    getMyAdminCommandById(commandId: number): Observable<AdminCommand> {
        return Http.get<AdminCommand>(`/commands/me/${commandId}`)
            .pipe(map(resp => plainToInstance(AdminCommand, resp)));
    }

    notifyMarket(body: FormGroupRawValue<NotifyMarket.CreateMessage>): Observable<AdminCommand> {
        return Http.post<AdminCommand>(`/commands/market-notification`, body);
    }

    private _getAdminCommandByIdWithDelay(commandId: number, delayTime: number): Observable<AdminCommand> {
        return this.getMyAdminCommandById(commandId)
            .pipe(delay(delayTime))
    }
}
