import {SectorDataService} from './sector-data.service';
import {ServiceHarness} from '@test/harness/service-harness';
import {TestUtils} from '@test/test-utils';

describe('SectorDataService', () => {
    let ha: ServiceHarness<SectorDataService>;

    beforeEach(() => {
        ha = new ServiceHarness(SectorDataService);
    });

    it('should be created', () => {
        expect(ha.service).toBeTruthy();
    });

    it('#getAllSectors should retrieve all sectors', () => {
        const sectors = TestUtils.getSectors();
        ha.service.getAllSectors().subscribe(resp => {
            expect(resp.content[0].code).toEqual(sectors.content[0].code)
        });

        ha.get('/sectors', sectors);
    });
});
