import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {Page} from '@models/page';
import {catchError, map} from 'rxjs/operators';
import {UploadOrderStatus} from '@models/upload';
import {QueryParams} from '@models/query-params';
import {InitialOrderBook, OrderBook} from '@models/order/order-book';
import {Http} from '@http';
import {AdminCommand} from '@models/admin-command';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '@models/api-error';
import {plainToInstance} from 'src/app/shared/plain-to-instance/plain-to-instance';
import {Order} from '@models/order/order';
import {RequestOptions} from '@models/request-options';
import {OrderBookStatistics} from '@models/order/order-book-statistics';

@Injectable({
    providedIn: 'root'
})
export class OrderDataService {

    insertOrder(securityId: string, reqBody: Order): Observable<AdminCommand> {
        return Http.post(`/securities/${securityId}/orders`, reqBody)
            .pipe(map(resp => plainToInstance(AdminCommand, resp)))
    }

    getCurrentStateOfOrders(securityId: string, params: QueryParams): Observable<Page<OrderBook>> {
        return Http.get<Page<OrderBook>>(`/securities/${securityId}/orders`, {params})
            .pipe(map(resp => {
                resp.content = plainToInstance(OrderBook, resp.content);

                return resp;
            }));
    }

    getInitialStateOfOrders(securityId: string, requestOptions: RequestOptions): Observable<Page<InitialOrderBook>> {
        return Http.get<Page<InitialOrderBook>>(`/securities/${securityId}/order-requests`, requestOptions)
            .pipe(map(resp => {
                resp.content = plainToInstance(InitialOrderBook, resp.content);

                return resp;
            }));
    }

    uploadFile(file: File): Observable<UploadOrderStatus> {
        const formData = new FormData();
        formData.append('file', file);

        return Http.post<UploadOrderStatus>('/orders/upload', formData)
            .pipe(catchError((error: HttpErrorResponse) => {
                throw plainToInstance(ApiError, error.error);
            }));
    }

    getStatusOfUploadFile(): Observable<UploadOrderStatus> {
        return Http.get<UploadOrderStatus>('/orders/upload');
    }

    removeOrder(securityId: string, order: OrderBook): Observable<AdminCommand> {
        return Http.delete(`/securities/${securityId}/orders/${order.sequenceId}`, {params: {side: order.side}})
            .pipe(map(resp => plainToInstance(AdminCommand, resp)))
    }

    editOrder(securityId: string, orderId: number, order: Order): Observable<any> {
        return Http.put(`/securities/${securityId}/orders/${orderId}`, order, {
            params: {
                side: order.side
            }
        })
    }

    purgeOrders(queryParams: QueryParams): Observable<AdminCommand> {
        return Http.delete(`/orders`, {params: queryParams})
            .pipe(map(resp => plainToInstance(AdminCommand, resp)));
    }

    getStatistics(securityId: string): Observable<OrderBookStatistics> {
        return Http.get<OrderBookStatistics>(`/securities/${securityId}/orders/statistics`)
            .pipe(map(resp => plainToInstance(OrderBookStatistics, resp)));
    }
}
