import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {Page} from '@models/page';
import {Group} from '@models/group';
import {Http} from '@http';
import {catchError, map} from 'rxjs/operators';
import {ApiError} from '@models/api-error';
import {HttpErrorResponse} from '@angular/common/http';
import {plainToInstance} from '../../shared/plain-to-instance/plain-to-instance';
import {AdminCommand} from '@models/admin-command';
import {QueryParams} from '@models/query-params';
import {FormGroupRawValue} from '@models/form-group-raw-value';
import {StaticThreshold} from '@models/static-threshold';
import {CreditChecking} from '@models/credit-checking';
import {RequestOptions} from '@models/request-options';
import {UpdatePriceBandOnOpening} from '@models/change-price-band';
import {FollowingGroupInstrument, Instrument, NotFollowingGroupInstrument} from '@homeModels/instrument';

@Injectable({
    providedIn: 'root'
})
export class GroupDataService {

    getAllGroups(requestOptions?: RequestOptions): Observable<Page<Group>> {
        return Http.get<Page<Group>>('/groups', requestOptions)
            .pipe(map(resp => {
                resp.content = plainToInstance(Group, resp.content);
                return resp;
            }));
    }

    getGroup(groupCode: string): Observable<Group> {
        return Http.get<Group>(`/groups/${groupCode}`)
            .pipe(map(resp => plainToInstance(Group, resp)))
    }

    changeGroupState(groupCode: string, body: Instrument.ChangeStateForm): Observable<AdminCommand> {
        return Http.post(`/groups/${groupCode}/state`, body);
    }

    deleteGroup(groupCode: string): Observable<Group> {
        return Http.delete<Group>(`/groups/${groupCode}`)
            .pipe(catchError((error: HttpErrorResponse) => {
                throw plainToInstance(ApiError, error.error);
            }));
    }

    includeInstrument(groupCode: string, securityId: string): Observable<null> {
        return Http.put<null>(`/groups/${groupCode}/securities/${securityId}/include`, null)
    }

    updateGroupInstruments(groupCode: string, securityIds: string[]): Observable<null> {
        return Http.post<null>(`/groups/${groupCode}/securities`, securityIds)
            .pipe(catchError((error: HttpErrorResponse) => {
                throw plainToInstance(ApiError, error.error);
            }));
    }

    addGroup(body: Group): Observable<null> {
        return Http.post<null>('/groups', body)
            .pipe(catchError((error: HttpErrorResponse) => {
                throw plainToInstance(ApiError, error.error);
            }))
    }

    editGroup(groupCode: string, body: Group): Observable<null> {
        return Http.put<null>(`/groups/${groupCode}`, body)

    }

    purgeOrders(groupCode: string, queryParams: QueryParams): Observable<AdminCommand> {
        return Http.delete(`/groups/${groupCode}/orders`, {params: queryParams})
    }

    getGroupQueuedSecurities(groupCode: string): Observable<string[]> {
        return Http.get<string[]>(`/groups/${groupCode}/queued-securities`);
    }

    updateStaticThreshold(groupCode: string, body: FormGroupRawValue<StaticThreshold.PercentageForm>): Observable<AdminCommand> {
        return Http.post(`/groups/${groupCode}/static-threshold`, body)
            .pipe(map(resp => plainToInstance(AdminCommand, resp)));
    }

    updateCreditChecking(groupCode: string, body: FormGroupRawValue<CreditChecking.UpdateFormGroup>): Observable<AdminCommand> {
        return Http.post(`/groups/${groupCode}/credit-checking`, body)
            .pipe(map(resp => plainToInstance(AdminCommand, resp)));
    }

    updatePriceBandOnOpening(groupCode: string, body: FormGroupRawValue<UpdatePriceBandOnOpening.UpdateFormGroup>): Observable<AdminCommand> {
        return Http.post(`/groups/${groupCode}/opening-update-price-band`, body)
            .pipe(map(resp => plainToInstance(AdminCommand, resp)));
    }

    fallowedStaticThresholdInstruments(groupCode: string, requestOptions?: RequestOptions): Observable<Page<FollowingGroupInstrument>> {
        return Http.get<Page<FollowingGroupInstrument>>(`/groups/${groupCode}/static-threshold/following-securities`, requestOptions)
            .pipe(map(resp => {
                resp.content = plainToInstance(FollowingGroupInstrument, resp.content);
                return resp;
            }))
    }

    notFallowedStaticThresholdInstruments(groupCode: string, requestOptions?: RequestOptions): Observable<Page<NotFollowingGroupInstrument>> {
        return Http.get<Page<NotFollowingGroupInstrument>>(`/groups/${groupCode}/static-threshold/not-following-securities`, requestOptions)
            .pipe(map(resp => {
                resp.content = plainToInstance(NotFollowingGroupInstrument, resp.content);
                return resp;
            }))
    }

    uploadStaticThreshold(file: File): Observable<any> {
        const formData = new FormData();
        formData.append('file', file);

        return Http.post<any>(`/groups/batch-static-threshold`, formData)
            .pipe(
                catchError((error: HttpErrorResponse) => {
                    throw plainToInstance(ApiError, error.error);
                }),
                map(resp => plainToInstance(AdminCommand, resp))
            );
    }
}
