import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {Page} from '@models/page';
import {Http} from '@http';
import {map} from 'rxjs/operators';
import {plainToInstance} from '../../shared/plain-to-instance/plain-to-instance';
import {Company, CompanyDetails} from '@models/company';
import {FormGroupRawValue} from '@models/form-group-raw-value';
import {RequestOptions} from '@models/request-options';

@Injectable({
  providedIn: 'root'
})
export class CompanyDataService {

    getAllCompanies(requestOptions?: RequestOptions): Observable<Page<Company>> {
        return Http.get<Page<Company>>('/companies', requestOptions)
            .pipe(map(resp => {
                resp.content = plainToInstance(Company, resp.content);
                return resp;
            }));
    }

    getCompany(companyCode: string): Observable<CompanyDetails> {
      return Http.get<CompanyDetails>(`/companies/${companyCode}`);
    }


    addCompany(body: FormGroupRawValue<Company.NewForm>): Observable<any> {
        return Http.post<any>('/companies', body)
    }


    updateCompany(companyCode: string, body: FormGroupRawValue<Company.UpdateForm>): Observable<any> {
        return Http.put<any>(`/companies/${companyCode}`, body)
    }


    deleteCompany(companyCode: string): Observable<any> {
        return Http.delete<any>(`/companies/${companyCode}`);
    }

    getCompanyProducts(companyCode: string): Observable<string[]> {
      return Http.get<string[]>(`/companies/${companyCode}/queued-products`)
    }
}
