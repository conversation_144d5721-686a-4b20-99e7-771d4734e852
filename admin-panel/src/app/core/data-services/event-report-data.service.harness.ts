import {Subject} from 'rxjs';
import {Page} from '@models/page';
import {EventReport} from '@layouts/main-layout/header/event-reports-dialog/event-report/event-report';
import {EventReportDataService} from '@dataServices/event-report-data.service';

export const eventReportDataServiceHarness: Pick<EventReportDataService, 'getAllEvents'> = {
    getAllEvents() { return new Subject<Page<EventReport>>(); },
}
