export class UtilConstants {
    static readonly AUTH_USER_KEY = 'user';
    static readonly USER_INFO_KEY = 'userInfo';
    static readonly REDIRECT_URL_KEY = 'redirectUrl';
    static readonly CURRENT_LANG_KEY = 'lang';
    static readonly DARK_MODE_KEY = 'dark-mode';
    static readonly HAS_LOCAL_ERROR_HANDLER = 'hasLocalErrorHandler';
    static readonly NOTIFICATION_DEFAULT_COUNT = 3;
    static readonly NOTIFICATION_DEFAULT_DELAY = 5_000 // ms;
    static readonly DATE_FORMAT = 'YYYY/MM/DD';
    static readonly ENDPOINT_DATE_FORMAT = 'YYYY-MM-DD';
    static readonly TIME_FORMAT = 'HH:mm:ss';
    static readonly DATE_TIME_FORMAT = 'YYY/MM/DD - HH:mm:ss';
    static readonly EXACT_DATE_TIME_FORMAT = 'YYY/MM/DD - HH:mm:ss.ms';
    static readonly CSRF_HEADER_NAME = 'X-XSRF-TOKEN';
    static readonly SCHEDULE_TEMPLATE_EMPTY_ACTION_CODE = 'empty';
    static readonly SYSTEM_TIME_UPDATE_INTERVAL = 30_000 // ms;
    static readonly HOT_KEY_HINT_DELAY = 500; // ms
    static readonly CLEARING_REPORT_FETCH_DATA_INTERVAL = 10_000; // ms
    static readonly SNACK_BAR_DURATION = 3_000; // ms
    static readonly GET_ARAMIS_STATUS_INTERVAL = 1_000; // ms
    static readonly DEFAULT_PAGE_SIZE = 20;
    static readonly DEFAULT_SEARCH_DEBOUNCE_TIME = 500; //ms
}
