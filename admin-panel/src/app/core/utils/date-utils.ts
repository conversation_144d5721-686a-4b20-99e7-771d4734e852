import {GDate} from 'mb-date/dist/src/gdate';
import {UtilConstants} from '@constants/util-constants';

export function toEndpointDateFormat(date: Date): string {
    return new GDate(date).format(UtilConstants.ENDPOINT_DATE_FORMAT);
}

export function toUTCDate(date: Date): Date {
    const utcDate = new Date();
    const timezoneOffset = utcDate.getTimezoneOffset() * -1;
    utcDate.setUTCFullYear(date.getFullYear())
    utcDate.setUTCMonth(date.getMonth())
    utcDate.setUTCDate(date.getDate());
    utcDate.setHours(0, timezoneOffset, 0, 0);
    return utcDate;
}
