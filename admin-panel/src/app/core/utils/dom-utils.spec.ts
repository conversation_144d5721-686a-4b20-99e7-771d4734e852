import {openSearchBox} from './dom-utils';

describe('DOM Utils', () => {
    describe('openSearchBox', () => {
        it('should dispatch keyboard events to open search box', () => {
            // Arrange
            spyOn(document, 'dispatchEvent').and.callThrough();

            // Act
            openSearchBox();

            // Assert
            expect(document.dispatchEvent).toHaveBeenCalledTimes(4);

            // Check that the correct events were dispatched
            const calls = (document.dispatchEvent as jasmine.Spy).calls.all();

            // First call - keydown ControlLeft
            expect(calls[0].args[0] instanceof KeyboardEvent).toBeTruthy();
            expect(calls[0].args[0].type).toBe('keydown');
            expect(calls[0].args[0].code).toBe('ControlLeft');

            // Second call - keydown KeyF
            expect(calls[1].args[0] instanceof KeyboardEvent).toBeTruthy();
            expect(calls[1].args[0].type).toBe('keydown');
            expect(calls[1].args[0].code).toBe('KeyF');

            // Third call - keyup ControlLeft
            expect(calls[2].args[0] instanceof KeyboardEvent).toBeTruthy();
            expect(calls[2].args[0].type).toBe('keyup');
            expect(calls[2].args[0].code).toBe('ControlLeft');

            // Fourth call - keyup KeyF
            expect(calls[3].args[0] instanceof KeyboardEvent).toBeTruthy();
            expect(calls[3].args[0].type).toBe('keyup');
            expect(calls[3].args[0].code).toBe('KeyF');
        });
    });
});
