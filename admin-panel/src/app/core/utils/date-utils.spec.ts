import {toEndpointDateFormat, toUTCDate} from './date-utils';
import {UtilConstants} from '@constants/util-constants';
import {GDate} from 'mb-date/dist/src/gdate';

describe('Date Utils', () => {
    describe('toEndpointDateFormat', () => {
        it('should format date according to ENDPOINT_DATE_FORMAT', () => {
            // Arrange
            const testDate = new Date(2023, 5, 15); // June 15, 2023
            const expectedFormat = new GDate(testDate).format(UtilConstants.ENDPOINT_DATE_FORMAT);

            // Act
            const result = toEndpointDateFormat(testDate);

            // Assert
            expect(result).toBe(expectedFormat);
        });

        it('should handle current date', () => {
            // Arrange
            const testDate = new Date();
            const expectedFormat = new GDate(testDate).format(UtilConstants.ENDPOINT_DATE_FORMAT);

            // Act
            const result = toEndpointDateFormat(testDate);

            // Assert
            expect(result).toBe(expectedFormat);
        });
    });

    describe('toUTCDate', () => {
        it('should handle current date', () => {
            // Arrange
            const testDate = new Date();

            // Act
            const result = toUTCDate(testDate);

            // Assert
            expect(result.getUTCFullYear()).toBe(testDate.getFullYear());
            expect(result.getUTCMonth()).toBe(testDate.getMonth());
            expect(result.getUTCDate()).toBe(testDate.getDate());
        });
    });
});
