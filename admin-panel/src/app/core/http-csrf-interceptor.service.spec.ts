import {TestBed} from '@angular/core/testing';

import {HttpCsrfInterceptor} from './http-csrf-interceptor.service';
import {provideHttpClientTesting} from '@angular/common/http/testing';
import {SharedModule} from '../shared/shared.module';
import {TranslateTestingModule} from '../../translate-testing.module';
import {provideHttpClient, withInterceptorsFromDi} from '@angular/common/http';

describe('HttpCSrfInterceptorService', () => {
  let service: HttpCsrfInterceptor;

  beforeEach(() => {
    TestBed.configureTestingModule({
        imports: [TranslateTestingModule,
            SharedModule],
        providers: [
            HttpCsrfInterceptor,
            provideHttpClient(withInterceptorsFromDi()),
            provideHttpClientTesting()
        ]
    });
    service = TestBed.inject(HttpCsrfInterceptor);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
