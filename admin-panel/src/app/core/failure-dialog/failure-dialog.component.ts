import {Component} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {TranslateKey} from '@shared/enums/translate-key';

@Component({
    selector: 'app-failure-dialog',
    templateUrl: './failure-dialog.component.html',
    standalone: false
})
export class FailureDialogComponent {
    readonly translateKeys = TranslateKey;

    constructor(private modalRef: BsModalRef) { }

    close(): void {
        this.modalRef.hide();
    }

}
