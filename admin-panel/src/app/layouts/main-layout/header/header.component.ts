import {AfterViewInit, ChangeDetectionStrategy, Component, OnD<PERSON>roy, OnInit, signal, ViewChild} from '@angular/core';
import {LoginDataService} from '@dataServices/login-data.service';
import {TranslateService} from '@ngx-translate/core';
import {BsModalService} from 'ngx-bootstrap/modal';
import {NotificationDialogComponent} from './notification-dialog/notification-dialog.component';
import {TranslateKey} from '@enums/translate-key';
import {UtilConstants} from '@constants/util-constants';
import {Language} from '@constants/language';
import {EventReportsDialogComponent} from './event-reports-dialog/event-reports-dialog.component';
import {NotificationPopoverComponent} from './notification-popover/notification-popover.component';
import {Auth} from '@models/auth';
import {RoutingLayout} from '@constants/routing-layout';
import {Router} from '@angular/router';
import {StoreService} from '@shared/services/store.service';
import {
    ChangePasswordDialogComponent
} from '../../../feature/home/<USER>/change-password-dialog/change-password-dialog.component';
import {interval} from 'rxjs';
import {startWith, takeUntil} from 'rxjs/operators';
import {OnDestroyBase} from '../../../shared/base/on-destroy-base';
import {Permissions} from '../../../shared/constants/permissions.constant';

@Component({
    selector: 'app-header',
    templateUrl: './header.component.html',
    styleUrls: ['./header.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class HeaderComponent extends OnDestroyBase implements OnInit, AfterViewInit, OnDestroy {
    readonly translateKeys = TranslateKey;

    readonly permissions = Permissions;

    get userInfo(): Auth { return this._loginDataService.getUserInfo(); }

    get currentLanguage(): string {
        return this._translateService.currentLang === Language.layouts.EN
            ? Language.layouts.EN
            : Language.layouts.FA;
    }

    set currentLanguage(value: string) {
        this.changeLanguage(value);
    }

    get myPermissionsRoute(): string {
        return `/${RoutingLayout.SETTINGS}/${RoutingLayout.MY_PERMISSIONS}`
    }

    readonly systemDate = signal<Date>(StoreService.systemDateTime);

    private _darkMode = localStorage.getItem(UtilConstants.DARK_MODE_KEY) === 'true';

    @ViewChild(NotificationPopoverComponent, {static: true})
    private _notificationPopoverElm: NotificationPopoverComponent;

    constructor(
        private _loginDataService: LoginDataService,
        private _translateService: TranslateService,
        private _modalService: BsModalService,
        private _router: Router
    ) { super(); }

    ngOnInit() {
        this.toggleDarkMode();

        this._updateSystemTime();
    }

    ngOnDestroy() {
        super.ngOnDestroy();
        this._removeNotificationPopoverElm();
    }

    ngAfterViewInit(): void {
        this._moveNotificationPopoverToTopOfBody();
    }

    logout(): void {
        this._loginDataService
            .logout()
            .subscribe(() => {
                this._loginDataService.invalidateAuthToken();
                // The following line is used for clean garbage collection after reloading page.
                this._navigateToLoginAndReload();
            });
    }

    changeLanguage(lang: string): void {
        this._translateService.use(lang).subscribe(() => {
            localStorage.setItem(UtilConstants.CURRENT_LANG_KEY, lang);
            document.body.dir = Language.direction[lang.toUpperCase()];
        });
    }

    openNotificationsDialog(): void {
        this._modalService.show(NotificationDialogComponent, {
            class: 'side-modal'
        });
    }

    openChangePasswordDialog(): void {
        this._modalService.show(ChangePasswordDialogComponent);
    }

    openEventReportsDialog(): void {
        this._modalService.show(EventReportsDialogComponent, {
            class: 'side-modal'
        });
    }

    toggleDarkMode(): void {
        localStorage.setItem(UtilConstants.DARK_MODE_KEY, this._darkMode.toString());

        this._darkMode = !this._darkMode;

        if (this._darkMode) {
            document.documentElement.style.filter = 'invert(0.9) hue-rotate(180deg)';
            document.querySelectorAll('img').forEach((el: HTMLElement) => {
                el.style.filter = 'invert(0.9) hue-rotate(180deg)';
            });
        } else {
            document.documentElement.style.filter = '';
            document.querySelectorAll('img').forEach((el: HTMLElement) => {
                el.style.filter = '';
            });
        }
    }

    private _moveNotificationPopoverToTopOfBody(): void {
        // This is because we blur the page while the search box is open
        const notificationPopover = this._notificationPopoverElm.elementRef.nativeElement;
        document.querySelector('body').prepend(notificationPopover);
    }

    private _navigateToLoginAndReload(): void {
        this._router
            .navigate([RoutingLayout.LOGIN])
            .then(this._reloadPage);
    }

    private _reloadPage(): void {
        window.location.reload();
    }

    private _removeNotificationPopoverElm(): void {
        document.body.removeChild(this._notificationPopoverElm.elementRef.nativeElement);
    }

    private _updateSystemTime(): void {
        interval(1_000)
            .pipe(
                startWith(0),
                takeUntil(this._onDestroy)
            )
            .subscribe(() => {
                StoreService.systemDateTime = new Date(StoreService.systemDateTime.getTime() + 1_000);
                this.systemDate.set(StoreService.systemDateTime);
            })
    }
}
