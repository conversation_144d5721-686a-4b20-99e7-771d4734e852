import {AdminCommandsComponent} from './admin-commands.component';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {CommandDataService} from '@dataServices/command-data.service';
import {commandDataServiceHarness} from '@dataServices/command-data.service.harness';
import {of} from 'rxjs';
import {TestUtils} from '@test/test-utils';
import {ComponentHarness} from '@test/harness/component-harness';
import {StoreService} from '@shared/services/store.service';
import {TranslateTestingModule} from '../../../../../translate-testing.module';
import {HasPermissionService} from '@directives/has-permission.service';
import {hasPermissionServiceHarness} from '@directives/has-permission.service.harness';

describe('AdminCommandsComponent', () => {
    let ha: ComponentHarness<AdminCommandsComponent>;

    beforeEach(() => {
        ha = new ComponentHarness(AdminCommandsComponent, {
            imports: [
                AdminCommandsComponent,
                TranslateTestingModule
            ],
            providers: [
                {provide: CommandDataService, useValue: commandDataServiceHarness},
                {provide: HasPermissionService, useValue: hasPermissionServiceHarness}
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
            detectChanges: false
        });

        StoreService.instrumentsObj = TestUtils.getInstrumentsObj();
        StoreService.instruments = TestUtils.getInstruments().content;
    });

    it('should create', () => {
        ha.detectChanges();
        expect(ha.component).toBeTruthy();
    });

    it('should append admin command container per any command', () => {
        const adminCommands = TestUtils.getCommands();
        spyOn(commandDataServiceHarness, 'getMyCommands').and.returnValue(of(adminCommands));
        spyOn(commandDataServiceHarness, 'getCommands').and.returnValue(of(adminCommands));
        ha.detectChanges();

        expect(ha.get('app-admin-command-container').query.length).toBe(adminCommands.content.length);
    })
});
