import {ChangeDetectionStrategy, Component, Input} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {SimpleTableData} from '@models/table-representation/simple-table-representation';

@Component({
    selector: 'app-admin-command-details',
    templateUrl: './admin-command-details.component.html',
    styleUrls: ['./admin-command-details.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class AdminCommandDetailsComponent {
    readonly translateKeys = TranslateKey;

    @Input() simpleTableGroups: SimpleTableData[];

}
