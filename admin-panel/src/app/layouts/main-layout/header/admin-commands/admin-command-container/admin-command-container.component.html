<div class="w-100 border-bottom">
    <div class="px-3 py-2">
        <div class="d-flex cursor-pointer" (click)="toggleOpenCommandDetail()">
            <div>
                <mat-icon
                    [class.text-success]="adminCommand.isSuccess"
                    [class.text-primary]="adminCommand.isPartialSuccess"
                    [class.text-warning]="adminCommand.isAwaitingResponse"
                    [class.text-danger]="adminCommand.isFailed">terminal</mat-icon>
            </div>

            <div class="mx-2 w-100">
                <strong class="d-block">{{adminCommand.type | camelCase | translate}}</strong>

                <small>{{translateKeys.status | translate}}: <strong>{{adminCommand.status | camelCase | translate}}</strong></small>

                <div class="d-flex justify-content-between">
                    <small *ngIf="hasAllCommandsPermission" class="d-block">{{translateKeys.sender | translate}}: <strong>{{adminCommand.sender}}</strong></small>

                    <small class="text-muted">{{adminCommand.timestamp | mbDate:'HH:mm:ss'}}</small>
                </div>
            </div>
        </div>

    </div>

    <div *ngIf="isCommandDetailsOpen" class="py-3 px-2 mb-2">
        <alert type="danger" class="mb-3 text-left" *ngIf="adminCommand.rejectionCause">
            <div class="text-break" *ngFor="let item of adminCommand.rejectionCause" style="line-height: 1.5">{{item}}</div>
        </alert>

        <alert type="warning" *ngIf="!adminCommand.request">
            {{translateKeys.thereAreNoDetails | translate}}
        </alert>

        <app-admin-command-details
            *ngIf="hasDetail"
            [simpleTableGroups]="adminCommand.request.getTableData()"></app-admin-command-details>

        <app-admin-command-no-details
            *ngIf="!hasDetail"
            [simpleTableGroups]="adminCommand.request"></app-admin-command-no-details>
    </div>
</div>
