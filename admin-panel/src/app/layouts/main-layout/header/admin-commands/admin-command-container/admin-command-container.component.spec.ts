import {AdminCommandContainerComponent} from './admin-command-container.component';
import {TranslateTestingModule} from 'src/translate-testing.module';
import {SharedModule} from 'src/app/shared/shared.module';
import {AdminCommand} from '@models/admin-command';
import {CamelCasePipe} from '@pipes/camel-case.pipe';
import {ComponentHarness} from '@test/harness/component-harness';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {plainToInstance} from '../../../../../shared/plain-to-instance/plain-to-instance';
import {TranslateKey} from '@enums/translate-key';

describe('AdminCommandContainerComponent', () => {
    let ha: ComponentHarness<AdminCommandContainerComponent>;

    beforeEach(() => {
        ha = new ComponentHarness(AdminCommandContainerComponent, {
            declarations: [
                AdminCommandContainerComponent,
                CamelCasePipe
            ],
            imports: [
                TranslateTestingModule,
                SharedModule
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
            detectChanges: false
        });

        const adminCommand = {
            request: {
                securityId: 'SPY',
                targetState: 'PRE_OPENING',
                shouldOpen: true,
                getTableData: () => [{
                    title: 'Test Table',
                    data: [{key: 'value'}]
                }]
            },
            type: 'CHANGE_SECURITY_STATE',
            status: 'SUCCESS',
            timestamp: '2021-07-07T11:20:17.216531',
            commandId: 1,
            rejectionCause: 'SECURITY_STATE_TRANSITION_INVALID',
            sender: 'admin',
            securityId: 'SPY',
            group: {code: 'G1'}
        };

        ha.component.adminCommand = plainToInstance(AdminCommand, adminCommand);
    });

    it('should create', () => {
        ha.fixture.detectChanges();
        expect(ha.component).toBeTruthy();
    });

    it('should initialize with translateKeys', () => {
        ha.fixture.detectChanges();
        expect(ha.component.translateKeys).toBe(TranslateKey);
    });

    it('should toggle open command details', () => {
        ha.fixture.detectChanges();

        expect(ha.get('app-admin-command-details')).not.toBeExists();

        ha.get('.cursor-pointer').click();
        expect(ha.get('app-admin-command-details')).toBeExists();

        ha.get('.cursor-pointer').click();
        expect(ha.get('app-admin-command-details')).not.toBeExists();
    });

    it('should not display rejection cause when command has not rejectionCause field', () => {
        (ha.component.adminCommand as any).rejectionCause = [];
        ha.detectChanges();

        ha.get('.cursor-pointer').thatContains('Change Instrument State').click();

        expect(ha.get('alert').thatContains('SECURITY_STATE_TRANSITION_INVALID')).not.toBeExists();
    });

    it('should display rejection cause when command has rejectionCause field', () => {
        ha.detectChanges();

        ha.get('.cursor-pointer').thatContains('Change Instrument State').click();

        expect(ha.get('alert').thatContains('SECURITY_STATE_TRANSITION_INVALID')).toBeExists();
    });

    it('should correctly determine if command has details', () => {
        // Command with getTableData method
        expect(ha.component.hasDetail).toBe(true);
    });

    it('should show admin-command-details when hasDetail is true', () => {
        ha.detectChanges();
        ha.get('.cursor-pointer').click();
        expect(ha.get('app-admin-command-details')).toBeExists();
    });

    it('should display sender information when hasAllCommandsPermission is true', () => {
        ha.component.hasAllCommandsPermission = true;
        ha.detectChanges();
        expect(ha.get('small').thatContains('Sender')).toBeExists();
    });

    it('should not display sender information when hasAllCommandsPermission is false', () => {
        ha.component.hasAllCommandsPermission = false;
        ha.detectChanges();
        expect(ha.get('small').thatContains('Sender')).not.toBeExists();
    });

    it('should display success icon for success status', () => {
        const adminCommand = {
            request: {
                securityId: 'SPY',
                targetState: 'PRE_OPENING',
                shouldOpen: true,
                getTableData: () => [{
                    title: 'Test Table',
                    data: [{key: 'value'}]
                }]
            },
            type: 'CHANGE_SECURITY_STATE',
            status: 'SUCCESS',
            timestamp: '2021-07-07T11:20:17.216531',
            commandId: 1,
            rejectionCause: 'SECURITY_STATE_TRANSITION_INVALID',
            sender: 'admin'
        };
        ha.component.adminCommand = plainToInstance(AdminCommand, adminCommand);
        ha.detectChanges();
        expect(ha.get('mat-icon.text-success')).toBeExists();
    });

    it('should display primary icon for partial success status', () => {
        const adminCommand = {
            request: {
                securityId: 'SPY',
                targetState: 'PRE_OPENING',
                shouldOpen: true,
                getTableData: () => [{
                    title: 'Test Table',
                    data: [{key: 'value'}]
                }]
            },
            type: 'CHANGE_SECURITY_STATE',
            status: 'PARTIAL_SUCCESS',
            timestamp: '2021-07-07T11:20:17.216531',
            commandId: 1,
            rejectionCause: 'SECURITY_STATE_TRANSITION_INVALID',
            sender: 'admin'
        };
        ha.component.adminCommand = plainToInstance(AdminCommand, adminCommand);
        ha.detectChanges();
        expect(ha.get('mat-icon.text-primary')).toBeExists();
    });

    it('should display warning icon for awaiting response status', () => {
        const adminCommand = {
            request: {
                securityId: 'SPY',
                targetState: 'PRE_OPENING',
                shouldOpen: true,
                getTableData: () => [{
                    title: 'Test Table',
                    data: [{key: 'value'}]
                }]
            },
            type: 'CHANGE_SECURITY_STATE',
            status: 'AWAITING_RESPONSE',
            timestamp: '2021-07-07T11:20:17.216531',
            commandId: 1,
            rejectionCause: 'SECURITY_STATE_TRANSITION_INVALID',
            sender: 'admin'
        };
        ha.component.adminCommand = plainToInstance(AdminCommand, adminCommand);
        ha.detectChanges();
        expect(ha.get('mat-icon.text-warning')).toBeExists();
    });

    it('should display danger icon for failure status', () => {
        const adminCommand = {
            request: {
                securityId: 'SPY',
                targetState: 'PRE_OPENING',
                shouldOpen: true,
                getTableData: () => [{
                    title: 'Test Table',
                    data: [{key: 'value'}]
                }]
            },
            type: 'CHANGE_SECURITY_STATE',
            status: 'FAILURE',
            timestamp: '2021-07-07T11:20:17.216531',
            commandId: 1,
            rejectionCause: 'SECURITY_STATE_TRANSITION_INVALID',
            sender: 'admin'
        };
        ha.component.adminCommand = plainToInstance(AdminCommand, adminCommand);
        ha.detectChanges();
        expect(ha.get('mat-icon.text-danger')).toBeExists();
    });

    it('should correctly format and display the command timestamp', () => {
        ha.detectChanges();
        expect(ha.get('small.text-muted')).toBeExists();
        // Note: We can't test the exact formatted time since it depends on the mbDate pipe implementation
    });
});
