import {ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit} from '@angular/core';
import {CommonModule} from '@angular/common';
import {TranslateKey} from '@enums/translate-key';
import {AdminCommand} from '@models/admin-command';
import {CommandDataService} from '@dataServices/command-data.service';
import {AdminCommandContainerModule} from './admin-command-container/admin-command-container.module';
import {Permissions} from '../../../../shared/constants/permissions.constant';
import {HasPermissionService} from '@directives/has-permission.service';
import {Params} from '@angular/router';

@Component({
    selector: 'app-admin-commands',
    imports: [
        CommonModule,
        AdminCommandContainerModule
    ],
    templateUrl: './admin-commands.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AdminCommandsComponent implements OnInit {
    get translateKeys() { return TranslateKey; }

    get hasAllCommandsPermission(): boolean {
        return this._hasPermissionService.hasPermission(Permissions.NOTIFICATIONS_PERMISSION)
    }

    adminCommands: AdminCommand[] = [];

    constructor(
        private _hasPermissionService: HasPermissionService,
        private _commandDataService: CommandDataService,
        private _changeDetectorRef: ChangeDetectorRef
    ) { }

    ngOnInit(): void {
        this._getAdminCommands();
    }

    private _getAdminCommands(): void {
        // In order to display only 20 top commands we need to keep
        // the following query params.
        const queryParams = {
            page: 0,
            size: 20
        };

        this.hasAllCommandsPermission ?
            this._getAllCommands(queryParams) :
            this._getMyCommands(queryParams);
    }

    private _getAllCommands(queryParams: Params): void {
        this._commandDataService
            .getCommands({params: queryParams})
            .subscribe(resp => {
                this.adminCommands = resp.content;
                this._changeDetectorRef.detectChanges();
            })
    }

    private _getMyCommands(queryParams: Params): void {
        this._commandDataService
            .getMyCommands({params: queryParams})
            .subscribe(resp => {
                this.adminCommands = resp.content;
                this._changeDetectorRef.detectChanges();
            });
    }
}
