import {TranslateTestingModule} from 'src/translate-testing.module';
import {EventReportsDialogComponent} from './event-reports-dialog.component';
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {EventReportDataService} from '@dataServices/event-report-data.service';
import {of} from 'rxjs';
import {TestUtils} from '@test/test-utils';
import {StoreService} from '@shared/services/store.service';
import {ModalHeaderComponent} from '@modules/shared-declarations/modal-header/modal-header.component';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {ComponentHarness} from '@test/harness/component-harness';
import {eventReportDataServiceHarness} from '@dataServices/event-report-data.service.harness';
import {SharedModule} from '../../../../shared/shared.module';
import {CamelCasePipe} from '@pipes/camel-case.pipe';
import {RouterTestingModule} from '@angular/router/testing';
import {HttpClientTestingModule} from '@angular/common/http/testing';

describe('EventReportsDialogComponent', () => {
    let ha: ComponentHarness<EventReportsDialogComponent>;
    StoreService.instrumentsObj = TestUtils.getInstrumentsObj();
    const eventReports = TestUtils.getEventReports();

    beforeEach(() => {
        ha = new ComponentHarness(EventReportsDialogComponent, {
            declarations: [
                EventReportsDialogComponent,
                ModalHeaderComponent,
                CamelCasePipe
            ],
            imports: [
                TranslateTestingModule,
                HttpClientTestingModule,
                RouterTestingModule,
                SharedModule
            ],
            providers: [
                BsModalRef,
                {provide: EventReportDataService, useValue: eventReportDataServiceHarness},
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA],
            detectChanges: false
        });
    });

    beforeEach(() => {
        spyOn(eventReportDataServiceHarness, 'getAllEvents').and.returnValue(of(eventReports));
    })

    it('should create', () => {
        ha.detectChanges();
        expect(ha.component).toBeTruthy();
    });

    it('should getTableData calls once', () => {
        spyOn(eventReports.content[0].internalEvent, 'getTableData');
        ha.detectChanges();

        expect(eventReports.content[0].internalEvent.getTableData).toHaveBeenCalled();
    })
});
