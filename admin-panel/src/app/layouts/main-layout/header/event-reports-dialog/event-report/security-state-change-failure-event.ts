import {TableDataProvider} from '@models/table-representation/table-representable';
import {Instrument, InstrumentState} from '@homeModels/instrument';
import {SimpleTableData} from '@models/table-representation/simple-table-representation';
import {TableGroupData} from '@models/table-representation/table-group-data';
import {TranslateKey} from '@enums/translate-key';
import {BilingualPipe} from '@shared/modules/shared-declarations/select-by-language/bilingual.pipe';
import {TranslatePipe} from '@ngx-translate/core';
import {Getter} from '../../../../../shared/plain-to-instance/getter';
import {toCamelCase} from '@core/utils';
import {StoreService} from '@shared/services/store.service';

@Getter
export class SecurityStateChangeFailureEvent extends TableDataProvider {
    readonly securityId: string;

    readonly oldState: InstrumentState;

    readonly newState: InstrumentState;

    readonly isFollowingGroup: boolean;

    readonly rejectionCause: string;

    get instrument(): Instrument.Simple { return StoreService.instrumentsObj[this.securityId]};

    getTableData(): SimpleTableData[] {
        const data: TableGroupData[] = [
            {
                title: TranslateKey.mnemonic,
                value: this.instrument.mnemonic,
                pipeToken: BilingualPipe,
                pipeArgs: [true]
            },
            {
                title: TranslateKey.oldState,
                value: toCamelCase(this.oldState),
                pipeToken: TranslatePipe
            },
            {
                title: TranslateKey.newState,
                value: toCamelCase(this.newState),
                pipeToken: TranslatePipe
            },
            {
                title: TranslateKey.followedGroup,
                value: this.isFollowingGroup?.toString(),
                pipeToken: TranslatePipe
            },
            {
                title: TranslateKey.rejectionCause,
                value: this.rejectionCause
            }
        ];

        return [{data}];
    }

}
