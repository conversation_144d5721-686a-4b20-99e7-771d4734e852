import {SecurityStateChangeFailureEvent} from './security-state-change-failure-event';
import {TranslateKey} from '@enums/translate-key';
import {BilingualPipe} from '@shared/modules/shared-declarations/select-by-language/bilingual.pipe';
import {TranslatePipe} from '@ngx-translate/core';
import {Instrument, InstrumentState} from '@homeModels/instrument';
import {plainToInstance} from '../../../../../shared/plain-to-instance/plain-to-instance';
import {toCamelCase} from '@core/utils';
import {InstrumentSimpleBuilder} from '@test/test-builders';
import {StoreService} from '@shared/services/store.service';

describe('SecurityStateChangeFailureEvent', () => {
    let event: SecurityStateChangeFailureEvent;
    const mockInstrument = new InstrumentSimpleBuilder() as unknown as Instrument.Simple;

    beforeEach(() => {
        StoreService.instrumentsObj[mockInstrument.securityId] = mockInstrument;

        // Create a mock SecurityStateChangeFailureEvent instance
        event = plainToInstance(SecurityStateChangeFailureEvent, {
            securityId: mockInstrument.securityId,
            oldState: InstrumentState.AUTHORIZED,
            newState: InstrumentState.SUSPENDED,
            isFollowingGroup: true,
            rejectionCause: 'Test rejection cause'
        });
    });

    describe('getTableData', () => {
        it('should return table data with correct structure', () => {
            // Act
            const result = event.getTableData();

            // Assert
            expect(result.length).toBe(1);
            expect(result[0].data.length).toBe(5);

            // Check mnemonic
            const mnemonicData = result[0].data.find((data) => data.title === TranslateKey.mnemonic);
            expect(mnemonicData.title).toBe(TranslateKey.mnemonic);
            expect(mnemonicData.value).toBe(mockInstrument.mnemonic);
            expect(mnemonicData.pipeToken).toBe(BilingualPipe);
            expect(mnemonicData.pipeArgs).toEqual([true]);

            // Check oldState
            const oldStateData = result[0].data.find((data) => data.title === TranslateKey.oldState);
            expect(oldStateData.title).toBe(TranslateKey.oldState);
            expect(oldStateData.value).toBe(toCamelCase(InstrumentState.AUTHORIZED));
            expect(oldStateData.pipeToken).toBe(TranslatePipe);

            // Check newState
            const newStateData = result[0].data.find((data) => data.title === TranslateKey.newState);
            expect(newStateData.title).toBe(TranslateKey.newState);
            expect(newStateData.value).toBe(toCamelCase(InstrumentState.SUSPENDED));
            expect(newStateData.pipeToken).toBe(TranslatePipe);

            // Check isFollowingGroup
            const followingGroupData = result[0].data.find((data) => data.title === TranslateKey.followedGroup);
            expect(followingGroupData.title).toBe(TranslateKey.followedGroup);
            expect(followingGroupData.value).toBe('true');
            expect(followingGroupData.pipeToken).toBe(TranslatePipe);

            // Check rejectionCause
            const rejectionCauseData = result[0].data.find((data) => data.title === TranslateKey.rejectionCause);
            expect(rejectionCauseData.title).toBe(TranslateKey.rejectionCause);
            expect(rejectionCauseData.value).toBe('Test rejection cause');
        });

        it('should handle undefined isFollowingGroup correctly', () => {
            // Arrange
            event = plainToInstance(SecurityStateChangeFailureEvent, {
                securityId: mockInstrument.securityId,
                oldState: InstrumentState.AUTHORIZED,
                newState: InstrumentState.SUSPENDED,
                isFollowingGroup: undefined,
                rejectionCause: 'Test rejection cause'
            });

            // Act
            const result = event.getTableData();

            // Assert
            expect(result.length).toBe(1);

            // Check isFollowingGroup specifically
            const followingGroupData = result[0].data.find((data) => data.title === TranslateKey.followedGroup);
            expect(followingGroupData.title).toBe(TranslateKey.followedGroup);
            expect(followingGroupData.value).toBe(undefined);
            expect(followingGroupData.pipeToken).toBe(TranslatePipe);
        });

        it('should handle null isFollowingGroup correctly', () => {
            // Arrange
            event = plainToInstance(SecurityStateChangeFailureEvent, {
                securityId: mockInstrument.securityId,
                oldState: InstrumentState.AUTHORIZED,
                newState: InstrumentState.SUSPENDED,
                isFollowingGroup: null,
                rejectionCause: 'Test rejection cause'
            });

            // Act
            const result = event.getTableData();

            // Assert
            expect(result.length).toBe(1);

            // Check isFollowingGroup specifically
            const followingGroupData = result[0].data.find((data) => data.title === TranslateKey.followedGroup);
            expect(followingGroupData.title).toBe(TranslateKey.followedGroup);
            expect(followingGroupData.value).toBe(undefined);
            expect(followingGroupData.pipeToken).toBe(TranslatePipe);
        });

        it('should handle false isFollowingGroup correctly', () => {
            // Arrange
            event = plainToInstance(SecurityStateChangeFailureEvent, {
                securityId: mockInstrument.securityId,
                oldState: InstrumentState.AUTHORIZED,
                newState: InstrumentState.SUSPENDED,
                isFollowingGroup: false,
                rejectionCause: 'Test rejection cause'
            });

            // Act
            const result = event.getTableData();

            // Assert
            expect(result.length).toBe(1);

            // Check isFollowingGroup specifically
            const followingGroupData = result[0].data.find((data) => data.title === TranslateKey.followedGroup);
            expect(followingGroupData.title).toBe(TranslateKey.followedGroup);
            expect(followingGroupData.value).toBe('false');
            expect(followingGroupData.pipeToken).toBe(TranslatePipe);
        });
    });
});
