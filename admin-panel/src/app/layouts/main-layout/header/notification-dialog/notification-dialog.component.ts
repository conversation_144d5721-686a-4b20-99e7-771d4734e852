import {ChangeDetectionStrategy, Component} from '@angular/core';
import {TranslateKey} from '@enums/translate-key';
import {RoutingLayout} from '@constants/routing-layout';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {HomeRoutes} from '../../../../feature/home/<USER>/constants/home-routing.constants';

@Component({
    selector: 'app-notification-dialog',
    templateUrl: './notification-dialog.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class NotificationDialogComponent {
    readonly translateKeys = TranslateKey;
    readonly notificationPageUrl = `${RoutingLayout.HOME}/${HomeRoutes.NOTIFICATIONS}`;

    constructor(
        private _modalRef: BsModalRef
    ) {}

    openNotificationsPage(): void {
        this._modalRef.hide();
    }
}
