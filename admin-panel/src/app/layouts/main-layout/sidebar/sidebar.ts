import {ThemePalette} from '@angular/material/core';

export interface Sidebar {
    items: SidebarItem[];
    footerItems: SideBarFooterItem[];
}

export interface SidebarItem {
    title: string;
    icon: string;
    submenu: SidebarSubmenu[];
}

interface SidebarSubmenu {
    title: string;
    permissions?: any[];
    disabled?: boolean;
    navigationUrl?: string;
    onClick?(): void;
}

export interface SideBarFooterItem {
    title: string;
    icon: string;
    color?: ThemePalette;
    class?: string;
    disabled?: boolean;
    onClick(): void;
}
