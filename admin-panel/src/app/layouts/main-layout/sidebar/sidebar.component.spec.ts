import {BsModalService} from 'ngx-bootstrap/modal';
import {RouterTestingModule} from '@angular/router/testing';
import {SidebarComponent} from './sidebar.component';
import {TranslateKey} from '@enums/translate-key';
import {SharedModule} from 'src/app/shared/shared.module';
import {ComponentHarness} from '@test/harness/component-harness';
import {TranslateTestingModule} from '../../../../translate-testing.module';

describe('SidebarComponent', () => {
    let ha: ComponentHarness<SidebarComponent>;

    beforeEach(() => {
        ha = new ComponentHarness(SidebarComponent, {
            declarations: [SidebarComponent],
            imports: [
                TranslateTestingModule,
                RouterTestingModule,
                SharedModule
            ],
            providers: [BsModalService],
            detectChanges: false
        })

        ha.component.sidebar = {
            items: [
                {
                    title: TranslateKey.group,
                    icon: 'folder_open',
                    submenu: [
                        {title: TranslateKey.groups, onClick: () => {}}
                    ]
                },
                {
                    title: TranslateKey.file,
                    icon: 'upload_file',
                    submenu: [
                        {title: TranslateKey.getM1File, onClick: () => {}}
                    ]
                }
            ],
            footerItems: []
        };
        ha.detectChanges();
    });

    it('should create', () => {
        expect(ha.component).toBeTruthy();
    });

    it('should toggle open sidebar by clicking on expand sidebar btn', () => {
        // When
        ha.get('div:last-child > .menu:last-child').click();
        // Then
        expect(ha.get('.sidenav')).not.toHaveClass('sidenav--collapse');
        expect(ha.get('.sidenav')).toHaveClass('sidenav--expand');

        // When
        ha.get('div:last-child > .menu:last-child').click();
        // Then
        expect(ha.get('.sidenav')).toHaveClass('sidenav--collapse')
        expect(ha.get('.sidenav')).not.toHaveClass('sidenav--expand')
    });

    it('should toggle open menus', () => {
        // When
        ha.get('div:last-child > .menu:last-child').click();
        // Then
        expect(ha.get('.menu').thatContains('Group')).not.toHaveClass('menu--expand');

        // When
        ha.get('.menu').thatContains('Group').click();
        // Then
        expect(ha.get('.menu').thatContains('Group')).toHaveClass('menu--expand');

        // When
        ha.get('.menu').thatContains('File').click();
        // Then
        expect(ha.get('.menu').thatContains('Group')).not.toHaveClass('menu--expand');
        expect(ha.get('.menu').thatContains('File')).toHaveClass('menu--expand');
    });
});
