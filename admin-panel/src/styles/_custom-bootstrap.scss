@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";

/*--------------------------------------------------*/
/*       Custom RTL                                 */
/*--------------------------------------------------*/
[dir=rtl] {
    .dropdown-menu {
        text-align: right;

        &#changeInstrumentState {
            left: -132px;
        }
    }

    .dropdown-toggle::after {
        margin-left: auto;
        margin-right: 0.255em;
    }

    .custom-control-label {
        &::before,
        &::after{
            right: -1.5rem;
            left: auto;
        }
    }

    .breadcrumb-item {
        padding-left: 0.5rem;

        &::before {
            padding-left: 0.5rem;
            padding-right: 0;
        }
    }

    .form-check {
        padding-right: 1.25rem;
        padding-left: unset;

        > .form-check-input {
            margin-right: -1.25rem;
            margin-left: unset;
        }
    }

    .input-group{
        > .input-group-prepend{
            margin-right: unset;
            margin-left: -1px;

            > .input-group-text {
                border-radius: 0 .25rem .25rem 0 !important;
            }
        }

        > .form-control:not(:first-child) {
            border-radius: .25rem 0 0 .25rem !important;
        }
    }

    .rotate-by-language {
        transform: rotate(180deg);
    }
}


/*--------------------------------------------------*/
/*       Custom RTL                                 */
/*--------------------------------------------------*/
[dir=ltr] {
    .dropdown-menu {
        right: 0;
        left: auto;

        &#changeInstrumentState {
            left: -30px;
        }
    }

    .dropdown-submenu {
        &:hover > .dropdown-menu {
            left: auto;
            right: 100%;
        }
    }

    .form-check-input {
        margin-left: 0!important;
    }

    .form-check-label {
        left: 1.25rem;
        position: relative;
    }
}


/*--------------------------------------------------*/
/*       Custom styles                              */
/*--------------------------------------------------*/
b,
strong,
.font-weight-bold {
    font-weight: 500 !important;
}

.form-control[disabled] {
    background-color: $input-disabled-bg;
}

select {
    line-height: 1.6;
    appearance: none;
    -moz-appearance:none;
    -webkit-appearance:none;
}


/*--------------------------------------------------*/
/*       New styles                                 */
/*--------------------------------------------------*/
.cursor-pointer {
    cursor: pointer;
}

.dropdown-submenu {
    position: relative;

    &:hover > .dropdown-menu {
        display: block;
        position: absolute;
        top: 0;
        left: 100%;
    }
}

.dropdown-header {
    font-size: 1rem;
    color: unset;
}




//------------------
.validated.ng-invalid {
    .ng-invalid {
        border-color: #dc3545;

        ~ .invalid-feedback,
        ~ required-err > .invalid-feedback {
            display: block;
        }
    }
}

.ng-dirty,
.ng-touched {
    &.ng-invalid {
        border-color: #dc3545;

        ~ .invalid-feedback,
        ~ required-err > .invalid-feedback {
            display: block;
        }
    }
}

app-form-field-container.ng-invalid.ng-dirty {
    .ng-dirty,
    .ng-touched {
        border-color: #dc3545;

        ~ .invalid-feedback,
        ~ required-err > .invalid-feedback {
            display: block;
        }
    }
}

.border-dashed {
    border-style: dashed!important;
}

fieldset {
    padding: revert;
}

legend {
    display: unset;
    width: unset;
    max-width: unset;
    padding: 0 5px;
    margin-bottom: unset;
    font-size: unset;
    line-height: unset;
    color: unset;
    white-space: unset;
}

.breadcrumb-item::before {
    float: unset!important;
}

.min-fit-content {
    min-width: fit-content;
}

.modal-dialog:not(.side-modal) {
    .modal {
        &-header,
        &-body,
        &-footer {
            padding-left: 24px;
            padding-right: 24px;
            border: hidden !important;
        }
    }
}

.modal-open {
    app-root,
    bs-dropdown-container {
        filter: blur(4px);
    }
}

.modal-backdrop {
    background: none;
}

.modal-content {
    background: linear-gradient(0deg, rgba(224, 231, 247, 0.8) 0%, rgba(232, 244, 250, 0.8) 100%);
    backdrop-filter: blur(4px);
    max-height: 95vh;
}

.modal-dialog:not(.non-scrollable-modal-body) {
    &:not(.side-modal) {
        .modal-body {
            max-height: calc(95vh - 210px);
        }
    }

    .modal-body {
        max-height: calc(95vh - 10px);
        overflow-y: auto;
    }

}

.side-modal {
    .modal-content {
        max-height: unset;
    }
}

.mat-expansion-panel {
    background: rgba(255, 255, 255, 0.6)!important;

    &:not(.mat-expanded) {
        box-shadow: 0 3px 1px -2px rgb(0 0 0 / 20%), 0 0 0 0 rgb(0 0 0 / 14%), 0 1px 0 0 rgb(0 0 0 / 12%)!important;
    }
}


.list-group {
    padding-right: 0;
}

table[multitemplatedatarows] {
    tr:not(.mat-detail-row) td {
        border-bottom: unset;
    }
}

.direction-ltr {
    direction: ltr;
}
