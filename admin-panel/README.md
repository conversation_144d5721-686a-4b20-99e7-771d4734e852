# Radin Bourse Admin Panel


### Folder Structure
A modular and scalable folder structure designed for our large Angular projects:

```
src/
├── app/
│   ├── core/
│   │   ├── interceptors/
│   │   ├── guards/
│   │   ├── services/
│   │   ├── models/
│   │   └── core.module.ts
│   ├── shared/
│   │   ├── components/
│   │   ├── directives/
│   │   ├── pipes/
│   │   └── shared.module.ts
│   ├── features/
│   │   ├── dashboard/
│   │   │   ├── components/
│   │   │   ├── pages/
│   │   │   ├── services/
│   │   │   ├── dashboard.module.ts
│   │   │   └── dashboard-routing.module.ts
│   │   ├── users/
│   │   │   ├── components/
│   │   │   ├── pages/
│   │   │   ├── services/
│   │   │   ├── users.module.ts
│   │   │   └── users-routing.module.ts
│   │   └── ...
│   ├── layouts/
│   │   ├── main-layout/
│   │   │   ├── main-layout.component.ts
│   │   │   ├── main-layout.component.html
│   │   │   └── main-layout.component.scss
│   │   ├── auth-layout/
│   │   │   ├── auth-layout.component.ts
│   │   │   ├── auth-layout.component.html
│   │   │   └── auth-layout.component.scss
│   │   └── ...
│   └── app.module.ts
├── assets/
│   ├── images/
│   ├── fonts/
│   └── data/
├── environments/
│   ├── environment.ts
│   └── environment.prod.ts
└── main.ts
```

---

### Folder Descriptions

#### Core
The `core` folder contains singleton services, guards, interceptors, and models that are loaded once and shared across the entire application.

- `interceptors/`: HTTP interceptors (e.g., for tokens or error handling).
- `guards/`: Route guards (e.g., AuthGuard or RoleGuard).
- `services/`: Singleton services like AuthService or LoggerService.
- `models/`: Shared interfaces and models, such as `User` or `Token`.


#### Utils

The `core/utils/` folder contains global utility functions that encapsulate logic used across the application. These are functions that are not tied to any single feature or UI element, and instead serve as logic helpers used in services, components, or guards.

We place utility functions here (rather than in `shared/`) for the following reasons:

- They are **logic-based**, not UI-related.
- They are **used across multiple features**.
- They help avoid **code duplication** and keep feature modules clean.
- `core/` is intended for foundational application logic, which makes it the right home for utilities used throughout the app.

##### Example

A utility like `getInstrumentById(securityId: string)` that retrieves instrument data from a shared store would live in:

```
src/app/core/utils/instrument-utils.ts
```

This helps keep the architecture organized and prepares the codebase for future scalability and reuse.


#### Shared
The `shared` folder includes reusable components, directives, and pipes that are used across multiple features.

- `components/`: UI components like buttons, modals, or loaders.
- `directives/`: Angular directives (e.g., for custom behaviors).
- `pipes/`: Custom pipes for formatting or transforming data.
- `shared.module.ts`: The module that exports shared components, directives, and pipes.

#### Features
The `features` folder is organized by functionality or feature. Each feature has its own module, components, pages, and services.

Example:
```
features/
├── dashboard/
│   ├── components/  # Feature-specific components
│   ├── pages/       # Feature-specific pages
│   ├── services/    # Feature-specific services
│   ├── dashboard.module.ts
│   └── dashboard-routing.module.ts
├── users/
│   ├── components/  # Feature-specific components
│   ├── pages/       # Feature-specific pages
│   ├── services/    # Feature-specific services
│   ├── users.module.ts
│   └── users-routing.module.ts
```

#### Layouts
The `layouts` folder contains layout components for different sections of the app.

- **Main Layout**: Used for authenticated pages like the dashboard.
- **Auth Layout**: Used for unauthenticated pages like login and registration.

Example:
```
layouts/
├── main-layout/
│   ├── main-layout.component.ts
│   ├── main-layout.component.html
│   └── main-layout.component.scss
├── auth-layout/
│   ├── auth-layout.component.ts
│   ├── auth-layout.component.html
│   └── auth-layout.component.scss
```

#### Assets
The `assets` folder contains static files like images, fonts, and JSON data.

- `images/`: Image assets.
- `fonts/`: Custom fonts.
- `data/`: Static data files like mock JSONs.

#### Environments
The `environments` folder contains environment-specific configurations.

- `environment.ts`: Configuration for development.
- `environment.prod.ts`: Configuration for production.

---

### Design Patterns

#### Explicit Setter Methods vs. Property Setters
When designing services that need to receive data from components, we use explicit setter methods (e.g., `setData()`) rather than TypeScript property setters (e.g., `set data()`). This decision is based on the following considerations:

1. **Explicit Intent**: Methods like `setRouteNodes()` make the intent clearer in the calling code. When you see `service.setRouteNodes(nodes)`, it's immediately obvious that you're setting data in the service.
2. **Method Naming Consistency**: Our services follow a method-based API pattern throughout (like `openDialog()`, `hideDialog()`). Using explicit setter methods maintains this consistent style.
3. **Flexibility for Additional Logic**: Explicit methods make it easier to add validation, transformation, or side effects when setting the data.
4. **API Stability**: If the internal implementation changes later, the method signature can remain stable, whereas property setters might need to change if the underlying data structure changes.

#### Specialized Dialog Services

To improve code organization and maintainability, we extract dialog-related functionality into dedicated dialog services. This pattern is particularly useful for features with complex UI interactions that involve multiple dialogs.

**Benefits:**

1. **Separation of Concerns**: Keeps component logic focused on its primary responsibility while dialog management is handled by a specialized service.
2. **Reusability**: Dialog opening, configuration, and data transformation logic can be reused across multiple components.
3. **Testability**: Dialog-related logic becomes easier to test in isolation.
4. **Reduced Component Complexity**: Components become smaller and more focused when dialog management is extracted.

**Implementation Pattern:**

- Create a dedicated service with the naming convention `[feature]-dialog.service.ts`
- The service should handle all dialog-related operations including:
    - Opening and closing dialogs
    - Preparing data for display in dialogs
    - Handling dialog callbacks and events
    - Managing dialog state and configuration

---

### Best Practices
1. **Feature-based Organization**:
   - Each feature has its own folder with components, services, and routing.
2. **Reusable Modules**:
   - Use `SharedModule` for reusable components, directives, and pipes.
   - Use `CoreModule` for singleton services and global configurations.
3. **Lazy Loading**:
   - Load feature modules lazily to improve performance.
4. **Service Specialization**:
    - Extract specialized functionality into dedicated services (e.g., dialog services, data services)
    - Name services according to their specific responsibility (e.g., `cap-groups-dialog.service.ts`)

## 📚 Documentation

- [Naming & Code Style Guide](./docs/naming-convention.md)
