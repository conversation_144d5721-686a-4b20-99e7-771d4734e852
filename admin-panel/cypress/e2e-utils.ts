import {BASE_URL} from './env-e2e';

export function getApiUrl(path) {
    return BASE_URL + path;
}

export function parseStepsTable(table) {
    const tableData = [];
    const headers = table.rawTable[0];

    for (let i = 1; i < table.rawTable.length; i++) {
        const row = {};
        table.rawTable[i].forEach((cell, index) => {
            row[headers[index]] = cell;
        });

        tableData.push(row);
    }

    return tableData.length > 1 ? tableData : tableData[0];
}

export function changeSecurityState(securityId: string, securityState: string, callback: (resp) => void = () => {}): void {
    const requestOptions = {
        method: 'POST',
        url: getApiUrl(`/securities/${securityId}/state`),
        body: {targetState: securityState},
        failOnStatusCode: false
    }
    cy.request(requestOptions).then(resp => callback(resp));
}
