Feature: Shareholder
    Background:
        Given Market is started
        Given I am in the "home" page
          And I searched shareholder with shareholder id "45191270768182"


    Scenario: Search shareholder
        Then I should see shareholder info with shareholder id "45191270768182"


    Scenario: Get shareholder positions
        When I click on the shareholder positions button
        Then I should see a datatable with some rows


#    Scenario Outline: Transfer shareholder share
#        When I click on the transfer share button
#         And I fill transfer share inputs with the following data
#            | productId | quantity | destinationId  |
#            | <productId> | 5000   | 11293490022726 |
#         And I click on submit button on the transfer share dialog
#        Then I should see admin command "<messageType>" message
#
#        Examples:
#            | productId        | messageType |
#            | IRO1KVIR0002 | success     |
#            | US78462F1030 | error       |


    Scenario: Block shareholder
        When I click on the block shareholder button
         And I check block buy-side checkbox
         And I click on submit button on the block shareholder dialog
        Then I should see admin command "success" message
