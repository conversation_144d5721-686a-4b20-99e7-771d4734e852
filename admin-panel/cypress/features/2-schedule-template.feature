Feature: Schedule template
    Background:
        Given Market is started
        Given System state is in "TRADING_SESSION"
        Given I am in the "home/scheduleTemplates" page

    Scenario: Add new schedule template
        Given Schedule template dialog is open
        When I add new schedule template with fallowing data
            | enName | faName | code | runAt    | targetState  | shouldOpen |
            | EnName | FaName | T1   | 10:00:00 | SURVEILLANCE | true       |
        And I wait for "100" seconds
        Then I should see the column number "1" with value "T1" in the new datatable
            | enName | faName | code | runAt    | targetState  |
            | EnName | FaName | T1   | 10:00:00 | SURVEILLANCE |


    Scenario: Delete schedule template

        Given Schedule template exists with the following data
            | enName | faName | code | runAt    | targetState  | shouldOpen |
            | EnName | FaName | T1   | 10:00:00 | SURVEILLANCE | true       |
        When I click on schedule template "T1" delete button
        Then I should not see schedule template with code "T1"
