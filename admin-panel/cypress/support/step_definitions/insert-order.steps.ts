import {Then, When} from '@badeball/cypress-cucumber-preprocessor';
import {parseStepsTable} from '../../e2e-utils';

When('I change instrument state to the {string}', (state: string) => {
    cy.get('app-change-instrument-state .mat-mdc-menu-trigger').click();
    cy.contains('[mat-menu-item]', state).then((button) => {
        if (!button.prop('disabled')) {
            cy.get('[mat-menu-item]').contains(state).click();
            cy.get('button').contains('Submit').click();
        } else {
            cy.get('body').type('{esc}');
        }
    });
});

When('I open the {string} dialog-box', (dialogName: string) => {
    cy.get('button').contains(dialogName).click();
});

When('I insert order with the following values', args => {
    const data = parseStepsTable(args);

    cy.get('mat-button-toggle button').contains(data.side).click();
    cy.get('app-form-field-container#price input').type(data.price);
    cy.get('app-form-field-container#quantity input').type(data.quantity);
    cy.get('app-form-field-container#shareholderId input').type(data.shareholder);
    cy.get('app-form-field-container#traderOrderNumber input').type(data.traderOrderNumber);
    cy.get('app-form-field-container#bankCode input').type('123');
    cy.get('app-form-field-container#branchCode input').type('123');

    cy.get('app-form-field-container#brokerId input').type(data.broker);
    cy.get('mat-option').contains(data.broker).click();

    cy.get('app-form-field-container#giveUpBrokerId input').type(data.giveUpBrokerId);
    cy.get('mat-option').contains(data.giveUpBrokerId).click();

    cy.get('app-form-field-container#townCode input').type('12');
    cy.get('mat-option').contains('12').click();

    cy.get('app-form-field-container#traderId input').type(data.traderId);
    cy.get('mat-option').contains(data.traderId).click();

    cy.get('.modal-footer button').contains(data.side).click();
    cy.contains('button', 'Confirm').click();

    cy.wait(1000);
});

Then('I should see "Request Type" with value "INSERT"', () => {
    cy.get('div').contains('Request Type')
        .parent().contains('INSERT').should('exist');
});
