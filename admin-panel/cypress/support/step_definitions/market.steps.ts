import {Given} from '@badeball/cypress-cucumber-preprocessor';
import {getApiUrl} from '../../e2e-utils';

Given('Market is started', () => {
    startMarket();
});

export function startMarket(): void {
    const requestOptions = {
        method: 'POST',
        url: getApiUrl('/initialization'),
        body: null,
        failOnStatusCode: false
    }

    requestAndStoreMarketStatus(requestOptions).then(() => {
        cy.wait(5000);
    });
}

function requestAndStoreMarketStatus(requestBody: any): Cypress.Chainable<any> {
    const key = 'CY_ENV_KEY_MARKET';
    const storedResponse = Cypress.env(key);

    if (storedResponse) {
        return cy.wrap(storedResponse);
    }

    return cy.request(requestBody).then(response => {
        Cypress.env(key, true);
        return response;
    });
}
