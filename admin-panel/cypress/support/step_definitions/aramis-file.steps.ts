import {Given, Then, When} from '@badeball/cypress-cucumber-preprocessor';
import {getApiUrl} from '../../e2e-utils';

Given('Get ARAMIS files dialog is open', () => {
    cy.get('.sidenav > div:last-child .menu:last-child').click();
    cy.get('.sidenav div').contains('File').click()
    cy.get('.sidenav div').contains('Get ARAMIS Files').click();
});

When('I delete instrument {string}', (securityId) => {
    cy.wait(5000);

    const requestOptions = {
        method: 'DELETE',
        url: getApiUrl(`/securities/${securityId}`),
        body: null,
        failOnStatusCode: false
    }

    cy.request(requestOptions);
});

When('I click on the declare remaining orders button', () => {
    cy.contains('button', 'campaign').click();
    cy.contains('button', 'Confirm').click();
    cy.wait(3000);
});

When('I click on download link {string}', (link) => {
    cy.contains('a', link).click();
    cy.wait(5000);
});

Then('File {string} should have at least {string} rows', (fileName, rows) => {
    cy.task('readMatchingFile', {
        directory: 'cypress/downloads',
        regex: fileName + '[_0-9]*'
    }).then((content: string) => {
        const lines = content.split('\r\n');
        expect(lines.length).be.greaterThan(+rows);
    });
});
