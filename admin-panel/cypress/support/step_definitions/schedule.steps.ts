import {Given, Then, When} from '@badeball/cypress-cucumber-preprocessor';
import {getApiUrl, parseStepsTable} from '../../e2e-utils';

Given('Schedule template dialog is open', () => {
    cy.get('button').contains('Add Group State Schedule Template').click();
});

Given('Schedule template exists with the following data', args => {
    const data = parseStepsTable(args);

    const body = {
        name: {
            en: data.enName,
            fa: data.faName
        },
        code: data.code,
        type: 'GROUP',
        groupActions: [
            {
                runAt: data.runAt,
                targetState: data.targetState,
                shouldOpen: data.shouldOpen
            }
        ]
    }

    const requestOptions = {
        method: 'POST',
        url: getApiUrl('/schedule-templates'),
        body,
        failOnStatusCode: false
    }
    cy.request(requestOptions);

});

Given('Schedule template item with code {string} is open', templateCode => {
    cy.get('div')
        .contains(templateCode + ' -')
        .parent().parent().parent().click();
});

When('I associate schedule template with code {string} to group', (templateCode: string) => {
    cy.get('button').contains('Associate Schedule Template').click({force: true});

    cy.contains('div', 'Apply On').find('.form-control').click();
    cy.get('.today + .picker').click();

    cy.get('#scheduleTemplates > div:nth-child(2) select').select(templateCode);
    cy.get('#scheduleTemplates > div:nth-child(4) select').select(templateCode);
    cy.get('#scheduleTemplates > div:nth-child(6) select').select(templateCode);
    cy.get('#scheduleTemplates > div:nth-child(8) select').select(templateCode);
    cy.get('#scheduleTemplates > div:nth-child(10) select').select(templateCode);
    cy.get('#scheduleTemplates > div:nth-child(12) select').select(templateCode);
    cy.get('#scheduleTemplates > div:nth-child(14) select').select(templateCode);

    cy.get('button').contains('Submit').click();
});

When('I click on exclude instrument button', () => {
    cy.get('button').contains('more_vert').click();
    cy.get('button').contains('Include to group schedule template').click();
});

When('I add new schedule template with fallowing data', args => {
    const data = parseStepsTable(args);

    cy.get('input#code').type(data.code);
    cy.get('input#fa').type(data.faName);
    cy.get('input#en').type(data.enName);

    cy.get('button').contains('Add Schedule Action').click();

    cy.get('input#runAt').type(data.runAt);
    cy.get('#targetState select').select(data.targetState.charAt(0).toUpperCase() + data.targetState.slice(1).toLowerCase());
    cy.contains('mat-checkbox', 'Should Open').click();
    cy.get('button').contains('Submit').click();
});

When('I click on schedule template {string} delete button', (templateCode: string) => {
    cy.wait(1000);
    cy.contains('td:nth-child(1)', templateCode)
        .parent()
        .contains('button', 'more_vert').click().then(() => {
        cy.contains('button[mat-menu-item]', 'Delete').click({force: true});
    });

    cy.get('button').contains('Confirm').click();
});

Then('I have to see instrument {string}', (symbol: string) => {
    cy.contains('app-page-panel button', 'Associated Templates').click();

    cy.get('app-range-datepicker-filter > input').click();
    cy.get('.today + .picker').click({ multiple: true });
    cy.get('body').click();

    cy.get('td').contains(symbol).should('exist');
});

Then('Data table should be empty', () => {
    cy.contains('td', 'No Data to Display').should('exist');
});

Then('I should see schedule template item with fallowing data', args => {
    const data = parseStepsTable(args);

    cy.contains('mat-panel-title', data.enName).should('exist');
    cy.contains('mat-panel-title', data.code).should('exist');
});

Then('I should not see schedule template with code {string}', (templateCode: string) => {
    cy.get('.modal-body').contains(templateCode).should('not.exist');
});


Then('Associated template data table should be empty', () => {
    cy.get('.mat-no-data-row').should('exist');
});
