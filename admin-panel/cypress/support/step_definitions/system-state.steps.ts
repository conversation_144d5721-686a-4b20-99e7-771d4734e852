import {Given, When} from '@badeball/cypress-cucumber-preprocessor';
import {getApiUrl} from '../../e2e-utils';

Given('System state is in {string}', (targetState: string) => {
    changeSystemState(targetState, () => {});
});

When('I change System state to the {string}', (targetState: string) => {
    changeSystemState(targetState, () => {});
});

export function changeSystemState(targetState: string, callback) {
    const requestOptions = {
        method: 'POST',
        url: getApiUrl(`/system/state/${targetState}`),
        body: null,
        failOnStatusCode: false
    }

    requestAndStoreSystemStateChange(requestOptions, targetState).then(() => {
        cy.wait(5000);
        callback();
    });
}

function requestAndStoreSystemStateChange(requestBody: any, expectedResponse: string): Cypress.Chainable<any> {
    const key = 'CY_ENV_KEY';
    const storedResponse = Cypress.env(key);

    if (storedResponse?.body?.commandRequest?.targetState === expectedResponse) {
        return cy.wrap(storedResponse);
    }

    return cy.request(requestBody).then(response => {
        Cypress.env(key, response);
        return response;
    });
}
