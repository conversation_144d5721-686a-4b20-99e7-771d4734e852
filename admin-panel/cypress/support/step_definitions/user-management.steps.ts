import {Then, When} from '@badeball/cypress-cucumber-preprocessor';
import {parseStepsTable} from '../../e2e-utils';

When('I insert the following data for the new user', (args) => {
    const data = parseStepsTable(args);

    cy.get('input#username').type(data.username);
    cy.get('input#password').type(data.password);
    cy.get('app-multi-picklist div').contains(data.role).dblclick();

    cy.get('button').contains('Submit').click();
});

When('I block user', () => {
    cy.get('input#blocked').check();

    cy.get('button').contains('Edit').click();
});

Then('I should see the added user in the user list', (args) => {
    const data = parseStepsTable(args);

    cy.get('td:nth-child(1)').contains(data.username).should('exist');
    cy.get('td:nth-child(2)').contains(data.role).should('exist');
});
