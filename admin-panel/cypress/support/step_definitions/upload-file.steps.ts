import {Given, Then, When} from '@badeball/cypress-cucumber-preprocessor';
import {parseStepsTable} from '../../e2e-utils';

Given('Order file is uploaded', () => {
    cy.get('.sidenav > div:last-child .menu:last-child').click();
    cy.get('.sidenav div').contains('File').click()
    cy.get('.sidenav button').contains('Upload Orders File').click();

    const filePath = 'cypress/fixtures/order-file.fix';
    cy.get('input[type="file"').selectFile(filePath, { force: true });
    cy.get('button').contains('Upload File').click();
    cy.get('.alert-info').contains('Status: Completed');
    cy.get('button.close').click();

    cy.get('.sidenav > div:last-child .menu:last-child').click();
    cy.wait(5000);
});

Given('Upload CSD file dialog is open', () => {
    cy.reload();
    cy.get('.sidenav > div:last-child .menu:last-child').click();
    cy.get('.sidenav div').contains('File').click()
    cy.get('.sidenav button').contains('Upload CSD File').click();
});

Given('Upload order file dialog is open', () => {
    cy.get('.sidenav > div:last-child .menu:last-child').click();
    cy.get('.sidenav div').contains('File').click()
    cy.get('.sidenav button').contains('Upload Orders File').click();
});

When('I uploaded {string} file', fileName => {
    const filePath = 'cypress/fixtures/' + fileName;

    cy.get('input[type="file"').selectFile(filePath, { force: true });
    cy.get('button').contains('Upload File').click();
});

Then('I should see upload progress info', () => {
    cy.get('.alert.alert-info').should('exist');
    cy.get('.alert.alert-info').contains('Processing...').should('not.exist');
});

Then('When I go to page {string} I should see the following row in the trades-page table', (url: string, args) => {
    const data = parseStepsTable(args);

    cy.visit(url);
    cy.contains('button', 'refresh').click();
    cy.contains('td:nth-child(4)', data.quantity).should('exist');
    cy.contains('td:nth-child(5)', data.price).should('exist');
});

Then('When I go to the page {string} I should see the following row in the order-book-page table', (url: string, args) => {
    const data = parseStepsTable(args);

    cy.visit(url);
    cy.contains('td:nth-child(6)', data.price).should('exist');
});
