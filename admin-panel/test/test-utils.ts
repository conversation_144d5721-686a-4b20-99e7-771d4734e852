import {read<PERSON><PERSON><PERSON>} from './read-json';
import {Group} from '@models/group';
import {Page} from '@models/page';
import {ScheduleTemplate} from '@models/schedule-template/schedule-template';
import {M1} from '@enums/m1';
import {AdminCommand} from '@models/admin-command';
import {Schedule, UnSchedule} from '@models/schedule';
import {UserRole} from '@models/user-role';
import {User} from '@models/user';
import {environment} from '../src/environments/environment';
import {QueuedCommand} from '@models/queued-command';
import {Shareholder} from '@models/shareholder';
import {Investor} from '@models/investor';
import {UploadOrderStatus} from '@models/upload';
import {Product} from '@homeModels/product';
import {InitialOrderBook, OrderBook} from '@models/order/order-book';
import {StoreService} from '@shared/services/store.service';
import {CsdFile, FailedCsdRecord} from '@models/csd-file';
import {Board} from '@models/board';
import {Sector} from '@models/sector';
import {SubSector} from '@models/sub-sector';
import {Permission} from '@models/permission';
import {Trader} from '@models/trader';
import {Company} from '@models/company';
import {OrderRequestReport} from '@models/order/order-request-report';
import {EventReport} from '@layouts/main-layout/header/event-reports-dialog/event-report/event-report';
import {Trade} from '@models/trade';
import {Instrument} from '@homeModels/instrument';
import {TraderAccessMatrix} from '@homeModels/access-matrix/trader-access-matrix';
import {BrokerAccessMatrix} from '@homeModels/access-matrix/broker-access-matrix';
import {Origin} from '@homeModels/origin';
import {RoutePredicate} from '@homeModels/route-predicate';
import {Broker} from '@homeModels/broker';
import {plainToInstance} from '@shared/plain-to-instance';

export class TestUtils {
    static url(path: string): string {
        return environment.apiBaseUrl + path;
    }

    private static _getFilePath(fileName: string): string {
        return `base/test/mock-files/${fileName}.json`;
    }

    static getInstruments(): Page<Instrument.Simple> {
        const instruments = readJSON(this._getFilePath('instruments'));
        instruments.content = plainToInstance<Instrument.Simple, Instrument.Simple[]>(Instrument.Simple, instruments.content);

        return instruments;
    }

    static storeInstrumentsAndGroups(): void {
        StoreService.instrumentsObj = TestUtils.getInstrumentsObj();
        StoreService.instruments = TestUtils.getInstruments().content;
        StoreService.groupsObj = TestUtils.getGroupsObj();
        StoreService.groups = TestUtils.getGroups().content;
    }

    static purgeInstrumentsAndGroups(): void {
        StoreService.instrumentsObj = {};
        StoreService.instruments = [];
        StoreService.groupsObj = {};
        StoreService.groups = [];
    }

    static getInstrumentsObj(): { [instrumentId: string]: Instrument.Simple } {
        const instruments = readJSON(this._getFilePath('instruments'));
        instruments.content = plainToInstance(Instrument.Simple, instruments.content);

        const obj = {};
        instruments.content.forEach(instrument => obj[instrument.securityId] = instrument);

        return obj;
    }

    static getInstrument(): Instrument.Simple {
        return TestUtils.getInstruments().content[0];
    }

    static getInstrumentDynamicData(): Instrument.DynamicData {
        const instruments = readJSON(this._getFilePath('instrumentDynamic'));
        return plainToInstance(Instrument.DynamicData, instruments);
    }

    static getInstrumentWithDynamicData(): Instrument.DynamicData {
        const instrument = readJSON(this._getFilePath('instruments')).content[0];
        const dynamic = readJSON(this._getFilePath('instrumentDynamic'));

        return plainToInstance(Instrument.DynamicData, Object.assign(instrument, dynamic) as Instrument.Single);
    }

    static getPlainGroups(): Page<any> {
        return readJSON(this._getFilePath('groups'));
    }

    static getGroups(): Page<Group> {
        const groups = readJSON(this._getFilePath('groups'));
        groups.content = plainToInstance(Group, groups.content);

        return groups;
    }

    static getGroupsObj(): { [groupCode: string]: Group } {
        const groups = readJSON(this._getFilePath('groups'));
        groups.content = plainToInstance(Group, groups.content);

        const obj = {};
        groups.content.forEach(group => obj[group.code] = group);

        return obj;
    }

    static getGroup(): Group {
        return TestUtils.getGroups().content[0];
    }

    static getBoards(): Page<Board> {
        const boards = readJSON(this._getFilePath('boards'));
        boards.content = plainToInstance(Board, boards.content);

        return boards;
    }

    static getSectors(): Page<Sector> {
        const sectors = readJSON(this._getFilePath('sectors'));
        sectors.content = plainToInstance(Sector, sectors.content);

        return sectors;
    }

    static getSubSectors(): SubSector[] {
        return readJSON(this._getFilePath('subSectors'));
    }
    static getScheduleTemplates(): Page<ScheduleTemplate> {
        const scheduleTemplates: Page<ScheduleTemplate> = readJSON(this._getFilePath('scheduleTemplates'));
        scheduleTemplates.content = plainToInstance(ScheduleTemplate, scheduleTemplates.content);

        return scheduleTemplates;
    }

    static getScheduleTemplate(): ScheduleTemplate {
        const scheduleTemplate: ScheduleTemplate = readJSON(this._getFilePath('scheduleTemplate'));

        return  plainToInstance(ScheduleTemplate, scheduleTemplate);
    }

    static getBrokers(): Page<Broker.Simple> {
        const brokers = readJSON(this._getFilePath('brokers'));
        brokers.content = plainToInstance(Broker.Simple, brokers.content);

        return brokers;
    }

    static getBrokersObj(): { [prop: string]: Broker.Simple } {
        const brokers = this.getBrokers();

        const obj = {};
        brokers.content.forEach(group => obj[group.id] = group);

        return obj;
    }

    static getM1Messages(): Page<M1> {
        return readJSON(this._getFilePath('m1-messages'));
    }

    static getM1File(): M1[] {
        return readJSON(this._getFilePath('m1File'));
    }

    static getPlainAdminCommands(): Page<any> {
        return readJSON(this._getFilePath('commands'));
    }

    static getCommands(): Page<AdminCommand> {
        const commands = readJSON(this._getFilePath('commands'));
        commands.content = plainToInstance(AdminCommand, commands.content);

        return commands;
    }

    static getStartMarketCommand(): AdminCommand {
        const command = readJSON(this._getFilePath('start-market-command'));
        return plainToInstance(AdminCommand, command);
    }


    static getCreateTradeCommand(): AdminCommand {
        const command = readJSON(this._getFilePath('create-trade-command'));
        return plainToInstance(AdminCommand, command);
    }

    static getUpdateProductCommand(): AdminCommand {
        const command = readJSON(this._getFilePath('update-product-command'));
        return plainToInstance(AdminCommand, command);
    }

    static getAddInstrumentCommand(): QueuedCommand {
        const command = readJSON(this._getFilePath('add-instrument-queued-command'));
        return plainToInstance(QueuedCommand, command);
    }

    static getCreateProductCommand(): QueuedCommand {
        const command = readJSON(this._getFilePath('create-product-queued-command'));
        return plainToInstance(QueuedCommand, command);
    }

    static getUpdateSecurityQueuedCommand(): QueuedCommand {
        const command = readJSON(this._getFilePath('update-security-queued-command'));
        return plainToInstance(QueuedCommand, command);
    }

    static getBonusQueuedCommand(): QueuedCommand {
        const command = readJSON(this._getFilePath('bonus-queued-command'));
        return plainToInstance(QueuedCommand, command);
    }

    static getRightsQueuedCommand(): QueuedCommand {
        const command = readJSON(this._getFilePath('rights-queued-command'));
        return plainToInstance(QueuedCommand, command);
    }

    static getCustomQueuedCommand(): QueuedCommand {
        const command = readJSON(this._getFilePath('custom-queued-command'));
        return plainToInstance(QueuedCommand, command);
    }

    static getBonusRightsQueuedCommand(): QueuedCommand {
        const command = readJSON(this._getFilePath('bonus-rights-queued-command'));
        return plainToInstance(QueuedCommand, command);
    }

    static getDividendsQueuedCommand(): QueuedCommand {
        const command = readJSON(this._getFilePath('dividends-queued-command'));
        return plainToInstance(QueuedCommand, command);
    }


    static getTransferShareCommand(): AdminCommand {
        const command = readJSON(this._getFilePath('transfer-share-command'));
        return plainToInstance(AdminCommand, command);
    }

    static getChangeInstrumentPriceBandCommand(): AdminCommand {
        const command = readJSON(this._getFilePath('change-instrument-price-band-command'));
        return plainToInstance(AdminCommand, command);
    }

    static getChangeGroupPriceBandCommand(): AdminCommand {
        const command = readJSON(this._getFilePath('change-group-price-band-command'));
        return plainToInstance(AdminCommand, command);
    }

    static getCancelTradeCommand(): AdminCommand {
        const command = readJSON(this._getFilePath('cancel-trade-command'));
        return plainToInstance(AdminCommand, command);
    }

    static getCancelOrderCommand(): AdminCommand {
        const command = readJSON(this._getFilePath('cancel-order-command'));
        return plainToInstance(AdminCommand, command);
    }

    static getInstrumentStateCommand(): AdminCommand {
        const command = readJSON(this._getFilePath('instrument-state-command'));
        return plainToInstance(AdminCommand, command);
    }

    static getOrderAdministrationCommand(): AdminCommand {
        const command = readJSON(this._getFilePath('order-administration-command'));
        return plainToInstance(AdminCommand, command);
    }

    static getPostSessionCommand(): AdminCommand {
        const command = readJSON(this._getFilePath('post-session-command'));
        return plainToInstance(AdminCommand, command);
    }

    static getOrderBook(): Page<OrderBook> {
        const resp = readJSON(this._getFilePath('orders'));
        resp.content = plainToInstance(OrderBook, resp.content);
        return resp;
    }

    static getOrderInitialState(): Page<InitialOrderBook> {
        const resp = readJSON(this._getFilePath('orders-initial-state'));
        resp.content = plainToInstance(InitialOrderBook, resp.content);
        return resp;
    }

    static getOrderRequestReports(): Page<OrderRequestReport> {
        const resp = readJSON(this._getFilePath('order-request-report'));
        resp.content = plainToInstance(OrderRequestReport, resp.content);
        return resp;
    }

    static getSchedules(): Page<Schedule> {
        const resp = readJSON('base/test/mock-files/schedules.json');
        resp.content = plainToInstance(Schedule, resp.content);

        return resp;
    }

    static getUnSchedules(): Page<UnSchedule> {
        const resp = readJSON('base/test/mock-files/unschedules.json');
        resp.content = plainToInstance(UnSchedule, resp.content);

        return resp;
    }

    static getRoles(): Page<UserRole> {
        const resp = readJSON(this._getFilePath('roles'));
        resp.content = resp.content = plainToInstance(UserRole, resp.content);

        return resp;
    }

    static getUsers(): Page<User> {
        const resp = readJSON(this._getFilePath('users'));
        resp.content = resp.content = plainToInstance(User, resp.content);

        return resp;
    }

    static getPermissions(): Page<Permission> {
        const resp = readJSON(this._getFilePath('permissions'));
        resp.content = plainToInstance(Permission, resp.content);

        return resp;
    }

    static getMyPermissions(): Permission[] {
        return readJSON(this._getFilePath('my-permissions'));
    }


    static getQueuedCommand(): QueuedCommand {
        const resp = readJSON(this._getFilePath('queued-command'));
        return plainToInstance(QueuedCommand, resp[0]);
    }

    static getQueuedCommands(): QueuedCommand[] {
        const resp = readJSON(this._getFilePath('queued-command'));
        return plainToInstance(QueuedCommand, resp as []);
    }

    static getPlainPositions() {
        return readJSON(this._getFilePath('positions'));
    }

    static getPositions(): Shareholder.Position {
        const resp = readJSON(this._getFilePath('positions'));
        resp.content = resp.content = plainToInstance(Shareholder.Position, resp.content);

        return resp;
    }

    static getPlainShareholder() {
        return readJSON(this._getFilePath('shareholder'));
    }

    static getShareholder(): Shareholder {
        const resp = readJSON(this._getFilePath('shareholder'));
        return plainToInstance(Shareholder, resp);
    }

    static getPlainInvestor() {
        return readJSON(this._getFilePath('investor'));
    }

    static getInvestor(): Investor {
        const resp = readJSON(this._getFilePath('investor'));
        return plainToInstance(Investor, resp);
    }

    static getPlainTrader() {
        return readJSON(this._getFilePath('trader'));
    }

    static getTraders(): Trader[] {
        const resp = readJSON(this._getFilePath('traders'));
        return plainToInstance<Trader, Trader>(Trader, resp.content);
    }

    static getTrader(): Trader {
        const resp = readJSON(this._getFilePath('trader'));
        return plainToInstance(Trader, resp);
    }

    static getEventReports(): Page<EventReport> {
        const resp: Page<EventReport> = readJSON(this._getFilePath('report'));
        resp.content = plainToInstance(EventReport, resp.content);
        return resp;
    }

    static getTrades(): Page<any> {
        return readJSON(this._getFilePath('trades'));
    }

    static getPlainOrders(): Page<any> {
        return readJSON(this._getFilePath('orders'));
    }

    static getPlainInitialStateOfOrders(): Page<any> {
        return readJSON(this._getFilePath('orders-initial-state'));
    }

    static getPlainOrderUploadStatus(): UploadOrderStatus {
        return readJSON(this._getFilePath('orders-upload'));
    }

    static getPlainInitialStateOfOrder() {
        return readJSON(this._getFilePath('order-initial-state'));
    }

    static getProducts(): Page<Product.Simple> {
        const products = readJSON(this._getFilePath('products'));
        products.content = plainToInstance(Product.Simple, products.content);

        return products;
    }

    static getPlainCompanies(): Page<Company> {
        return readJSON(this._getFilePath('companies'));
    }

    static getCompanies(): Page<Company> {
        const companies = readJSON(this._getFilePath('companies'));
        companies.content = plainToInstance(Company, companies.content);

        return companies;
    }

    static getProduct(): Product.Simple {
        return TestUtils.getProducts().content[0];
    }

    static getClearingReport(): CsdFile[] {
        return readJSON(this._getFilePath('clearing-report'));
    }

    static getFailedCsdRecords(): Page<FailedCsdRecord> {
        return readJSON(this._getFilePath('failed-csd-records'));
    }

    static getTraderGroup(): Page<TraderAccessMatrix> {
        const resp = readJSON(this._getFilePath('trader-group'));
        resp.content = plainToInstance(TraderAccessMatrix, resp.content);

        return resp;
    }

    static getBrokerGroup(): Page<BrokerAccessMatrix> {
        const resp = readJSON(this._getFilePath('broker-group'));
        resp.content = plainToInstance(BrokerAccessMatrix, resp.content);

        return resp;
    }

    static getOrigins(): Origin[] {
        const origins = readJSON(this._getFilePath('origins'));
        return plainToInstance<Origin, Origin>(Origin, origins.content);
    }

    static getCancelReports(): Page<Trade.CancelDetail> {
        const resp = readJSON(this._getFilePath('cancel-reports'));
        resp.content = plainToInstance(Trade.CancelDetail, resp.content);

        return resp;
    }

    static getCancelReportDetails(): Trade.CancelDetail {
        const resp = readJSON(this._getFilePath('cancel-reports-details'));
        return plainToInstance(Trade.CancelDetail, resp);
    }

    static getRoutePredicates(): Page<RoutePredicate> {
        const routePredicate = readJSON(this._getFilePath('route-predicates'));
        routePredicate.content = plainToInstance(RoutePredicate, routePredicate.content);

        return routePredicate;
    }
}
