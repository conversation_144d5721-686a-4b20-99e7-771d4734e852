
#
## Given: there are a lot of resources
#
GET /securities/IRO1SPY0000-/orders
Accept: application/json

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Security' not resolvable by 'IRO1SPY0000-'"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
#
GET /securities/IRO1SPY00001/orders?side=BUY
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 0
  },
  "element": {
    "count": 0,
    "offset": 0,
    "total": 0
  }
}

########################################################################################################################


#
## Given: there are a lot of resources
#
GET /securities/IRO1SPY00001/orders?side=BUY
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "securityId": "IRO1SPY00001",
      "sequenceId": "2",
      "internalSubscriberReference": "****************",
      "type": "LIMIT",
      "price": "110.000",
      "quantity": 100,
      "initialQuantity": 100,
      "displayedQuantity": 100,
      "shareholderId": "**********",
      "brokerId": "110",
      "originId": "1",
      "technicalOrigin": "MANUAL",
      "priorityDateTime": "2021-02-21T15:46:20",
      "side": "BUY",
      "minimumQuantity": 0,
      "validityQualifierType": "GOOD_TILL_DATE",
      "validityDate": "2021-02-22",
      "clearingData": {
        "traderId": "********",
        "traderOrderNumber": "1",
        "brokerOrderEntryDateTime": "2020-06-17T15:38:23",
        "brokerBusinessIdentificationCode": {
          "bankCode": "110",
          "countryCode": "IR",
          "townCode": "98",
          "branchCode": "020"
        },
        "freeText": "random free text",
        "giveUpBrokerId": "110"
      }
    },
    {
      "securityId": "IRO1SPY00001",
      "sequenceId": "1",
      "internalSubscriberReference": "****************",
      "type": "LIMIT",
      "price": "100.000",
      "quantity": 100,
      "initialQuantity": 100,
      "displayedQuantity": 100,
      "shareholderId": "**********",
      "brokerId": "110",
      "originId": "1",
      "technicalOrigin": "MANUAL",
      "priorityDateTime": "2021-02-21T15:46:20",
      "side": "BUY",
      "minimumQuantity": 0,
      "validityQualifierType": "GOOD_TILL_DATE",
      "validityDate": "2021-02-22",
      "clearingData": {
        "traderId": "********",
        "traderOrderNumber": "1",
        "brokerOrderEntryDateTime": "2020-06-17T15:38:23",
        "brokerBusinessIdentificationCode": {
          "bankCode": "110",
          "countryCode": "IR",
          "townCode": "98",
          "branchCode": "020"
        },
        "freeText": "random free text",
        "giveUpBrokerId": "110"
      }
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 2,
    "offset": 0,
    "total": 2
  }
}


########################################################################################################################

#
## Given: there are a lot of resources
#
GET /securities/IRO1SPY00001/orders?side=SELL
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "securityId": "IRO1SPY00001",
      "sequenceId": "3",
      "internalSubscriberReference": "****************",
      "type": "LIMIT",
      "price": "120.000",
      "quantity": 100,
      "initialQuantity": 100,
      "displayedQuantity": 100,
      "shareholderId": "**********",
      "brokerId": "110",
      "originId": "1",
      "technicalOrigin": "MANUAL",
      "priorityDateTime": "2021-02-21T15:46:20",
      "side": "SELL",
      "minimumQuantity": 0,
      "validityQualifierType": "GOOD_TILL_DATE",
      "validityDate": "2021-02-22",
      "clearingData": {
        "traderId": "********",
        "traderOrderNumber": "1",
        "brokerOrderEntryDateTime": "2020-06-17T15:38:23",
        "brokerBusinessIdentificationCode": {
          "bankCode": "110",
          "countryCode": "IR",
          "townCode": "98",
          "branchCode": "020"
        },
        "freeText": "random free text",
        "giveUpBrokerId": "110"
      }
    },
    {
      "securityId": "IRO1SPY00001",
      "sequenceId": "4",
      "internalSubscriberReference": "****************",
      "type": "ICEBERG",
      "price": "130.000",
      "quantity": 100,
      "disclosedQuantity": 15,
      "initialQuantity": 100,
      "displayedQuantity": 15,
      "shareholderId": "**********",
      "brokerId": "110",
      "originId": "1",
      "technicalOrigin": "MANUAL",
      "priorityDateTime": "2021-02-21T15:46:20",
      "side": "SELL",
      "minimumQuantity": 0,
      "validityQualifierType": "GOOD_TILL_DATE",
      "validityDate": "2021-02-22",
      "clearingData": {
        "traderId": "********",
        "traderOrderNumber": "1",
        "brokerOrderEntryDateTime": "2020-06-17T15:38:23",
        "brokerBusinessIdentificationCode": {
          "bankCode": "110",
          "countryCode": "IR",
          "townCode": "98",
          "branchCode": "020"
        },
        "freeText": "random free text",
        "giveUpBrokerId": "110"
      }
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 2,
    "offset": 0,
    "total": 2
  }
}


########################################################################################################################

#
## Given: there are a lot of resources
#
GET /securities/IRO1SPY00001/orders?side=SELL&page=100
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [],
  "page": {
    "size": 10000,
    "number": 100,
    "total": 1
  },
  "element": {
    "count": 0,
    "offset": 2,
    "total": 2
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /securities/IRO1SPY00001/orders/1?side=BUY
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "securityId": "IRO1SPY00001",
  "sequenceId": "1",
  "internalSubscriberReference": "****************",
  "type": "LIMIT",
  "price": "100.000",
  "quantity": 100,
  "initialQuantity": 100,
  "displayedQuantity": 100,
  "shareholderId": "**********",
  "brokerId": "110",
  "originId": "1",
  "technicalOrigin": "MANUAL",
  "priorityDateTime": "2021-02-21T15:46:20",
  "side": "BUY",
  "minimumQuantity": 0,
  "validityQualifierType": "GOOD_TILL_DATE",
  "validityDate": "2021-02-22",
  "clearingData": {
    "traderId": "********",
    "traderOrderNumber": "1",
    "brokerOrderEntryDateTime": "2020-06-17T15:38:23",
    "brokerBusinessIdentificationCode": {
      "bankCode": "110",
      "countryCode": "IR",
      "townCode": "98",
      "branchCode": "020"
    },
    "freeText": "random free text",
    "giveUpBrokerId": "110"
  }
}


########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
POST /securities/IRO1SPY00001/orders
Content-Type: application/json

{}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'brokerId' field is required"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'clearingData' must not be null"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'isPreOpeningOrder' must not be null"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'quantity' must not be null"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'shareholderId' field is required"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'side' must not be null"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'type' must not be null"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'validityQualifierType' must not be null"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
#
POST /securities/IRB1RADIN00-/orders

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Security' not resolvable by 'IRB1RADIN00-'"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
POST /securities/IRO1SPY00001/orders
Content-Type: application/json

{
  "price": "105.000",
  "quantity": 1400,
  "disclosedQuantity": 100,
  "minimumQuantity": 100,
  "side": "BUY",
  "shareholderId": "**************",
  "brokerId": "112",
  "validityQualifierType": "FILL_AND_KILL",
  "type": "LIMIT",
  "clearingData": {
    "traderId": "********",
    "traderOrderNumber": "TON2",
    "freeText": "some description",
    "brokerOrderEntryDateTime": "2020-06-17T15:38:23",
    "brokerBusinessIdentificationCode": {
      "bankCode": "124",
      "countryCode": "IR",
      "townCode": "98",
      "branchCode": "020"
    }
  },
  "isPreOpeningOrder": true
}

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "request": {
      "price": "105.000",
      "quantity": 1400,
      "disclosedQuantity": 100,
      "minimumQuantity": 100,
      "side": "BUY",
      "type": "LIMIT",
      "shareholderId": "**************",
      "brokerId": "112",
      "validityQualifierType": "FILL_AND_KILL",
      "clearingData": {
        "traderId": "********",
        "traderOrderNumber": "TON2",
        "brokerOrderEntryDateTime": "2020-06-17T15:38:23",
        "brokerBusinessIdentificationCode": {
          "bankCode": "124",
          "countryCode": "IR",
          "townCode": "98",
          "branchCode": "020"
        },
        "freeText": "some description"
      },
      "isPreOpeningOrder": true
    },
    "securityId": "IRO1SPY00001"
  },
  "status": "AWAITING_RESPONSE",
  "type": "ORDER_ADMINISTRATION",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /securities/IRO1SPY00001/orders/10?side=SELL

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1009",
      "title": "No such element",
      "detail": "'Order' not found"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
#
DELETE /securities/IRB1RADIN00-/orders/1

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Security' not resolvable by 'IRB1RADIN00-'"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /securities/IRO1SPY00001/orders/1?side=SELL

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securityId": "IRO1SPY00001",
    "originalSequenceId": 1,
    "side": "SELL"
  },
  "status": "AWAITING_RESPONSE",
  "type": "ORDER_ADMINISTRATION",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
PUT /securities/IRO1SPY00001/orders/1?side=SELL
Content-Type: application/json

{
  "price": "110.0",
  "quantity": 500
}

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "request": {
      "price": "110.0",
      "quantity": 500
    },
    "securityId": "IRO1SPY00001",
    "side": "SELL",
    "originalSequenceId": 1
  },
  "status": "AWAITING_RESPONSE",
  "type": "ORDER_ADMINISTRATION",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
PUT /securities/IRO1SPY00001/orders/1?side=SELL
Content-Type: application/json

{
  "price": "110.000",
  "quantity": 500,
  "validityQualifierType": "GOOD_TILL_DATE"
}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "`validityDate` must not be null."
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
PUT /securities/IRO1SPY00001/orders/1?side=SELL
Content-Type: application/json

{
  "price": "0",
  "quantity": 500
}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "Price should be more than zero."
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
PUT /securities/IRO1SPY00001/orders/1?side=SELL
Content-Type: application/json

{
  "price": "120.000",
  "quantity": 2000,
  "validityQualifierType": "GOOD_TILL_CANCEL"
}

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securityId": "IRO1SPY00001",
    "originalSequenceId": 1,
    "side": "SELL",
    "request": {
      "price": "120.000",
      "quantity": 2000,
      "validityQualifierType": "GOOD_TILL_CANCEL"
    }
  },
  "status": "AWAITING_RESPONSE",
  "type": "ORDER_ADMINISTRATION",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /orders

Nothing

###
403
Content-Type: application/json

{
  "errors": [
    {
      "type": "1030",
      "title": "All orders elimination is not permitted",
      "detail": "Such a filter leads to elimination of all orders already in the market."
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /orders?securities=SPY|NQZ&products=PRD0|PRD1|PRD2&brokers=BRK0|BRK1&traders=TRD0|TRD1&shareholders=SHD0|SHD1&investors=INV0|INV1&groups=GRP0|GRP1&sides=BUY|SELL

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securities": [
      "SPY",
      "NQZ"
    ],
    "products": [
      "PRD0",
      "PRD1",
      "PRD2"
    ],
    "brokers": [
      "BRK0",
      "BRK1"
    ],
    "traders": [
      "TRD0",
      "TRD1"
    ],
    "shareholders": [
      "SHD0",
      "SHD1"
    ],
    "investors": [
      "INV0",
      "INV1"
    ],
    "groups": [
      "GRP0",
      "GRP1"
    ],
    "sides": [
      "BUY",
      "SELL"
    ]
  },
  "status": "AWAITING_RESPONSE",
  "type": "PURGE_ORDERS",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /orders?securities=SPY&products=PRD&brokers=BRK&traders=TRD&shareholders=SHD&investors=INV&groups=GRP&sides=BUY

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securities": [
      "SPY"
    ],
    "products": [
      "PRD"
    ],
    "brokers": [
      "BRK"
    ],
    "traders": [
      "TRD"
    ],
    "shareholders": [
      "SHD"
    ],
    "investors": [
      "INV"
    ],
    "groups": [
      "GRP"
    ],
    "sides": [
      "BUY"
    ]
  },
  "status": "AWAITING_RESPONSE",
  "type": "PURGE_ORDERS",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /orders?securities=SPY

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securities": [
      "SPY"
    ],
    "products": [],
    "brokers": [],
    "traders": [],
    "shareholders": [],
    "investors": [],
    "groups": [],
    "sides": []
  },
  "status": "AWAITING_RESPONSE",
  "type": "PURGE_ORDERS",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /orders?products=PRD

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securities": [],
    "products": [
      "PRD"
    ],
    "brokers": [],
    "traders": [],
    "shareholders": [],
    "investors": [],
    "groups": [],
    "sides": []
  },
  "status": "AWAITING_RESPONSE",
  "type": "PURGE_ORDERS",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /orders?brokers=BRK

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securities": [],
    "products": [],
    "brokers": [
      "BRK"
    ],
    "traders": [],
    "shareholders": [],
    "investors": [],
    "groups": [],
    "sides": []
  },
  "status": "AWAITING_RESPONSE",
  "type": "PURGE_ORDERS",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /orders?traders=TRD

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securities": [],
    "products": [],
    "brokers": [],
    "traders": [
      "TRD"
    ],
    "shareholders": [],
    "investors": [],
    "groups": [],
    "sides": []
  },
  "status": "AWAITING_RESPONSE",
  "type": "PURGE_ORDERS",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /orders?shareholders=SHD

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securities": [],
    "products": [],
    "brokers": [],
    "traders": [],
    "shareholders": [
      "SHD"
    ],
    "investors": [],
    "groups": [],
    "sides": []
  },
  "status": "AWAITING_RESPONSE",
  "type": "PURGE_ORDERS",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /orders?investors=INV

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securities": [],
    "products": [],
    "brokers": [],
    "traders": [],
    "shareholders": [],
    "investors": [
      "INV"
    ],
    "groups": [],
    "sides": []
  },
  "status": "AWAITING_RESPONSE",
  "type": "PURGE_ORDERS",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /orders?groups=GRP

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securities": [],
    "products": [],
    "brokers": [],
    "traders": [],
    "shareholders": [],
    "investors": [],
    "groups": [
      "GRP"
    ],
    "sides": []
  },
  "status": "AWAITING_RESPONSE",
  "type": "PURGE_ORDERS",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /orders?sides=SELL

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securities": [],
    "products": [],
    "brokers": [],
    "traders": [],
    "shareholders": [],
    "investors": [],
    "groups": [],
    "sides": [
      "SELL"
    ]
  },
  "status": "AWAITING_RESPONSE",
  "type": "PURGE_ORDERS",
  "sender": "Test User"
}

########################################################################################################################
#
## Given: there are a lot of resources
#
GET /securities/IRO1SPY00001/orders/statistics

Nothing

###
200
Content-Type: application/json

{
  "buyQueue": {
    "bestPrice": "110.000",
    "worstPrice": "100.000"
  },
  "sellQueue": {
    "bestPrice": "120.000",
    "worstPrice": "130.000"
  }
}

########################################################################################################################
#
## Given: there are a lot of resources
#
GET /securities/IRO1SPY00001/best-limits

Nothing

###
200
Content-Type: application/json

{
  "securityId": "IRO1SPY00001",
  "buyQueue": [
    {
      "price": "110.000",
      "quantity": 100,
      "orderCount": 1
    },
    {
      "price": "100.000",
      "quantity": 100,
      "orderCount": 1
    }
  ],
  "sellQueue": [
    {
      "price": "120.000",
      "quantity": 100,
      "orderCount": 1
    },
    {
      "price": "130.000",
      "quantity": 100,
      "orderCount": 1
    }
  ]
}

########################################################################################################################
#
## Given: there are a lot of resources
#
GET /securities/IRO1SPY00002/best-limits

Nothing

###
200
Content-Type: application/json

{
  "securityId": "IRO1SPY00002",
  "buyQueue": [],
  "sellQueue": []
}
