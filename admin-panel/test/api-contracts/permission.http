#
## Given: no resources exist
#
GET /permissions
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 0
  },
  "element": {
    "count": 0,
    "offset": 0,
    "total": 0
  }
}

########################################################################################################################

#
## Given: there is a single resource
#
GET /permissions
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "uri": "/securities/**",
      "method": "GET",
      "name": "security-read",
      "description": "permission to read security entity"
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 1,
    "offset": 0,
    "total": 1
  }
}
########################################################################################################################

#
## Given: there are a lot of resources
#
GET /permissions
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "uri": "/securities/**",
      "method": "GET",
      "name": "securities-GET",
      "description": "securities-GET-description"
    },
    {
      "uri": "/securities/**",
      "method": "POST",
      "name": "securities-POST",
      "description": "securities-POST-description"
    },
    {
      "uri": "/securities/**",
      "method": "DELETE",
      "name": "securities-DELETE",
      "description": "securities-DELETE-description"
    },
    {
      "uri": "/securities/**",
      "method": "PUT",
      "name": "securities-PUT",
      "description": "securities-PUT-description"
    },
    {
      "uri": "/groups/**",
      "method": "GET",
      "name": "groups-GET",
      "description": "groups-GET-description"
    },
    {
      "uri": "/groups/**",
      "method": "POST",
      "name": "groups-POST",
      "description": "groups-POST-description"
    },
    {
      "uri": "/groups/**",
      "method": "DELETE",
      "name": "groups-DELETE",
      "description": "groups-DELETE-description"
    },
    {
      "uri": "/groups/**",
      "method": "PUT",
      "name": "groups-PUT",
      "description": "groups-PUT-description"
    },
    {
      "uri": "/**",
      "method": "GET",
      "name": "no-auth-user-get",
      "description": "No-Auth permission GET"
    },
    {
      "uri": "/**",
      "method": "HEAD",
      "name": "no-auth-user-head",
      "description": "No-Auth permission HEAD"
    },
    {
      "uri": "/**",
      "method": "POST",
      "name": "no-auth-user-post",
      "description": "No-Auth permission POST"
    },
    {
      "uri": "/**",
      "method": "PUT",
      "name": "no-auth-user-put",
      "description": "No-Auth permission PUT"
    },
    {
      "uri": "/**",
      "method": "PATCH",
      "name": "no-auth-user-patch",
      "description": "No-Auth permission PATCH"
    },
    {
      "uri": "/**",
      "method": "DELETE",
      "name": "no-auth-user-delete",
      "description": "No-Auth permission DELETE"
    },
    {
      "uri": "/**",
      "method": "OPTIONS",
      "name": "no-auth-user-options",
      "description": "No-Auth permission OPTIONS"
    },
    {
      "uri": "/**",
      "method": "TRACE",
      "name": "no-auth-user-trace",
      "description": "No-Auth permission TRACE"
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 16,
    "offset": 0,
    "total": 16
  }
}
########################################################################################################################

#
## Given: there are a lot of resources
#
GET /permissions?page=0&size=3
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "uri": "/securities/**",
      "method": "GET",
      "name": "securities-GET",
      "description": "securities-GET-description"
    },
    {
      "uri": "/securities/**",
      "method": "POST",
      "name": "securities-POST",
      "description": "securities-POST-description"
    },
    {
      "uri": "/securities/**",
      "method": "DELETE",
      "name": "securities-DELETE",
      "description": "securities-DELETE-description"
    }
  ],
  "page": {
    "size": 3,
    "number": 0,
    "total": 6
  },
  "element": {
    "count": 3,
    "offset": 0,
    "total": 16
  }
}
########################################################################################################################

#
## Given: there are a lot of resources
#
GET /permissions?page=1&size=3
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "uri": "/securities/**",
      "method": "PUT",
      "name": "securities-PUT",
      "description": "securities-PUT-description"
    },
    {
      "uri": "/groups/**",
      "method": "GET",
      "name": "groups-GET",
      "description": "groups-GET-description"
    },
    {
      "uri": "/groups/**",
      "method": "POST",
      "name": "groups-POST",
      "description": "groups-POST-description"
    }
  ],
  "page": {
    "size": 3,
    "number": 1,
    "total": 6
  },
  "element": {
    "count": 3,
    "offset": 3,
    "total": 16
  }
}
########################################################################################################################

#
## Given: there are a lot of resources
#
GET /permissions?page=2&size=3
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "uri": "/groups/**",
      "method": "DELETE",
      "name": "groups-DELETE",
      "description": "groups-DELETE-description"
    },
    {
      "uri": "/groups/**",
      "method": "PUT",
      "name": "groups-PUT",
      "description": "groups-PUT-description"
    },
    {
      "uri": "/**",
      "method": "GET",
      "name": "no-auth-user-get",
      "description": "No-Auth permission GET"
    }
  ],
  "page": {
    "size": 3,
    "number": 2,
    "total": 6
  },
  "element": {
    "count": 3,
    "offset": 6,
    "total": 16
  }
}
########################################################################################################################

#
## Given: no resources exist
#
POST /permissions
Content-Type: application/json

{
  "name": "security-read",
  "description": "security read permission",
  "uri": "/securities/**",
  "method": "GET"
}
###
200

Nothing
########################################################################################################################

#
## Given: there is a single resource
#
POST /permissions
Content-Type: application/json

{
  "name": "security-read-1",
  "description": "security read permission another",
  "uri": "/someuri/**",
  "method": "POST"
}
###
200

Nothing
########################################################################################################################

#
## Given: there are a lot of resources
#
POST /permissions
Content-Type: application/json

{
  "name": "security-read",
  "description": "security read permission",
  "uri": "/securities/**",
  "method": "GET"
}
###
200

Nothing
########################################################################################################################

#
## Given: no resources exist
#
PUT /permissions/non-existed-permission
Content-Type: application/json

{
  "name": "security-read",
  "description": "security read permission",
  "uri": "/securities/**",
  "method": "GET"
}

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Permission' not resolvable by 'non-existed-permission'"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
#
PUT /permissions/security-read
Content-Type: application/json

{
  "name": "security-write",
  "description": "security read permission",
  "uri": "/securities/**",
  "method": "POST"
}
###
200

Nothing
########################################################################################################################

#
## Given: there are a lot of resources
#
PUT /permissions/securities-GET
Content-Type: application/json

{
  "name": "security-write",
  "description": "security read permission",
  "uri": "/somerandomuri/**",
  "method": "POST"
}
###
200

Nothing
########################################################################################################################

#
## Given: there is a single resource
#
DELETE /permissions/non-existed-permission
Accept: application/json

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Permission' not resolvable by 'non-existed-permission'"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
#
DELETE /permissions/security-read
Accept: application/json

Nothing
###
200

Nothing
########################################################################################################################

#
## Given: there are a lot of resources
#
DELETE /permissions/securities-GET
Accept: application/json

Nothing
###
200

Nothing
########################################################################################################################

#
## Given: there are a lot of resources
#
GET /permissions/securities-GET
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "uri": "/securities/**",
  "method": "GET",
  "name": "securities-GET",
  "description": "securities-GET-description"
}
########################################################################################################################

#
## Given: there is a single resource
#
GET /permissions/users/mohammad
Accept: application/json

Nothing

###
200
Content-Type: application/json

[
  {
    "uri": "/securities/**",
    "method": "GET",
    "name": "security-read",
    "description": "permission to read security entity"
  }
]

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /permissions/user
Accept: application/json

Nothing

###
200
Content-Type: application/json

[
  {
    "uri": "/**",
    "method": "DELETE",
    "name": "no-auth-user-delete",
    "description": "No-Auth permission DELETE"
  },
  {
    "uri": "/**",
    "method": "GET",
    "name": "no-auth-user-get",
    "description": "No-Auth permission GET"
  },
  {
    "uri": "/**",
    "method": "HEAD",
    "name": "no-auth-user-head",
    "description": "No-Auth permission HEAD"
  },
  {
    "uri": "/**",
    "method": "OPTIONS",
    "name": "no-auth-user-options",
    "description": "No-Auth permission OPTIONS"
  },
  {
    "uri": "/**",
    "method": "PATCH",
    "name": "no-auth-user-patch",
    "description": "No-Auth permission PATCH"
  },
  {
    "uri": "/**",
    "method": "POST",
    "name": "no-auth-user-post",
    "description": "No-Auth permission POST"
  },
  {
    "uri": "/**",
    "method": "PUT",
    "name": "no-auth-user-put",
    "description": "No-Auth permission PUT"
  },
  {
    "uri": "/**",
    "method": "TRACE",
    "name": "no-auth-user-trace",
    "description": "No-Auth permission TRACE"
  }
]

########################################################################################################################
