#
## Given: no resources exist
#
GET /groups
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 0
  },
  "element": {
    "count": 0,
    "offset": 0,
    "total": 0
  }
}

########################################################################################################################

#
## Given: there is a single resource
#
GET /groups
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "name": {
        "en": "Group 0",
        "fa": "گروه 0"
      },
      "code": "G0",
      "market": "NO",
      "description": "Group Description",
      "state": "FORBIDDEN",
      "priceBandPercentage": {
        "lowerBoundPercentage": "0.2",
        "upperBoundPercentage": "1.0"
      },
      "creditCheckingStatus": true,
      "updatingStaticPriceBandAfterOpening":false
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 1,
    "offset": 0,
    "total": 1
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /groups
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "name": {
        "en": "Group 0",
        "fa": "گروه 0"
      },
      "code": "G0",
      "market": "NO",
      "description": "Group Description",
      "state": "FORBIDDEN",
      "priceBandPercentage": {
        "lowerBoundPercentage": "0.2",
        "upperBoundPercentage": "1.0"
      },
      "creditCheckingStatus": true,
      "updatingStaticPriceBandAfterOpening":false
    },
    {
      "name": {
        "en": "Group 1",
        "fa": "گروه 1"
      },
      "code": "G1",
      "market": "OL",
      "description": "Group Description",
      "state": "FORBIDDEN",
      "priceBandPercentage": {
        "lowerBoundPercentage": "0.2",
        "upperBoundPercentage": "1.0"
      },
      "creditCheckingStatus": true,
      "updatingStaticPriceBandAfterOpening":false
    },
    {
      "name": {
        "en": "Group 2",
        "fa": "گروه 2"
      },
      "code": "G2",
      "market": "BY",
      "description": "Group Description",
      "state": "FORBIDDEN",
      "priceBandPercentage": {
        "lowerBoundPercentage": "0.2",
        "upperBoundPercentage": "1.0"
      },
      "creditCheckingStatus": true,
      "updatingStaticPriceBandAfterOpening":false
    },
    {
      "name": {
        "en": "Group 3",
        "fa": "گروه 3"
      },
      "code": "G3",
      "market": "BK",
      "description": "Group Description",
      "state": "FORBIDDEN",
      "priceBandPercentage": {
        "lowerBoundPercentage": "0.2",
        "upperBoundPercentage": "1.0"
      },
      "creditCheckingStatus": true,
      "updatingStaticPriceBandAfterOpening":false
    },
    {
      "name": {
        "en": "Group 4",
        "fa": "گروه 4"
      },
      "code": "G4",
      "market": "NO",
      "description": "Group Description",
      "state": "FORBIDDEN",
      "priceBandPercentage": {
        "lowerBoundPercentage": "0.2",
        "upperBoundPercentage": "1.0"
      },
      "creditCheckingStatus": true,
      "updatingStaticPriceBandAfterOpening":false
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 5,
    "offset": 0,
    "total": 5
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /groups?page=0&size=3
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "name": {
        "en": "Group 0",
        "fa": "گروه 0"
      },
      "code": "G0",
      "market": "NO",
      "description": "Group Description",
      "state": "FORBIDDEN",
      "priceBandPercentage": {
        "lowerBoundPercentage": "0.2",
        "upperBoundPercentage": "1.0"
      },
      "creditCheckingStatus": true,
      "updatingStaticPriceBandAfterOpening":false
    },
    {
      "name": {
        "en": "Group 1",
        "fa": "گروه 1"
      },
      "code": "G1",
      "market": "OL",
      "description": "Group Description",
      "state": "FORBIDDEN",
      "priceBandPercentage": {
        "lowerBoundPercentage": "0.2",
        "upperBoundPercentage": "1.0"
      },
      "creditCheckingStatus": true,
      "updatingStaticPriceBandAfterOpening":false
    },
    {
      "name": {
        "en": "Group 2",
        "fa": "گروه 2"
      },
      "code": "G2",
      "market": "BY",
      "description": "Group Description",
      "state": "FORBIDDEN",
      "priceBandPercentage": {
        "lowerBoundPercentage": "0.2",
        "upperBoundPercentage": "1.0"
      },
      "creditCheckingStatus": true,
      "updatingStaticPriceBandAfterOpening":false
    }
  ],
  "page": {
    "size": 3,
    "number": 0,
    "total": 2
  },
  "element": {
    "count": 3,
    "offset": 0,
    "total": 5
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /groups?page=1&size=3
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "name": {
        "en": "Group 3",
        "fa": "گروه 3"
      },
      "code": "G3",
      "market": "BK",
      "description": "Group Description",
      "state": "FORBIDDEN",
      "priceBandPercentage": {
        "lowerBoundPercentage": "0.2",
        "upperBoundPercentage": "1.0"
      },
      "creditCheckingStatus": true,
      "updatingStaticPriceBandAfterOpening":false
    },
    {
      "name": {
        "en": "Group 4",
        "fa": "گروه 4"
      },
      "code": "G4",
      "market": "NO",
      "description": "Group Description",
      "state": "FORBIDDEN",
      "priceBandPercentage": {
        "lowerBoundPercentage": "0.2",
        "upperBoundPercentage": "1.0"
      },
      "creditCheckingStatus": true,
      "updatingStaticPriceBandAfterOpening":false
    }
  ],
  "page": {
    "size": 3,
    "number": 1,
    "total": 2
  },
  "element": {
    "count": 2,
    "offset": 3,
    "total": 5
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /groups?page=2&size=3
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [],
  "page": {
    "size": 3,
    "number": 2,
    "total": 2
  },
  "element": {
    "count": 0,
    "offset": 5,
    "total": 5
  }
}

########################################################################################################################

#
## Given: no resources exist
#
GET /groups/N0
Accept: application/json

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Group' not resolvable by 'N0'"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
#
GET /groups/G0
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "name": {
    "en": "Group 0",
    "fa": "گروه 0"
  },
  "code": "G0",
  "market": "NO",
  "description": "Group Description",
  "state": "FORBIDDEN",
  "includedSecurities": [
    {
      "securityId": "IRO1SPY00001",
      "mnemonic": {
        "en": "SPY01",
        "fa": "اس پی وای01"
      }
    }
  ],
  "priceBandPercentage": {
    "lowerBoundPercentage": "0.2",
    "upperBoundPercentage": "1.0"
  },
  "creditCheckingStatus": true,
  "updatingStaticPriceBandAfterOpening":false
}

########################################################################################################################

#
## Given: no resources exist
#
POST /groups/N0/state
Accept: application/json

{"targetState": "CONTINUOUS_TRADING"}

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Group' not resolvable by 'N0'"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
POST /groups/G0/state
Accept: application/json

{"targetState": "CONTINUOUS_TRADING", "shouldOpen": true}

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "groupCode": "G0",
    "targetState": "CONTINUOUS_TRADING",
    "shouldOpen": true
  },
  "status": "AWAITING_RESPONSE",
  "type": "CHANGE_GROUP_STATE",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
POST /groups/G0/state
Accept: application/json

{"targetState": "GROUP_AUCTION", "shouldOpen": false}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1008",
      "title": "Validation error",
      "detail": "Can not change group state to auction."
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
POST /groups
Content-Type: application/json

{
  "code": "N1",
  "name": {
    "en": "N1",
    "fa": "ن۱"
  },
  "market": "NO",
  "priceBandPercentage": {
    "lowerBoundPercentage": "5.0",
    "upperBoundPercentage": "5.0"
  },
  "description": "Group Description"
}

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "name": {
      "en": "N1",
      "fa": "ن۱"
    },
    "code": "N1",
    "market": "NO",
    "description": "Group Description",
    "priceBandPercentage": {
      "lowerBoundPercentage": "5.0",
      "upperBoundPercentage": "5.0"
    }
  },
  "status": "AWAITING_RESPONSE",
  "type": "CREATE_GROUP",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
POST /groups
Content-Type: application/json

{
  "code": "G0",
  "name": {
    "en": "G0",
    "fa": "گ۱"
  },
  "market": "NO",
  "priceBandPercentage": {
    "lowerBoundPercentage": "2.0",
    "upperBoundPercentage": "2.0"
  },
  "description": "This group already exists"
}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1008",
      "title": "Validation error",
      "detail": "Group already exists!"
    }
  ]
}

########################################################################################################################
#
## Given: there are a lot of resources
#
GET /groups/G0/queued-securities
Accept: application/json

Nothing

###
200

["IRO1SPY00004","IRO1SPY00031"]

########################################################################################################################
#
## Given: there are a lot of resources
#
GET /groups/G0/static-threshold/following-securities
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "securityId": "IRO1SPY00001",
      "ghostStatus": "PERSISTED",
      "mnemonic": {
        "en": "SPY01",
        "fa": "اس پی وای01"
      },
      "product": {
        "productId": "IRO1SPY00006",
        "ghostStatus": "PERSISTED",
        "company": {
          "code": "SPY0",
          "shortName": {
            "en": "SPDR S&P 500 Trust",
            "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
          },
          "fullName": {
            "en": "SPDR S&P 500 Trust",
            "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
          },
          "sectorCode": "S0",
          "subSectorCode": "SS0",
          "creationDate": "2000-01-01",
          "updateDate": "2000-01-01"
        },
        "boardCode": "0",
        "productTypeCode": "O",
        "productSubTypeCode": "110",
        "marketFlowCode": "00",
        "referencePrice": "invalid",
        "totalShares": 900000000000,
        "maxOwnershipByShareholder": "0.2",
        "adjustedTotalShares": *********,
        "creationDate": "2000-01-01",
        "updateDate": "2000-01-01",
        "name": {
          "en": "SPDR S&P 500 Trust",
          "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
        },
        "mnemonic": {
          "en": "SPY0",
          "fa": "اس پی وای0"
        },
        "productCode": "SPY000",
        "tradingStartDate": "2000-01-01",
        "tradingEndDate": "2000-01-01",
        "maturityDate": "2000-01-01",
        "availableForSell": true,
        "isPricePercentage": false
      },
      "yesterdayClosingPrice": "108.250",
      "lotSize": 1,
      "priceTick": "10.000",
      "baseVolume": 1000000,
      "staticPriceBandLowerBound": "100.210",
      "staticPriceBandUpperBound": "152.120",
      "state": "AUTHORIZED",
      "isShortSellAllowed": false,
      "group": {
        "name": {
          "en": "Group 0",
          "fa": "گروه 0"
        },
        "code": "G0",
        "market": "NO",
        "description": "Group Description",
        "state": "FORBIDDEN",
        "priceBandPercentage": {
          "lowerBoundPercentage": "0.2",
          "upperBoundPercentage": "1.0"
        },
        "creditCheckingStatus": true,
        "updatingStaticPriceBandAfterOpening":false
      },
      "market": "NO",
      "creationDate": "2000-01-01",
      "updateDate": "2000-01-01",
      "settlementDelay": 3,
      "minOrderQuantity": 100,
      "maxBuyOrderQuantity": 100000,
      "maxSellOrderQuantity": 4000
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 1,
    "offset": 0,
    "total": 1
  }
}

########################################################################################################################
#
## Given: there are a lot of resources
#
GET /groups/G0/static-threshold/not-following-securities
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "securityId": "IRO1SPY00011",
      "ghostStatus": "DELETED",
      "mnemonic": {
        "en": "SPY11",
        "fa": "اس پی وای11"
      },
      "product": {
        "productId": "IRO1SPY00014",
        "ghostStatus": "DELETED",
        "company": {
          "code": "SPY0",
          "shortName": {
            "en": "SPDR S&P 500 Trust",
            "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
          },
          "fullName": {
            "en": "SPDR S&P 500 Trust",
            "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
          },
          "sectorCode": "S0",
          "subSectorCode": "SS0",
          "creationDate": "2000-01-01",
          "updateDate": "2000-01-01"
        },
        "boardCode": "0",
        "productTypeCode": "O",
        "productSubTypeCode": "110",
        "marketFlowCode": "00",
        "referencePrice": "invalid",
        "totalShares": 900000000000,
        "maxOwnershipByShareholder": "0.2",
        "adjustedTotalShares": *********,
        "creationDate": "2000-01-01",
        "updateDate": "2000-01-01",
        "name": {
          "en": "SPDR S&P 500 Trust",
          "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
        },
        "mnemonic": {
          "en": "SPY1",
          "fa": "اس پی وای1"
        },
        "productCode": "SPY001",
        "tradingStartDate": "2000-01-01",
        "tradingEndDate": "2000-01-01",
        "maturityDate": "2000-01-01",
        "availableForSell": true,
        "isPricePercentage": false
      },
      "priceBandPercentage": {
        "lowerBoundPercentage": "89.5",
        "upperBoundPercentage": "100.0"
      },
      "yesterdayClosingPrice": "108.250",
      "lotSize": 1,
      "priceTick": "10.000",
      "baseVolume": 1000000,
      "staticPriceBandLowerBound": "100.210",
      "staticPriceBandUpperBound": "152.120",
      "state": "AUTHORIZED",
      "isShortSellAllowed": false,
      "group": {
        "name": {
          "en": "Group 0",
          "fa": "گروه 0"
        },
        "code": "G0",
        "market": "NO",
        "description": "Group Description",
        "state": "FORBIDDEN",
        "priceBandPercentage": {
          "lowerBoundPercentage": "0.2",
          "upperBoundPercentage": "1.0"
        },
        "creditCheckingStatus": true,
        "updatingStaticPriceBandAfterOpening":false
      },
      "market": "NO",
      "creationDate": "2000-01-01",
      "updateDate": "2000-01-01",
      "settlementDelay": 3,
      "minOrderQuantity": 100,
      "maxBuyOrderQuantity": 100000,
      "maxSellOrderQuantity": 4000
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 1,
    "offset": 0,
    "total": 1
  }
}

########################################################################################################################
#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /groups/G0
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "code": "G0"
  },
  "status": "AWAITING_RESPONSE",
  "type": "DELETE_GROUP",
  "sender": "Test User"
}

########################################################################################################################
#
## Given: there is a single resource
#
DELETE /groups/N1
Accept: application/json

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Group' not resolvable by 'N1'"
    }
  ]
}

#######################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
PUT /groups/G0
Content-Type: application/json

{
  "name": {
    "en": "N1",
    "fa": "ن۱"
  },
  "description": "Group Description",
  "priceBandPercentage": {
    "lowerBoundPercentage": "0.1",
    "upperBoundPercentage": "0.05"
  }
}

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "code": "G0",
    "name": {
      "en": "N1",
      "fa": "ن۱"
    },
    "description": "Group Description"
  },
  "status": "AWAITING_RESPONSE",
  "type": "UPDATE_GROUP",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
#
PUT /groups/G1
Content-Type: application/json

{
  "name": {
    "en": "N1",
    "fa": "ن۱"
  },
  "description": "Group Description"
}

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Group' not resolvable by 'G1'"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
DELETE /groups/G1

Nothing

###
200
Content-Type: application/json

{
  "commandId": 8,
  "request": {
    "code": "G1"
  },
  "status": "AWAITING_RESPONSE",
  "type": "DELETE_GROUP",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
PUT /groups/G0
Content-Type: application/json

{
  "name": {
    "en": "Group",
    "fa": "گروه"
  },
  "market": "NO",
  "code": "G0",
  "description": "Group Description"
}

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "code": "G0",
    "name": {
      "en": "Group",
      "fa": "گروه"
    },
    "description": "Group Description"
  },
  "status": "AWAITING_RESPONSE",
  "type": "UPDATE_GROUP",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
PUT /groups/G0
Content-Type: application/json

{
  "name": {
    "en": "Group",
    "fa": "گروه"
  },
  "code": "G0",
  "market": "NO",
  "description": "Group Description",
  "priceBandPercentage": {
    "lowerBoundPercentage": "0.1",
    "upperBoundPercentage": "120"
  }
}

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "code": "G0",
    "name": {
      "en": "Group",
      "fa": "گروه"
    },
    "description": "Group Description"
  },
  "status": "AWAITING_RESPONSE",
  "type": "UPDATE_GROUP",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: no resources exist
#
PUT /groups/G0
Accept: application/json

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Group' not resolvable by 'G0'"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
PUT /groups/G0
Content-Type: application/json


{
  "name": {
    "en": "Group",
    "fa": "گروه"
  },
  "code": "G0",
  "market": "NO",
  "description": "Group Description",
  "priceBandPercentage": {
    "lowerBoundPercentage": "0.1",
    "upperBoundPercentage": "0.05"
  }
}

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "code": "G0",
    "name": {
      "en": "Group",
      "fa": "گروه"
    },
    "description": "Group Description"
  },
  "status": "AWAITING_RESPONSE",
  "type": "UPDATE_GROUP",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /groups/G0/orders

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securities": [],
    "products": [],
    "brokers": [],
    "traders": [],
    "shareholders": [],
    "investors": [],
    "groups": [
      "G0"
    ],
    "sides": []
  },
  "status": "AWAITING_RESPONSE",
  "type": "PURGE_ORDERS",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
#
DELETE /groups/G0/orders?brokers=BRK0|BRK1&traders=TRD0|TRD1&shareholders=SHD0|SHD1&investors=INV0|INV1&
    securities=SPY|NGZ&products=GRP0|GRP1&sides=BUY|SELL

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securities": [
      "SPY",
      "NGZ"
    ],
    "products": [
      "GRP0",
      "GRP1"
    ],
    "brokers": [
      "BRK0",
      "BRK1"
    ],
    "traders": [
      "TRD0",
      "TRD1"
    ],
    "shareholders": [
      "SHD0",
      "SHD1"
    ],
    "investors": [
      "INV0",
      "INV1"
    ],
    "groups": [
      "G0"
    ],
    "sides": [
      "BUY",
      "SELL"
    ]
  },
  "status": "AWAITING_RESPONSE",
  "type": "PURGE_ORDERS",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there are a lot of resources
#
POST /groups/G1/credit-checking
Content-Type: application/json

{
  "creditCheckingStatus": false
}

###
200
Content-Type: application/json

{
  "commandId": 8,
  "request": {
    "groupCode": "G1",
    "creditCheckingStatus": false
  },
  "status": "AWAITING_RESPONSE",
  "type": "CHANGE_GROUP_CREDIT_CHECKING_STATUS",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there are a lot of resources
#
POST /groups/G1/credit-checking
Content-Type: application/json

{}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'creditCheckingStatus' must not be null"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
PUT /groups/G0/queued-commands/GROUP_PRICE_BAND_UPDATE/0
Content-Type: application/json

{
  "lowerBoundPercentage": "0.2",
  "upperBoundPercentage": "0.15"
}

###
200
Content-Type: application/json

{
  "status":"IN_QUEUE",
  "adminCommandType":"CHANGE_GROUP_STATIC_PRICE_BAND",
  "minorPriority":0,
  "majorPriority":"GROUP_PRICE_BAND_UPDATE",
  "request":{
    "groupCode":"G0",
    "priceBandPercentage":{
      "lowerBoundPercentage":"0.2",
      "upperBoundPercentage":"0.15"
    }
  },
  "groupCode":"G0"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
POST /groups/G0/opening-update-price-band
Content-Type: application/json

{
  "isUpdatingAfterOpening": true
}

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "groupCode": "G0",
    "isUpdatingAfterOpening": true
  },
  "status": "AWAITING_RESPONSE",
  "type": "CHANGE_OPENING_UPDATE_PRICE_BAND_STATUS",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
PUT /groups/G0/queued-commands/GROUP_PRICE_BAND_UPDATE/7
Content-Type: application/json

{
  "lowerBoundPercentage": "0.2",
  "upperBoundPercentage": "0.15"
}

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1009",
      "title": "No such element",
      "detail": "'CommandQueueItem' not found"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
POST /groups/G0/opening-update-price-band
Content-Type: application/json

{}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'isUpdatingAfterOpening' must not be null"
    }
  ]
}

########################################################################################################################
