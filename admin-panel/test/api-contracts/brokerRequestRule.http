#
## Given: no resources exist
#
GET /broker-requests/rules
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 0
  },
  "element": {
    "count": 0,
    "offset": 0,
    "total": 0
  }
}

########################################################################################################################

#
## Given: there is a single resource
#
GET /broker-requests/rules
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "ruleId": 1,
      "securityIds": {
        "items": [
          "SPY"
        ],
        "isNegated": true
      },
      "accountIds": {
        "items": [
          "TADBIRCAP00"
        ],
        "isNegated": false
      },
      "actionType": "REJECT_REQUEST"
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 1,
    "offset": 0,
    "total": 1
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET  /broker-requests/rules

###
200
Content-Type: application/json

{}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /broker-requests/rules?page=0&size=3
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "ruleId": 1,
      "securityIds": {
        "items": [
          "SPY"
        ],
        "isNegated": true
      },
      "accountIds": {
        "items": [
          "TADBIRCAP00"
        ],
        "isNegated": false
      },
      "actionType": "REJECT_REQUEST"
    },
    {
      "ruleId": 2,
      "securityIds": {
        "items": [
          "SPY"
        ],
        "isNegated": true
      },
      "accountIds": {
        "items": [
          "TADBIRCAP01"
        ],
        "isNegated": false
      },
      "actionType": "REJECT_REQUEST"
    },
    {
      "ruleId": 3,
      "securityIds": {
        "items": [
          "SPY"
        ],
        "isNegated": true
      },
      "accountIds": {
        "items": [
          "TADBIRCAP02"
        ],
        "isNegated": false
      },
      "actionType": "REJECT_REQUEST"
    }
  ],
  "page": {
    "size": 3,
    "number": 0,
    "total": 2
  },
  "element": {
    "count": 3,
    "offset": 0,
    "total": 4
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /broker-requests/rules?actionType=DROP_REQUEST
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 0
  },
  "element": {
    "count": 0,
    "offset": 0,
    "total": 0
  }
}

########################################################################################################################

#
## Given: no resources exist
#

GET /broker-requests/rules/0
Accept: application/json

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'BrokerRequestRule' not resolvable by '0'"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
#
GET /broker-requests/rules/1
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "ruleId": 1,
  "securityIds": {
    "items": [
      "SPY"
    ],
    "isNegated": true
  },
  "accountIds": {
    "items": [
      "TADBIRCAP00"
    ],
    "isNegated": false
  },
  "actionType": "REJECT_REQUEST"
}

########################################################################################################################

#
## Given: no resources exist
#
POST /broker-requests/rules
Content-Type: application/json

{
  "description": "TADBIR GAP RULE",
  "accountIds": {
    "isNegated": true,
    "items": [
      "123456"
    ]
  },
  "brokerIds": {
    "isNegated": true,
    "items": [
      "MOFID",
      "MELLI"
    ]
  },
  "actionType": "REJECT_REQUEST"
}

###
200
Content-Type: application/json

{
  "commandId": 1,
  "request": {
    "id": 1,
    "ruleId": 1,
    "description": "TADBIR GAP RULE",
    "actionType": "REJECT_REQUEST",
    "brokerIds": {
      "items": [
        "MELLI",
        "MOFID"
      ],
      "isNegated": true
    },
    "accountIds": {
      "items": [
        "123456"
      ],
      "isNegated": true
    }
  },
  "status": "AWAITING_RESPONSE",
  "type": "ADD_OR_UPDATE_RULE",
  "sender": "Test User"
}


########################################################################################################################

#
## Given: no resources exist
#
POST /broker-requests/rules
Content-Type: application/json

{
  "description": "TADBIR GAP RULE",
  "accountIds": {
    "items": [
      "123456"
    ]
  },
  "brokerIds": {
    "isNegated": true,
    "items": [
      "MOFID",
      "MELLI"
    ]
  },
  "actionType": "SOMETHING_ELSE"
}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1005",
      "title": "Unable to assign parameter value",
      "detail": "'actionType' not assignable by 'SOMETHING_ELSE'"
    }
  ]
}

########################################################################################################################

#
## Given: no resources exist
#
POST /broker-requests/rules
Content-Type: application/json

{
  "description": "TADBIR GAP RULE",
  "accountIds": {
    "items": [
      "123456"
    ]
  },
  "brokerIds": {
    "isNegated": true,
    "items": []
  },
  "securityIds": {
    "isNegated": true
  },
  "actionType": "REJECT_REQUEST"
}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'accountIds.isNegated' must not be null"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'brokerIds.items' must not be empty"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'securityIds.items' must not be empty"
    }
  ]
}

########################################################################################################################

#
## Given: no resources exist
#
POST /broker-requests/rules
Content-Type: application/json

{
  "description": "TADBIR GAP RULE",
  "actionType": "REJECT_REQUEST"
}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "At least one of the filters should be provided."
    }
  ]
}

########################################################################################################################

#
## Given: no resources exist
#
DELETE /broker-requests/rules/0
Accept: application/json

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'BrokerRequestRule' not resolvable by '0'"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
#
DELETE /broker-requests/rules/1
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "id": 1,
    "ruleId": 1,
    "actionType": "REJECT_REQUEST",
    "securityIds": {
      "items": [
        "SPY"
      ],
      "isNegated": true
    },
    "accountIds": {
      "items": [
        "TADBIRCAP00"
      ],
      "isNegated": false
    }
  },
  "status": "AWAITING_RESPONSE",
  "type": "CLEAR_RULE",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there are a lot of resources
#
DELETE /broker-requests/rules
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "commandId": 8,
  "status": "AWAITING_RESPONSE",
  "type": "CLEAR_ALL_RULES",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there are a lot of resources
#
PUT /broker-requests/rules/resend
Accept: application/json

Nothing

###
200

Nothing

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /broker-requests/rules/actions
Accept: application/json

Nothing

###
200
Content-Type: application/json

[
  "DROP_REQUEST",
  "REJECT_REQUEST"
]

########################################################################################################################

#
## Given: there are a lot of resources
#

PUT /broker-requests/rules/1
Content-Type: application/json

{
  "brokerIds": {
    "isNegated": true,
    "items": [
      "MOFID",
      "MELLI"
    ]
  },
  "actionType": "DROP_REQUEST"
}

###
200
Content-Type: application/json

{
  "commandId": 8,
  "request": {
    "id": 1,
    "ruleId": 1,
    "actionType": "DROP_REQUEST",
    "brokerIds": {
      "items": [
        "MELLI",
        "MOFID"
      ],
      "isNegated": true
    }
  },
  "status": "AWAITING_RESPONSE",
  "type": "ADD_OR_UPDATE_RULE",
  "sender": "Test User"
}

########################################################################################################################