#
## Given: no resources exist
## With System State: POST_SESSION
#
GET /aramis/ARAFICO01
Accept: */*

Nothing

###
200
Content-Type: text/plain

########################################################################################################################

#
## Given: no resources exist
## With System State: POST_SESSION
#
GET /aramis/ARAFICO02
Accept: */*

Nothing

###
200
Content-Type: text/plain

########################################################################################################################

#
## Given: no resources exist
## With System State: POST_SESSION
#
GET /aramis/ARAFICO03
Accept: */*

Nothing

###
200
Content-Type: text/plain

########################################################################################################################

#
## Given: no resources exist
## With System State: POST_SESSION
#
GET /aramis/ARAFICO05
Accept: */*

Nothing

###
200
Content-Type: text/plain

########################################################################################################################

#
## Given: no resources exist
## With System State: POST_SESSION
#
GET /aramis/closing_price
Accept: */*

Nothing

###
200
Content-Type: text/plain

########################################################################################################################

#
## Given: no resources exist
## With System State: POST_SESSION
#
GET /aramis/FORDSUP
Accept: */*

Nothing

###
200
Content-Type: text/plain

01Ord withdrawn during PS 21/02/2021154620
03Ord withdrawn during PS 21/02/2021154620000000002

########################################################################################################################

#
## Given: no resources exist
## With System State: POST_SESSION
#
GET /aramis/OPEN_ORDERS
Accept: */*

Nothing

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1008",
      "title": "Validation error",
      "detail": "DeclaredOrdersCount not found"
    }
  ]
}

########################################################################################################################

#
## Given: no resources exist
## With System State: POST_SESSION
#
GET /aramis/BROKER
Accept: */*

Nothing

###
200
Content-Type: text/plain

01BROKER                  **************
99BROKER                  **************000000002

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: POST_SESSION
#
GET /aramis/ARAFICO01
Accept: */*

Nothing

###
200
Content-Type: text/plain


{offset:0}
{offset:1}
{offset:2}
{offset:3}
{offset:10}
{offset:11}
{offset:12}
{offset:13}
{offset:20}
{offset:21}
{offset:22}
{offset:23}
{offset:30}
{offset:31}
{offset:32}
{offset:33}

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
GET /aramis/ARAFICO01
Accept: */*

Nothing

###
412
Content-Type: application/json

{
  "errors": [
    {
      "type": "1015",
      "title": "Operation not permitted in current system state",
      "detail": "Operation is not permitted in TRADING_SESSION and can only be executed in [POST_SESSION]"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: POST_SESSION
#
GET /aramis/ARAFICO02
Accept: */*

Nothing

###
200
Content-Type: text/plain

{offset:0}
{offset:1}
{offset:2}
{offset:5}
{offset:6}
{offset:10}
{offset:11}
{offset:12}
{offset:15}
{offset:16}
{offset:20}
{offset:21}
{offset:22}
{offset:25}
{offset:26}
{offset:30}
{offset:31}
{offset:32}
{offset:35}
{offset:36}


########################################################################################################################

#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
GET /aramis/ARAFICO02
Accept: */*

Nothing

###
412
Content-Type: application/json

{
  "errors": [
    {
      "type": "1015",
      "title": "Operation not permitted in current system state",
      "detail": "Operation is not permitted in TRADING_SESSION and can only be executed in [POST_SESSION]"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: POST_SESSION
#
GET /aramis/ARAFICO03
Accept: */*

Nothing

###
200
Content-Type: text/plain

{offset:8}
{offset:9}
{offset:18}
{offset:19}
{offset:28}
{offset:29}
{offset:38}
{offset:39}


########################################################################################################################

#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
GET /aramis/ARAFICO03
Accept: */*

Nothing

###
412
Content-Type: application/json

{
  "errors": [
    {
      "type": "1015",
      "title": "Operation not permitted in current system state",
      "detail": "Operation is not permitted in TRADING_SESSION and can only be executed in [POST_SESSION]"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: POST_SESSION
#
GET /aramis/ARAFICO05
Accept: */*

Nothing

###
200
Content-Type: text/plain

{offset:4}
{offset:5}
{offset:6}
{offset:14}
{offset:15}
{offset:16}
{offset:24}
{offset:25}
{offset:26}
{offset:34}
{offset:35}
{offset:36}


########################################################################################################################

#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
GET /aramis/ARAFICO05
Accept: */*

Nothing

###
412
Content-Type: application/json

{
  "errors": [
    {
      "type": "1015",
      "title": "Operation not permitted in current system state",
      "detail": "Operation is not permitted in TRADING_SESSION and can only be executed in [POST_SESSION]"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: POST_SESSION
#
GET /aramis/closing_price
Accept: */*

Nothing

###
200
Content-Type: text/plain

{offset:7}
{offset:17}
{offset:27}
{offset:37}


########################################################################################################################

#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
GET /aramis/closing_price
Accept: */*

Nothing

###
412
Content-Type: application/json

{
  "errors": [
    {
      "type": "1015",
      "title": "Operation not permitted in current system state",
      "detail": "Operation is not permitted in TRADING_SESSION and can only be executed in [POST_SESSION]"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: POST_SESSION
#
GET /aramis/FORDSUP
Accept: */*

Nothing

###
200
Content-Type: text/plain

01Ord withdrawn during PS 21/02/2021154620
{ARAMIS withdrawn:0}
{ARAMIS withdrawn:1}
{ARAMIS withdrawn:2}
{ARAMIS withdrawn:3}
{ARAMIS withdrawn:4}
{ARAMIS withdrawn:5}
{ARAMIS withdrawn:6}
{ARAMIS withdrawn:7}
{ARAMIS withdrawn:8}
{ARAMIS withdrawn:9}
{ARAMIS withdrawn:10}
{ARAMIS withdrawn:11}
{ARAMIS withdrawn:12}
{ARAMIS withdrawn:13}
{ARAMIS withdrawn:14}
{ARAMIS withdrawn:15}
{ARAMIS withdrawn:16}
{ARAMIS withdrawn:17}
{ARAMIS withdrawn:18}
{ARAMIS withdrawn:19}
{ARAMIS withdrawn:20}
{ARAMIS withdrawn:21}
{ARAMIS withdrawn:22}
{ARAMIS withdrawn:23}
{ARAMIS withdrawn:24}
{ARAMIS withdrawn:25}
{ARAMIS withdrawn:26}
{ARAMIS withdrawn:27}
{ARAMIS withdrawn:28}
{ARAMIS withdrawn:29}
{ARAMIS withdrawn:30}
{ARAMIS withdrawn:31}
{ARAMIS withdrawn:32}
{ARAMIS withdrawn:33}
{ARAMIS withdrawn:34}
{ARAMIS withdrawn:35}
{ARAMIS withdrawn:36}
{ARAMIS withdrawn:37}
{ARAMIS withdrawn:38}
{ARAMIS withdrawn:39}
03Ord withdrawn during PS 21/02/2021154620000000042

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
GET /aramis/FORDSUP
Accept: */*

Nothing

###
412
Content-Type: application/json

{
  "errors": [
    {
      "type": "1015",
      "title": "Operation not permitted in current system state",
      "detail": "Operation is not permitted in TRADING_SESSION and can only be executed in [POST_SESSION]"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: POST_SESSION
#
GET /aramis/OPEN_ORDERS
Accept: */*

Nothing

###
200
Content-Type: text/plain

01Ord remaining in the bk 21/02/2021154620
{ARAMIS remaining:0}
{ARAMIS remaining:1}
{ARAMIS remaining:2}
{ARAMIS remaining:3}
{ARAMIS remaining:4}
{ARAMIS remaining:5}
{ARAMIS remaining:6}
{ARAMIS remaining:7}
{ARAMIS remaining:8}
{ARAMIS remaining:9}
{ARAMIS remaining:10}
{ARAMIS remaining:11}
{ARAMIS remaining:12}
{ARAMIS remaining:13}
{ARAMIS remaining:14}
{ARAMIS remaining:15}
{ARAMIS remaining:16}
{ARAMIS remaining:17}
{ARAMIS remaining:18}
{ARAMIS remaining:19}
{ARAMIS remaining:20}
{ARAMIS remaining:21}
{ARAMIS remaining:22}
{ARAMIS remaining:23}
{ARAMIS remaining:24}
{ARAMIS remaining:25}
{ARAMIS remaining:26}
{ARAMIS remaining:27}
{ARAMIS remaining:28}
{ARAMIS remaining:29}
{ARAMIS remaining:30}
{ARAMIS remaining:31}
{ARAMIS remaining:32}
{ARAMIS remaining:33}
{ARAMIS remaining:34}
{ARAMIS remaining:35}
{ARAMIS remaining:36}
{ARAMIS remaining:37}
{ARAMIS remaining:38}
03Ord remaining in the bk 21/02/2021154620000000041

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
GET /aramis/OPEN_ORDERS
Accept: */*

Nothing

###
412
Content-Type: application/json

{
  "errors": [
    {
      "type": "1015",
      "title": "Operation not permitted in current system state",
      "detail": "Operation is not permitted in TRADING_SESSION and can only be executed in [POST_SESSION]"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: POST_SESSION
#
GET /aramis/BROKER
Accept: */*

Nothing

###
200
Content-Type: text/plain

01BROKER                  **************
{ARAMIS broker:0}
{ARAMIS broker:1}
{ARAMIS broker:2}
{ARAMIS broker:3}
{ARAMIS broker:4}
{ARAMIS broker:5}
{ARAMIS broker:6}
{ARAMIS broker:7}
{ARAMIS broker:8}
{ARAMIS broker:9}
{ARAMIS broker:10}
{ARAMIS broker:11}
{ARAMIS broker:12}
{ARAMIS broker:13}
{ARAMIS broker:14}
{ARAMIS broker:15}
{ARAMIS broker:16}
{ARAMIS broker:17}
{ARAMIS broker:18}
{ARAMIS broker:19}
{ARAMIS broker:20}
{ARAMIS broker:21}
{ARAMIS broker:22}
{ARAMIS broker:23}
{ARAMIS broker:24}
{ARAMIS broker:25}
{ARAMIS broker:26}
{ARAMIS broker:27}
{ARAMIS broker:28}
{ARAMIS broker:29}
{ARAMIS broker:30}
{ARAMIS broker:31}
{ARAMIS broker:32}
{ARAMIS broker:33}
{ARAMIS broker:34}
{ARAMIS broker:35}
{ARAMIS broker:36}
{ARAMIS broker:37}
{ARAMIS broker:38}
{ARAMIS broker:39}
99BROKER                  **************000000042

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
GET /aramis/BROKER
Accept: */*

Nothing

###
412
Content-Type: application/json

{
  "errors": [
    {
      "type": "1015",
      "title": "Operation not permitted in current system state",
      "detail": "Operation is not permitted in TRADING_SESSION and can only be executed in [POST_SESSION]"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /aramis/CALENDAR
Accept: */*

Nothing

###
200
Content-Type: text/plain

032060                8A**************
{ARAMIS calendar:0}
{ARAMIS calendar:1}
{ARAMIS calendar:2}
{ARAMIS calendar:3}
{ARAMIS calendar:4}
{ARAMIS calendar:5}
{ARAMIS calendar:6}
{ARAMIS calendar:7}
{ARAMIS calendar:8}
{ARAMIS calendar:9}
{ARAMIS calendar:10}
{ARAMIS calendar:11}
{ARAMIS calendar:12}
{ARAMIS calendar:13}
{ARAMIS calendar:14}
{ARAMIS calendar:15}
{ARAMIS calendar:16}
{ARAMIS calendar:17}
{ARAMIS calendar:18}
{ARAMIS calendar:19}
{ARAMIS calendar:20}
{ARAMIS calendar:21}
{ARAMIS calendar:22}
{ARAMIS calendar:23}
{ARAMIS calendar:24}
{ARAMIS calendar:25}
{ARAMIS calendar:26}
{ARAMIS calendar:27}
{ARAMIS calendar:28}
{ARAMIS calendar:29}
{ARAMIS calendar:30}
{ARAMIS calendar:31}
{ARAMIS calendar:32}
{ARAMIS calendar:33}
{ARAMIS calendar:34}
{ARAMIS calendar:35}
{ARAMIS calendar:36}
{ARAMIS calendar:37}
{ARAMIS calendar:38}
{ARAMIS calendar:39}
032060                8B**************

########################################################################################################################

#
## Given: no resources exist
#
GET /aramis/CALENDAR
Accept: */*

Nothing

###
200
Content-Type: text/plain

032060                8A**************
032060                8B**************

########################################################################################################################

#
## Given: no resources exist
## With System State: POST_SESSION
#
GET /aramis/ARAFICO06
Accept: */*

Nothing

###
200
Content-Type: text/plain

########################################################################################################################

#
## Given: no resources exist
## With System State: POST_SESSION
#
GET /aramis/ARAFICO07
Accept: */*

Nothing

###
200
Content-Type: text/plain

########################################################################################################################

#
## Given: no resources exist
## With System State: POST_SESSION
#
GET /aramis/PLC
Accept: */*

Nothing

###
200
Content-Type: text/plain

########################################################################################################################

#
## Given: no resources exist
## With System State: POST_SESSION
#
GET /aramis/CLIENT_ACCOUNT
Accept: */*

Nothing

###
200
Content-Type: text/plain

########################################################################################################################

#
## Given: no resources exist
## With System State: POST_SESSION
#
GET /aramis/liquidity_provider
Accept: */*

Nothing

###
200
Content-Type: text/plain

01LIQUIDITY_PROVIDER      **************
99LIQUIDITY_PROVIDER      **************000000002

########################################################################################################################