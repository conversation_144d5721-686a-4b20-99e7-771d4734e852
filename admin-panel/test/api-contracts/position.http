#
## Given: there is a single resource
#
GET /shareholders/1125915421/positions/IRO1SPY00006
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "productId": "IRO1SPY00006",
  "shareholderId": "1125915421",
  "ownership": 1000,
  "pendingBuy": 10,
  "pendingSell": 20,
  "isBuyBlocked": false,
  "isSellBlocked": false,
  "blockedOwnership": 5,
  "currentSessionBlockedOwnership": 0
}

########################################################################################################################

#
## Given: there is a single resource
#
GET /shareholders/1125915421/positions/IRCISIN0000-
Accept: application/json

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Product' not resolvable by 'IRCISIN0000-'"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
#
GET /shareholders/1125915421/positions
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "productId": "IRO1SPY00006",
      "shareholderId": "1125915421",
      "ownership": 1000,
      "pendingBuy": 10,
      "pendingSell": 20,
      "isBuyBlocked": false,
      "isSellBlocked": false,
      "blockedOwnership": 5,
      "currentSessionBlockedOwnership": 0
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 1,
    "offset": 0,
    "total": 1
  }
}

########################################################################################################################
#
## Given: there is a single resource
#
GET /shareholders/1125915421-/positions
Accept: application/json

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Shareholder' not resolvable by '1125915421-'"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /shareholders/1125915420/positions
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "productId": "IRO1SPY00006",
      "shareholderId": "1125915420",
      "ownership": 1000,
      "pendingBuy": 10,
      "pendingSell": 20,
      "isBuyBlocked": false,
      "isSellBlocked": false,
      "blockedOwnership": 5,
      "currentSessionBlockedOwnership": 0
    },
    {
      "productId": "IRO1SPY00014",
      "shareholderId": "1125915420",
      "ownership": 1000,
      "pendingBuy": 10,
      "pendingSell": 20,
      "isBuyBlocked": false,
      "isSellBlocked": false,
      "blockedOwnership": 5,
      "currentSessionBlockedOwnership": 0
    },
    {
      "productId": "IRO1SPY00022",
      "shareholderId": "1125915420",
      "ownership": 1000,
      "pendingBuy": 10,
      "pendingSell": 20,
      "isBuyBlocked": false,
      "isSellBlocked": false,
      "blockedOwnership": 5,
      "currentSessionBlockedOwnership": 0
    },
    {
      "productId": "IRO1SPY00030",
      "shareholderId": "1125915420",
      "ownership": 1000,
      "pendingBuy": 10,
      "pendingSell": 20,
      "isBuyBlocked": false,
      "isSellBlocked": false,
      "blockedOwnership": 5,
      "currentSessionBlockedOwnership": 0
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 4,
    "offset": 0,
    "total": 4
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /shareholders/1125915420/positions?page=0&size=3
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "productId": "IRO1SPY00006",
      "shareholderId": "1125915420",
      "ownership": 1000,
      "pendingBuy": 10,
      "pendingSell": 20,
      "isBuyBlocked": false,
      "isSellBlocked": false,
      "blockedOwnership": 5,
      "currentSessionBlockedOwnership": 0
    },
    {
      "productId": "IRO1SPY00014",
      "shareholderId": "1125915420",
      "ownership": 1000,
      "pendingBuy": 10,
      "pendingSell": 20,
      "isBuyBlocked": false,
      "isSellBlocked": false,
      "blockedOwnership": 5,
      "currentSessionBlockedOwnership": 0
    },
    {
      "productId": "IRO1SPY00022",
      "shareholderId": "1125915420",
      "ownership": 1000,
      "pendingBuy": 10,
      "pendingSell": 20,
      "isBuyBlocked": false,
      "isSellBlocked": false,
      "blockedOwnership": 5,
      "currentSessionBlockedOwnership": 0
    }
  ],
  "page": {
    "size": 3,
    "number": 0,
    "total": 2
  },
  "element": {
    "count": 3,
    "offset": 0,
    "total": 4
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /shareholders/1125915420/positions?page=1&size=3
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "productId": "IRO1SPY00030",
      "shareholderId": "1125915420",
      "ownership": 1000,
      "pendingBuy": 10,
      "pendingSell": 20,
      "isBuyBlocked": false,
      "isSellBlocked": false,
      "blockedOwnership": 5,
      "currentSessionBlockedOwnership": 0
    }
  ],
  "page": {
    "size": 3,
    "number": 1,
    "total": 2
  },
  "element": {
    "count": 1,
    "offset": 3,
    "total": 4
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /shareholders/1125915420/positions?page=2&size=3
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [],
  "page": {
    "size": 3,
    "number": 2,
    "total": 2
  },
  "element": {
    "count": 0,
    "offset": 4,
    "total": 4
  }
}

########################################################################################################################
