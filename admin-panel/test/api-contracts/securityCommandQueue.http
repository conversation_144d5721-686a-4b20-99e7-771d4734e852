#
## Given: no resources exist
#
GET /securities/IRO1SPY00001/queued-commands
Content-Type: application/json

###
404
Content-Type: application/json

{
  "messages": [
    "'Security' not resolved by 'IRO1SPY00001'"
  ]
}

########################################################################################################################

#
## Given: there is a single resource
#
GET /securities/IRO1SPY00001/queued-commands
Accept: application/json

Nothing

###
200
Content-Type: application/json



{
  "content": [
    {
      "status": "IN_QUEUE",
      "adminCommandType": "UPDATE_SECURITY_SPECIFICATION",
      "minorPriority": 0,
      "majorPriority": "SECURITY_UPDATE",
      "request": {
        "securityId": "IRO1SPY00001",
        "updateSpec": {
          "settlementDelay": 3,
          "baseVolume": 1000000,
          "orderSpec": {
            "priceTick": "1000",
            "lotSize": 1000,
            "minQuantity": 100,
            "maxBuyQuantity": 100000,
            "maxSellQuantity": 4000
          },
          "isShortSellAllowed": false
        }
      },
      "securityId": "IRO1SPY00001"
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 1,
    "offset": 0,
    "total": 1
  }
}

########################################################################################################################

#
## Given: there is a single resource
#
GET /securities/IRO1SPY00001/queued-commands/CORPORATE_ACTION/1
Accept: application/json

Nothing

###
412
Content-Type: application/json

{
  "errors": [
    {
      "type": "1023",
      "title": "Command Queue priority is violated",
      "detail": "Major priority: CORPORATE_ACTION is invalid for securities!"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /securities/IRO1SPY00021/queued-commands/SECURITY_DELETION/0
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "securityId": "IRO1SPY00021",
  "status": "IN_QUEUE",
  "adminCommandType": "PURGE_SECURITY",
  "minorPriority": 0,
  "majorPriority": "SECURITY_DELETION",
  "request": {
    "securityId": "IRO1SPY00021"
  }
}

########################################################################################################################

#
## Given: there is a single resource
#
GET /securities/IRO1SPY00001/queued-commands/SECURITY_DELETION/10
Accept: application/json

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1009",
      "title": "No such element",
      "detail": "'CommandQueueItem' not found"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /securities/IRO1SPY00003/queued-commands/latest-update
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "status": "IN_QUEUE",
  "adminCommandType": "UPDATE_SECURITY_SPECIFICATION",
  "minorPriority": 0,
  "majorPriority": "SECURITY_UPDATE",
  "request": {
    "securityId": "IRO1SPY00003",
    "updateSpec": {
      "settlementDelay": 3,
      "baseVolume": 1000000,
      "orderSpec": {
        "priceTick": "1000",
        "lotSize": 1000,
        "minQuantity": 100,
        "maxBuyQuantity": 100000,
        "maxSellQuantity": 4000
      },
      "isShortSellAllowed": false
    }
  },
  "securityId": "IRO1SPY00003"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /securities/IRO1SPY00001/queued-commands/SECURITY_UPDATE/0

Nothing

###
200

NOTHING

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /securities/IRO1SPY00001/queued-commands/SECURITY_DELETION/10

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1009",
      "title": "No such element",
      "detail": "'CommandQueueItem' not found"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /securities/IRO1SPY00001/queued-commands/CORPORATE_ACTION/2

Nothing

###
412
Content-Type: application/json

{
  "errors": [
    {
      "type": "1023",
      "title": "Command Queue priority is violated",
      "detail": "Major priority: CORPORATE_ACTION is invalid for securities!"
    }
  ]
}
