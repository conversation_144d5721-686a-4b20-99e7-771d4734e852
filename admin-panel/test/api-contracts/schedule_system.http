#
## Given: there is a single resource
#
GET /schedules/system
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "from": "2025-01-01",
      "scheduleTemplates": [
        {
          "scheduleTemplate": {
            "name": {
              "en": "System Template",
              "fa": "الگو سیستم"
            },
            "code": "TS",
            "type": "SYSTEM"
          },
          "dayOfWeek": "MONDAY"
        },
        {
          "scheduleTemplate": {
            "name": {
              "en": "System Template",
              "fa": "الگو سیستم"
            },
            "code": "TS",
            "type": "SYSTEM"
          },
          "dayOfWeek": "TUESDAY"
        },
        {
          "scheduleTemplate": {
            "name": {
              "en": "System Template",
              "fa": "الگو سیستم"
            },
            "code": "TS",
            "type": "SYSTEM"
          },
          "dayOfWeek": "WEDNESDAY"
        },
        {
          "scheduleTemplate": {
            "name": {
              "en": "System Template",
              "fa": "الگو سیستم"
            },
            "code": "TS",
            "type": "SYSTEM"
          },
          "dayOfWeek": "THURSDAY"
        },
        {
          "scheduleTemplate": {
            "name": {
              "en": "System Template",
              "fa": "الگو سیستم"
            },
            "code": "TS",
            "type": "SYSTEM"
          },
          "dayOfWeek": "FRIDAY"
        },
        {
          "scheduleTemplate": {
            "name": {
              "en": "System Template",
              "fa": "الگو سیستم"
            },
            "code": "TS",
            "type": "SYSTEM"
          },
          "dayOfWeek": "SATURDAY"
        },
        {
          "scheduleTemplate": {
            "name": {
              "en": "System Template",
              "fa": "الگو سیستم"
            },
            "code": "TS",
            "type": "SYSTEM"
          },
          "dayOfWeek": "SUNDAY"
        }
      ]
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 1,
    "offset": 0,
    "total": 1
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /schedules/system?page=0&size=7
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "from": "2025-01-01",
      "scheduleTemplates": [
        {
          "scheduleTemplate": {
            "name": {
              "en": "Template 3",
              "fa": "الگو 3"
            },
            "code": "T3",
            "type": "SYSTEM"
          },
          "dayOfWeek": "MONDAY"
        },
        {
          "scheduleTemplate": {
            "name": {
              "en": "Template 3",
              "fa": "الگو 3"
            },
            "code": "T3",
            "type": "SYSTEM"
          },
          "dayOfWeek": "TUESDAY"
        },
        {
          "scheduleTemplate": {
            "name": {
              "en": "Template 3",
              "fa": "الگو 3"
            },
            "code": "T3",
            "type": "SYSTEM"
          },
          "dayOfWeek": "WEDNESDAY"
        },
        {
          "scheduleTemplate": {
            "name": {
              "en": "Template 3",
              "fa": "الگو 3"
            },
            "code": "T3",
            "type": "SYSTEM"
          },
          "dayOfWeek": "THURSDAY"
        },
        {
          "scheduleTemplate": {
            "name": {
              "en": "Template 3",
              "fa": "الگو 3"
            },
            "code": "T3",
            "type": "SYSTEM"
          },
          "dayOfWeek": "FRIDAY"
        },
        {
          "scheduleTemplate": {
            "name": {
              "en": "Template 3",
              "fa": "الگو 3"
            },
            "code": "T3",
            "type": "SYSTEM"
          },
          "dayOfWeek": "SATURDAY"
        },
        {
          "scheduleTemplate": {
            "name": {
              "en": "Template 3",
              "fa": "الگو 3"
            },
            "code": "T3",
            "type": "SYSTEM"
          },
          "dayOfWeek": "SUNDAY"
        }
      ]
    }
  ],
  "page": {
    "size": 7,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 1,
    "offset": 0,
    "total": 1
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /schedules/system?from=2025-10-10
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "from": "2025-01-01",
      "scheduleTemplates": [
        {
          "scheduleTemplate": {
            "name": {
              "en": "Template 3",
              "fa": "الگو 3"
            },
            "code": "T3",
            "type": "SYSTEM"
          },
          "dayOfWeek": "MONDAY"
        },
        {
          "scheduleTemplate": {
            "name": {
              "en": "Template 3",
              "fa": "الگو 3"
            },
            "code": "T3",
            "type": "SYSTEM"
          },
          "dayOfWeek": "TUESDAY"
        },
        {
          "scheduleTemplate": {
            "name": {
              "en": "Template 3",
              "fa": "الگو 3"
            },
            "code": "T3",
            "type": "SYSTEM"
          },
          "dayOfWeek": "WEDNESDAY"
        },
        {
          "scheduleTemplate": {
            "name": {
              "en": "Template 3",
              "fa": "الگو 3"
            },
            "code": "T3",
            "type": "SYSTEM"
          },
          "dayOfWeek": "THURSDAY"
        },
        {
          "scheduleTemplate": {
            "name": {
              "en": "Template 3",
              "fa": "الگو 3"
            },
            "code": "T3",
            "type": "SYSTEM"
          },
          "dayOfWeek": "FRIDAY"
        },
        {
          "scheduleTemplate": {
            "name": {
              "en": "Template 3",
              "fa": "الگو 3"
            },
            "code": "T3",
            "type": "SYSTEM"
          },
          "dayOfWeek": "SATURDAY"
        },
        {
          "scheduleTemplate": {
            "name": {
              "en": "Template 3",
              "fa": "الگو 3"
            },
            "code": "T3",
            "type": "SYSTEM"
          },
          "dayOfWeek": "SUNDAY"
        }
      ]
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 1,
    "offset": 0,
    "total": 1
  }
}

########################################################################################################################

#
## Given: there is a single resource
#
GET /schedules/system/2025-01-01
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "from": "2025-01-01",
  "scheduleTemplates": [
    {
      "scheduleTemplate": {
        "name": {
          "en": "System Template",
          "fa": "الگو سیستم"
        },
        "code": "TS",
        "type": "SYSTEM"
      },
      "dayOfWeek": "MONDAY"
    },
    {
      "scheduleTemplate": {
        "name": {
          "en": "System Template",
          "fa": "الگو سیستم"
        },
        "code": "TS",
        "type": "SYSTEM"
      },
      "dayOfWeek": "TUESDAY"
    },
    {
      "scheduleTemplate": {
        "name": {
          "en": "System Template",
          "fa": "الگو سیستم"
        },
        "code": "TS",
        "type": "SYSTEM"
      },
      "dayOfWeek": "WEDNESDAY"
    },
    {
      "scheduleTemplate": {
        "name": {
          "en": "System Template",
          "fa": "الگو سیستم"
        },
        "code": "TS",
        "type": "SYSTEM"
      },
      "dayOfWeek": "THURSDAY"
    },
    {
      "scheduleTemplate": {
        "name": {
          "en": "System Template",
          "fa": "الگو سیستم"
        },
        "code": "TS",
        "type": "SYSTEM"
      },
      "dayOfWeek": "FRIDAY"
    },
    {
      "scheduleTemplate": {
        "name": {
          "en": "System Template",
          "fa": "الگو سیستم"
        },
        "code": "TS",
        "type": "SYSTEM"
      },
      "dayOfWeek": "SATURDAY"
    },
    {
      "scheduleTemplate": {
        "name": {
          "en": "System Template",
          "fa": "الگو سیستم"
        },
        "code": "TS",
        "type": "SYSTEM"
      },
      "dayOfWeek": "SUNDAY"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
POST /schedules/system
Accept: application/json

{"from": "2030-01-01", "scheduleTemplates": [{"dayOfWeek": "SATURDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "SUNDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "MONDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "TUESDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "WEDNESDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "THURSDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "FRIDAY", "scheduleTemplateCode": "T3"}]}

###
200
Content-Type: application/json

{
  "from": "2030-01-01",
  "scheduleTemplates": [
    {
      "scheduleTemplate": {
        "name": {
          "en": "Template 3",
          "fa": "الگو 3"
        },
        "code": "T3",
        "type": "SYSTEM"
      },
      "dayOfWeek": "MONDAY"
    },
    {
      "scheduleTemplate": {
        "name": {
          "en": "Template 3",
          "fa": "الگو 3"
        },
        "code": "T3",
        "type": "SYSTEM"
      },
      "dayOfWeek": "TUESDAY"
    },
    {
      "scheduleTemplate": {
        "name": {
          "en": "Template 3",
          "fa": "الگو 3"
        },
        "code": "T3",
        "type": "SYSTEM"
      },
      "dayOfWeek": "WEDNESDAY"
    },
    {
      "scheduleTemplate": {
        "name": {
          "en": "Template 3",
          "fa": "الگو 3"
        },
        "code": "T3",
        "type": "SYSTEM"
      },
      "dayOfWeek": "THURSDAY"
    },
    {
      "scheduleTemplate": {
        "name": {
          "en": "Template 3",
          "fa": "الگو 3"
        },
        "code": "T3",
        "type": "SYSTEM"
      },
      "dayOfWeek": "FRIDAY"
    },
    {
      "scheduleTemplate": {
        "name": {
          "en": "Template 3",
          "fa": "الگو 3"
        },
        "code": "T3",
        "type": "SYSTEM"
      },
      "dayOfWeek": "SATURDAY"
    },
    {
      "scheduleTemplate": {
        "name": {
          "en": "Template 3",
          "fa": "الگو 3"
        },
        "code": "T3",
        "type": "SYSTEM"
      },
      "dayOfWeek": "SUNDAY"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
POST /schedules/system
Accept: application/json

{"from": "2025-01-01", "scheduleTemplates": [{"dayOfWeek": "SATURDAY", "scheduleTemplateCode": "T1"}, {"dayOfWeek": "SUNDAY", "scheduleTemplateCode": "T1"}, {"dayOfWeek": "MONDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "TUESDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "WEDNESDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "THURSDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "FRIDAY", "scheduleTemplateCode": "T3"}]}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1008",
      "title": "Validation error",
      "detail": "For template T1: Actions are not compatible with this schedulable,\nFor template T1: Actions are not compatible with this schedulable"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
POST /schedules/system
Accept: application/json

{"from": "2025-01-01", "scheduleTemplates": [{"dayOfWeek": "SATURDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "SUNDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "MONDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "TUESDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "WEDNESDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "THURSDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "FRIDAY", "scheduleTemplateCode": "T3"}]}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1008",
      "title": "Validation error",
      "detail": "A schedule already exists!"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
POST /schedules/system
Accept: application/json

{"from": "2000-01-01", "scheduleTemplates": [{"dayOfWeek": "SATURDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "SUNDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "MONDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "TUESDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "WEDNESDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "THURSDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "FRIDAY", "scheduleTemplateCode": "T3"}]}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1008",
      "title": "Validation error",
      "detail": "Date is not in the future"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
PUT /schedules/system/2025-01-01
Accept: application/json

{"scheduleTemplates": [{"dayOfWeek": "SATURDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "SUNDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "MONDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "TUESDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "WEDNESDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "THURSDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "FRIDAY", "scheduleTemplateCode": "T3"}]}

###
200
Content-Type: application/json

{
  "from": "2025-01-01",
  "scheduleTemplates": [
    {
      "scheduleTemplate": {
        "name": {
          "en": "Template 3",
          "fa": "الگو 3"
        },
        "code": "T3",
        "type": "SYSTEM"
      },
      "dayOfWeek": "MONDAY"
    },
    {
      "scheduleTemplate": {
        "name": {
          "en": "Template 3",
          "fa": "الگو 3"
        },
        "code": "T3",
        "type": "SYSTEM"
      },
      "dayOfWeek": "TUESDAY"
    },
    {
      "scheduleTemplate": {
        "name": {
          "en": "Template 3",
          "fa": "الگو 3"
        },
        "code": "T3",
        "type": "SYSTEM"
      },
      "dayOfWeek": "WEDNESDAY"
    },
    {
      "scheduleTemplate": {
        "name": {
          "en": "Template 3",
          "fa": "الگو 3"
        },
        "code": "T3",
        "type": "SYSTEM"
      },
      "dayOfWeek": "THURSDAY"
    },
    {
      "scheduleTemplate": {
        "name": {
          "en": "Template 3",
          "fa": "الگو 3"
        },
        "code": "T3",
        "type": "SYSTEM"
      },
      "dayOfWeek": "FRIDAY"
    },
    {
      "scheduleTemplate": {
        "name": {
          "en": "Template 3",
          "fa": "الگو 3"
        },
        "code": "T3",
        "type": "SYSTEM"
      },
      "dayOfWeek": "SATURDAY"
    },
    {
      "scheduleTemplate": {
        "name": {
          "en": "Template 3",
          "fa": "الگو 3"
        },
        "code": "T3",
        "type": "SYSTEM"
      },
      "dayOfWeek": "SUNDAY"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
PUT /schedules/system/2030-01-01
Accept: application/json

{"scheduleTemplates": [{"dayOfWeek": "SATURDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "SUNDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "MONDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "TUESDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "WEDNESDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "THURSDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "FRIDAY", "scheduleTemplateCode": "T3"}]}

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1009",
      "title": "No such element",
      "detail": "'Schedule' not found"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
PUT /schedules/system/2025-01-01
Accept: application/json

{"scheduleTemplates": [{"dayOfWeek": "SATURDAY", "scheduleTemplateCode": ""}, {"dayOfWeek": "SUNDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "MONDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "TUESDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "WEDNESDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "THURSDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "FRIDAY", "scheduleTemplateCode": "T3"}]}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'scheduleTemplates[0].scheduleTemplateCode' must not be blank"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
PUT /schedules/system/2000-01-01
Accept: application/json

{"scheduleTemplates": [{"dayOfWeek": "SATURDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "SUNDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "MONDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "TUESDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "WEDNESDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "THURSDAY", "scheduleTemplateCode": "T3"}, {"dayOfWeek": "FRIDAY", "scheduleTemplateCode": "T3"}]}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1008",
      "title": "Validation error",
      "detail": "Date is not in the future"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
DELETE /schedules/system/2025-01-01

Nothing

###
200

Nothing

########################################################################################################################

#
## Given: there are a lot of resources
#
DELETE /schedules/system/2030-01-01

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1009",
      "title": "No such element",
      "detail": "'Schedule' not found"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
DELETE /schedules/system/2000-01-01

Nothing

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1008",
      "title": "Validation error",
      "detail": "Date is not in the future"
    }
  ]
}

########################################################################################################################
