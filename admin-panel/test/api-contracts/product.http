#
## Given: no resources exist
#
GET /products/IRCISIN00000
Accept: application/json

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Product' not resolvable by 'IRCISIN00000'"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
#
GET /products/IRCISIN0000-
Accept: application/json

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Product' not resolvable by 'IRCISIN0000-'"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /products
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "productId": "IRO1SPY00006",
      "ghostStatus": "PERSISTED",
      "company": {
        "code": "SPY0",
        "shortName": {
          "en": "SPDR S&P 500 Trust",
          "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
        },
        "fullName": {
          "en": "SPDR S&P 500 Trust",
          "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
        },
        "sectorCode": "S0",
        "subSectorCode": "SS0",
        "creationDate": "2000-01-01",
        "updateDate": "2000-01-01"
      },
      "boardCode": "0",
      "productTypeCode": "O",
      "productSubTypeCode": "110",
      "marketFlowCode": "00",
      "referencePrice": "invalid",
      "totalShares": 900000000000,
      "maxOwnershipByShareholder": "0.2",
      "adjustedTotalShares": *********,
      "creationDate": "2000-01-01",
      "updateDate": "2000-01-01",
      "name": {
        "en": "SPDR S&P 500 Trust",
        "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
      },
      "mnemonic": {
        "en": "SPY0",
        "fa": "اس پی وای0"
      },
      "productCode": "SPY000",
      "tradingStartDate": "2000-01-01",
      "tradingEndDate": "2000-01-01",
      "maturityDate": "2000-01-01",
      "availableForSell": true,
      "isPricePercentage": false
    },
    {
      "productId": "IRO1SPY00014",
      "ghostStatus": "DELETED",
      "company": {
        "code": "SPY0",
        "shortName": {
          "en": "SPDR S&P 500 Trust",
          "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
        },
        "fullName": {
          "en": "SPDR S&P 500 Trust",
          "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
        },
        "sectorCode": "S0",
        "subSectorCode": "SS0",
        "creationDate": "2000-01-01",
        "updateDate": "2000-01-01"
      },
      "boardCode": "0",
      "productTypeCode": "O",
      "productSubTypeCode": "110",
      "marketFlowCode": "00",
      "referencePrice": "invalid",
      "totalShares": 900000000000,
      "maxOwnershipByShareholder": "0.2",
      "adjustedTotalShares": *********,
      "creationDate": "2000-01-01",
      "updateDate": "2000-01-01",
      "name": {
        "en": "SPDR S&P 500 Trust",
        "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
      },
      "mnemonic": {
        "en": "SPY1",
        "fa": "اس پی وای1"
      },
      "productCode": "SPY001",
      "tradingStartDate": "2000-01-01",
      "tradingEndDate": "2000-01-01",
      "maturityDate": "2000-01-01",
      "availableForSell": true,
      "isPricePercentage": false
    },
    {
      "productId": "IRO1SPY00022",
      "ghostStatus": "UPDATED",
      "company": {
        "code": "SPY0",
        "shortName": {
          "en": "SPDR S&P 500 Trust",
          "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
        },
        "fullName": {
          "en": "SPDR S&P 500 Trust",
          "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
        },
        "sectorCode": "S0",
        "subSectorCode": "SS0",
        "creationDate": "2000-01-01",
        "updateDate": "2000-01-01"
      },
      "boardCode": "0",
      "productTypeCode": "O",
      "productSubTypeCode": "110",
      "marketFlowCode": "00",
      "referencePrice": "invalid",
      "totalShares": 900000000000,
      "maxOwnershipByShareholder": "0.2",
      "adjustedTotalShares": *********,
      "creationDate": "2000-01-01",
      "updateDate": "2000-01-01",
      "name": {
        "en": "SPDR S&P 500 Trust",
        "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
      },
      "mnemonic": {
        "en": "SPY2",
        "fa": "اس پی وای2"
      },
      "productCode": "SPY002",
      "tradingStartDate": "2000-01-01",
      "tradingEndDate": "2000-01-01",
      "maturityDate": "2000-01-01",
      "availableForSell": true,
      "isPricePercentage": false,
      "ghostView": {
        "productId": "IRO1SPY00022",
        "company": {
          "code": "SPY0",
          "shortName": {
            "en": "SPDR S&P 500 Trust",
            "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
          },
          "fullName": {
            "en": "SPDR S&P 500 Trust",
            "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
          },
          "sectorCode": "S0",
          "subSectorCode": "SS0",
          "creationDate": "2000-01-01",
          "updateDate": "2000-01-01"
        },
        "boardCode": "0",
        "productTypeCode": "O",
        "productSubTypeCode": "110",
        "marketFlowCode": "00",
        "referencePrice": "invalid",
        "totalShares": 900000000000,
        "maxOwnershipByShareholder": "0.2",
        "adjustedTotalShares": *********,
        "creationDate": "2000-01-01",
        "updateDate": "2000-01-01",
        "name": {
          "en": "SPDR S&P 500 Trust",
          "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
        },
        "mnemonic": {
          "en": "SPY0",
          "fa": "اس پی وای0"
        },
        "productCode": "SPY002",
        "tradingStartDate": "2000-01-01",
        "tradingEndDate": "2000-01-01",
        "maturityDate": "2000-01-01",
        "availableForSell": true,
        "isPricePercentage": false
      }
    },
    {
      "productId": "IRO1SPY00030",
      "ghostStatus": "CREATED",
      "company": {
        "code": "SPY0",
        "shortName": {
          "en": "SPDR S&P 500 Trust",
          "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
        },
        "fullName": {
          "en": "SPDR S&P 500 Trust",
          "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
        },
        "sectorCode": "S0",
        "subSectorCode": "SS0",
        "creationDate": "2000-01-01",
        "updateDate": "2000-01-01"
      },
      "boardCode": "0",
      "productTypeCode": "O",
      "productSubTypeCode": "110",
      "marketFlowCode": "00",
      "referencePrice": "invalid",
      "totalShares": 900000000000,
      "maxOwnershipByShareholder": "0.2",
      "adjustedTotalShares": *********,
      "creationDate": "2000-01-01",
      "updateDate": "2000-01-01",
      "name": {
        "en": "SPDR S&P 500 Trust",
        "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
      },
      "mnemonic": {
        "en": "SPY3",
        "fa": "اس پی وای3"
      },
      "productCode": "SPY003",
      "tradingStartDate": "2000-01-01",
      "tradingEndDate": "2000-01-01",
      "maturityDate": "2000-01-01",
      "availableForSell": true,
      "isPricePercentage": false
    },
    {
      "productId": "IRO1SPY00048",
      "ghostStatus": "PERSISTED",
      "company": {
        "code": "SPY0",
        "shortName": {
          "en": "SPDR S&P 500 Trust",
          "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
        },
        "fullName": {
          "en": "SPDR S&P 500 Trust",
          "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
        },
        "sectorCode": "S0",
        "subSectorCode": "SS0",
        "creationDate": "2000-01-01",
        "updateDate": "2000-01-01"
      },
      "boardCode": "0",
      "productTypeCode": "O",
      "productSubTypeCode": "110",
      "marketFlowCode": "00",
      "referencePrice": "invalid",
      "totalShares": 900000000000,
      "maxOwnershipByShareholder": "0.2",
      "adjustedTotalShares": *********,
      "creationDate": "2000-01-01",
      "updateDate": "2000-01-01",
      "name": {
        "en": "SPDR S&P 500 Trust",
        "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
      },
      "mnemonic": {
        "en": "SPY4",
        "fa": "اس پی وای4"
      },
      "productCode": "SPY004",
      "tradingStartDate": "2000-01-01",
      "tradingEndDate": "2000-01-01",
      "maturityDate": "2000-01-01",
      "availableForSell": true,
      "isPricePercentage": false
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 5,
    "offset": 0,
    "total": 5
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /products/IRO1SPY00014
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "productId": "IRO1SPY00014",
  "ghostStatus": "DELETED",
  "company": {
    "code": "SPY0",
    "shortName": {
      "en": "SPDR S&P 500 Trust",
      "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
    },
    "fullName": {
      "en": "SPDR S&P 500 Trust",
      "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
    },
    "sectorCode": "S0",
    "subSectorCode": "SS0",
    "creationDate": "2000-01-01",
    "updateDate": "2000-01-01"
  },
  "boardCode": "0",
  "productTypeCode": "O",
  "productSubTypeCode": "110",
  "marketFlowCode": "00",
  "boardName": "تابلو 0",
  "productTypeName": "سهام عادی0",
  "productSubTypeName": "سهام 0",
  "marketFlowName": "مارکت فلو 0",
  "parValue": "1000.000",
  "referencePrice": "invalid",
  "totalShares": 900000000000,
  "maxOwnershipByShareholder": "0.2",
  "currentOwnershipByNationality": {
    "NATIONAL": 900000000000,
    "FOREIGNER": 0
  },
  "currentOwnershipByInvestorType": {
    "INDIVIDUAL": 900000000000,
    "NON_INDIVIDUAL": 0,
    "INDIVIDUAL_BOARD_DIRECTOR": 0
  },
  "maxOwnershipByNationality": {
    "NATIONAL": "1.0",
    "FOREIGNER": "0.0"
  },
  "maxOwnershipByInvestorType": {
    "INDIVIDUAL": "1.0",
    "NON_INDIVIDUAL": "0.0",
    "INDIVIDUAL_BOARD_DIRECTOR": "0.0"
  },
  "adjustedTotalShares": *********,
  "creationDate": "2000-01-01",
  "updateDate": "2000-01-01",
  "name": {
    "en": "SPDR S&P 500 Trust",
    "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
  },
  "mnemonic": {
    "en": "SPY1",
    "fa": "اس پی وای1"
  },
  "productCode": "SPY001",
  "matchingType": "FIFO",
  "issuePrice": "200.000",
  "strikePrice": "200.000",
  "underlyingProductId": "IRO1SPY00006",
  "tradingStartDate": "2000-01-01",
  "tradingEndDate": "2000-01-01",
  "maturityDate": "2000-01-01",
  "normalBlockSize": 1,
  "availableForSell": true,
  "isPricePercentage": false
}

#######################################################################################################################

#
## Given: there is a single resource
#
POST /products
Accept: application/json

{}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'availableForSell' must not be null"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'boardCode' field is required"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'companyCode' field is required"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'isPricePercentage' must not be null"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'marketFlowCode' must not be blank"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'matchingType' must not be null"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'mnemonic' must not be null"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'name' must not be null"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'normalBlockSize' must not be null"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'parValue' must not be blank"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'productCode' field is required"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'productId' field is required"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'productSubTypeCode' must not be blank"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'productTypeCode' must not be blank"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'referencePrice' must not be null"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'totalShares' must not be null"
    }
  ]
}

#######################################################################################################################
# Creates a product with all optional and non-optional fields
#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
POST /products
Content-Type: application/json

{
  "productId": "IRO1SPY00014",
  "securities": [
    {
      "priceBandPercentage": {
        "lowerBoundPercentage": "99.0",
        "upperBoundPercentage": "100.0"
      },
      "market": "NO",
      "groupId": "G0"
    }
  ],
  "companyCode": "SPY0",
  "boardCode": "0",
  "productTypeCode": "O",
  "productSubTypeCode": "110",
  "marketFlowCode": "00",
  "parValue": "100.000",
  "referencePrice": "200.000",
  "totalShares": 20000000000000000,
  "name": {
    "en": "IRCISIN00001",
    "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
  },
  "mnemonic": {
    "en": "SPY2",
    "fa": "اس پی وای2"
  },
  "productCode": "SPY000",
  "matchingType": "FIFO",
  "issuePrice": "200.000",
  "strikePrice": "200.000",
  "underlyingProductId": "IRCISIN00001",
  "tradingStartDate": "2021-01-01",
  "tradingEndDate": "2021-01-01",
  "maturityDate": "2021-01-01",
  "normalBlockSize": 1000000,
  "availableForSell": true,
  "isPricePercentage": false
}

###
200
Content-Type: application/json

{
  "status": "IN_QUEUE",
  "adminCommandType": "CREATE_PRODUCT",
  "minorPriority": 0,
  "majorPriority": "PRODUCT_CREATION",
  "request": {
    "productId": "IRO1SPY00014",
    "spec": {
      "productId": "IRO1SPY00014",
      "securities": [
        {
          "priceBandPercentage": {
            "lowerBoundPercentage": "99.0",
            "upperBoundPercentage": "100.0"
          },
          "groupId": "G0",
          "market": "NO"
        }
      ],
      "boardCode": "0",
      "productTypeCode": "O",
      "productSubTypeCode": "110",
      "marketFlowCode": "00",
      "companyCode": "SPY0",
      "parValue": "100.000",
      "referencePrice": "200.000",
      "totalShares": 20000000000000000,
      "name": {
        "en": "IRCISIN00001",
        "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
      },
      "mnemonic": {
        "en": "SPY2",
        "fa": "اس پی وای2"
      },
      "productCode": "SPY000",
      "matchingType": "FIFO",
      "issuePrice": "200.000",
      "strikePrice": "200.000",
      "underlyingProductId": "IRCISIN00001",
      "tradingStartDate": "2021-01-01",
      "tradingEndDate": "2021-01-01",
      "maturityDate": "2021-01-01",
      "normalBlockSize": 1000000,
      "availableForSell": true,
      "isPricePercentage": false
    }
  },
  "productId": "IRO1SPY00014"
}

#######################################################################################################################
# Creates a product with only non-optional fields
#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
POST /products
Content-Type: application/json

{
  "productId": "IRO1SPY00014",
  "companyCode": "SPY0",
  "boardCode": "0",
  "productTypeCode": "O",
  "productSubTypeCode": "110",
  "marketFlowCode": "00",
  "parValue": "100.000",
  "referencePrice": "200.000",
  "totalShares": 20000000000000000,
  "name": {
    "en": "IRCISIN00001",
    "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
  },
  "mnemonic": {
    "en": "SPY2",
    "fa": "اس پی وای2"
  },
  "productCode": "SPY000",
  "matchingType": "FIFO",
  "tradingStartDate": "",
  "tradingEndDate": "",
  "maturityDate": "",
  "normalBlockSize": 1000000,
  "availableForSell": true,
  "isPricePercentage": false
}

###
200
Content-Type: application/json

{
  "status": "IN_QUEUE",
  "adminCommandType": "CREATE_PRODUCT",
  "minorPriority": 0,
  "majorPriority": "PRODUCT_CREATION",
  "request": {
    "productId": "IRO1SPY00014",
    "spec": {
      "productId": "IRO1SPY00014",
      "boardCode": "0",
      "productTypeCode": "O",
      "productSubTypeCode": "110",
      "marketFlowCode": "00",
      "companyCode": "SPY0",
      "parValue": "100.000",
      "referencePrice": "200.000",
      "totalShares": 20000000000000000,
      "name": {
        "en": "IRCISIN00001",
        "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
      },
      "mnemonic": {
        "en": "SPY2",
        "fa": "اس پی وای2"
      },
      "productCode": "SPY000",
      "matchingType": "FIFO",
      "normalBlockSize": 1000000,
      "availableForSell": true,
      "isPricePercentage": false
    }
  },
  "productId": "IRO1SPY00014"
}

########################################################################################################################

# Check duplication on product productId
#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
POST /products
Content-Type: application/json

{
  "productId": "IRO1SPY00014",
  "companyCode": "SPY0",
  "boardCode": "0",
  "productTypeCode": "O",
  "productSubTypeCode": "110",
  "marketFlowCode": "00",
  "parValue": "100.000",
  "referencePrice": "200.000",
  "totalShares": 20000000000000000,
  "maxOwnershipByShareholder": "0.2",
  "adjustedTotalShares": 20000,
  "name": {
    "en": "IRCISIN00001",
    "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
  },
  "mnemonic": {
    "en": "SPY0",
    "fa": "اس پی وای0"
  },
  "productCode": "SPY000",
  "matchingType": "FIFO",
  "issuePrice": "200.000",
  "strikePrice": "200.000",
  "underlyingProductId": "IRCISIN00001",
  "tradingStartDate": "2021-01-01",
  "tradingEndDate": "2021-01-01",
  "maturityDate": "2021-01-01",
  "normalBlockSize": 1000000,
  "availableForSell": true,
  "isPricePercentage": false
}

###
412
Content-Type: application/json

{
  "errors": [
    {
      "type": "1023",
      "title": "Command Queue priority is violated",
      "detail": "Product with Mnemonic: SPY0|اس پی وای0 already exists"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /products/IRO1SPY00006/orders

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securities": [],
    "products": [
      "IRO1SPY00006"
    ],
    "brokers": [],
    "traders": [],
    "shareholders": [],
    "investors": [],
    "groups": [],
    "sides": []
  },
  "status": "AWAITING_RESPONSE",
  "type": "PURGE_ORDERS",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
#
DELETE /products/IRCISIN00001/orders?brokers=BRK0|BRK1&traders=TRD0|TRD1&shareholders=SHD0|SHD1&investors=INV0|INV1&
    groups=GRP0|GRP1&sides=BUY|SELL

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securities": [],
    "products": [
      "IRCISIN00001"
    ],
    "brokers": [
      "BRK0",
      "BRK1"
    ],
    "traders": [
      "TRD0",
      "TRD1"
    ],
    "shareholders": [
      "SHD0",
      "SHD1"
    ],
    "investors": [
      "INV0",
      "INV1"
    ],
    "groups": [
      "GRP0",
      "GRP1"
    ],
    "sides": [
      "BUY",
      "SELL"
    ]
  },
  "status": "AWAITING_RESPONSE",
  "type": "PURGE_ORDERS",
  "sender": "Test User"
}

########################################################################################################################
#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
DELETE /products/IRO1SPY00048
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "productId": "IRO1SPY00048",
  "status": "IN_QUEUE",
  "adminCommandType": "DELETE_PRODUCT",
  "minorPriority": 0,
  "majorPriority": "PRODUCT_DELETION",
  "request": {
    "productId": "IRO1SPY00048"
  }
}

########################################################################################################################

#
## Given: no resources exist
#
DELETE /products/SPY
Accept: application/json

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Product' not resolvable by 'SPY'"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
PUT /products/IRO1SPY00006
Content-Type: application/json

{
  "boardCode": "0",
  "marketFlowCode": "00",
  "parValue": "100.000",
  "adjustedTotalShares": 20000,
  "name": {
    "en": "IRCISIN00001",
    "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
  },
  "mnemonic": {
    "en": "SPY0",
    "fa": "اس پی وای0"
  },
  "matchingType": "FIFO",
  "issuePrice": "200.000",
  "strikePrice": "200.000",
  "underlyingProductId": "IRCISIN00001",
  "tradingStartDate": "2021-01-01",
  "tradingEndDate": "2021-01-01",
  "maturityDate": "2021-01-01",
  "normalBlockSize": 1000000,
  "isPricePercentage": false
}

###
200
Content-Type: application/json

{
  "productId": "IRO1SPY00006",
  "status": "IN_QUEUE",
  "adminCommandType": "UPDATE_PRODUCT",
  "minorPriority": 0,
  "majorPriority": "PRODUCT_UPDATE",
  "request": {
    "productId": "IRO1SPY00006",
    "spec": {
      "boardCode": "0",
      "marketFlowCode": "00",
      "parValue": "100.000",
      "name": {
        "en": "IRCISIN00001",
        "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
      },
      "mnemonic": {
        "en": "SPY0",
        "fa": "اس پی وای0"
      },
      "matchingType": "FIFO",
      "issuePrice": "200.000",
      "strikePrice": "200.000",
      "underlyingProductId": "IRCISIN00001",
      "tradingStartDate": "2021-01-01",
      "tradingEndDate": "2021-01-01",
      "maturityDate": "2021-01-01",
      "normalBlockSize": 1000000,
      "isPricePercentage": false
    }
  }
}

########################################################################################################################

#
## Given: no resources exist
#
PUT /products/SPY
Content-Type: application/json

{
  "boardCode": "B0",
  "marketFlowCode": "00",
  "parValue": "100.000",
  "maxOwnershipByShareholder": "0.2",
  "adjustedTotalShares": 20000,
  "name": {
    "en": "IRCISIN00001",
    "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
  },
  "matchingType": "FIFO",
  "issuePrice": "200.000",
  "strikePrice": "200.000",
  "underlyingProductId": "IRCISIN00001",
  "tradingStartDate": "2021-01-01",
  "tradingEndDate": "2021-01-01",
  "maturityDate": "2021-01-01",
  "normalBlockSize": 1000000
}

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Product' not resolvable by 'SPY'"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
PUT /products/IRO1SPY00030/queued-commands/PRODUCT_CREATION/0
Content-Type: application/json

{
  "productId": "IRO1SPY00030",
  "securities": [
    {
      "priceBandPercentage": {
        "lowerBoundPercentage": "89.5",
        "upperBoundPercentage": "100"
      },
      "groupId": "G0",
      "market": "NO"
    }
  ],
  "boardCode": "0",
  "productTypeCode": "O",
  "productSubTypeCode": "110",
  "marketFlowCode": "00",
  "companyCode": "SPY0",
  "parValue": "101.000",
  "referencePrice": "10.000",
  "totalShares": 20000000000000000,
  "name": {
    "en": "IRCISIN00001",
    "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
  },
  "mnemonic": {
    "en": "SPY3",
    "fa": "اس پی وای3"
  },
  "productCode": "SPY000",
  "matchingType": "FIFO",
  "issuePrice": "200.000",
  "strikePrice": "200.000",
  "underlyingProductId": "IRCISIN00001",
  "tradingStartDate": "2021-01-01",
  "tradingEndDate": "2021-01-01",
  "maturityDate": "2021-01-01",
  "normalBlockSize": 1000000,
  "availableForSell": true,
  "isPricePercentage": false
}

###
200
Content-Type: application/json

{
  "status": "IN_QUEUE",
  "adminCommandType": "CREATE_PRODUCT",
  "minorPriority": 0,
  "majorPriority": "PRODUCT_CREATION",
  "request": {
    "productId": "IRO1SPY00030",
    "spec": {
      "productId": "IRO1SPY00030",
      "securities": [
        {
          "priceBandPercentage": {
            "lowerBoundPercentage": "89.5",
            "upperBoundPercentage": "100"
          },
          "groupId": "G0",
          "market": "NO"
        }
      ],
      "boardCode": "0",
      "productTypeCode": "O",
      "productSubTypeCode": "110",
      "marketFlowCode": "00",
      "companyCode": "SPY0",
      "parValue": "101.000",
      "referencePrice": "10.000",
      "totalShares": 20000000000000000,
      "name": {
        "en": "IRCISIN00001",
        "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
      },
      "mnemonic": {
        "en": "SPY3",
        "fa": "اس پی وای3"
      },
      "productCode": "SPY000",
      "matchingType": "FIFO",
      "issuePrice": "200.000",
      "strikePrice": "200.000",
      "underlyingProductId": "IRCISIN00001",
      "tradingStartDate": "2021-01-01",
      "tradingEndDate": "2021-01-01",
      "maturityDate": "2021-01-01",
      "normalBlockSize": 1000000,
      "availableForSell": true,
      "isPricePercentage": false
    }
  },
  "productId": "IRO1SPY00030"
}

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
PUT /products/IRO1SPY00022/queued-commands/PRODUCT_UPDATE/1
Content-Type: application/json

{
  "boardCode": "0",
  "marketFlowCode": "00",
  "parValue": "100.000",
  "adjustedTotalShares": 20000,
  "name": {
    "en": "IRCISIN00002",
    "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
  },
  "mnemonic": {
    "en": "SPY5",
    "fa": "اس پی وای5"
  },
  "matchingType": "FIFO",
  "issuePrice": "200.000",
  "strikePrice": "200.000",
  "underlyingProductId": "IRCISIN00001",
  "tradingStartDate": "2021-01-01",
  "tradingEndDate": "2021-01-01",
  "maturityDate": "2021-01-01",
  "normalBlockSize": 1000000,
  "isPricePercentage": false
}

###
200
Content-Type: application/json

{
  "productId": "IRO1SPY00022",
  "status": "IN_QUEUE",
  "adminCommandType": "UPDATE_PRODUCT",
  "minorPriority": 1,
  "majorPriority": "PRODUCT_UPDATE",
  "request": {
    "productId": "IRO1SPY00022",
    "spec": {
      "boardCode": "0",
      "marketFlowCode": "00",
      "parValue": "100.000",
      "name": {
        "en": "IRCISIN00002",
        "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
      },
      "mnemonic": {
        "en": "SPY5",
        "fa": "اس پی وای5"
      },
      "matchingType": "FIFO",
      "issuePrice": "200.000",
      "strikePrice": "200.000",
      "underlyingProductId": "IRCISIN00001",
      "tradingStartDate": "2021-01-01",
      "tradingEndDate": "2021-01-01",
      "maturityDate": "2021-01-01",
      "normalBlockSize": 1000000,
      "isPricePercentage": false
    }
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
PUT /products/IRO1SPY00022/queued-commands/PRODUCT_UPDATE/5
Content-Type: application/json

{
  "boardCode": "0",
  "marketFlowCode": "00",
  "parValue": "100.000",
  "maxOwnershipByShareholder": "0.2",
  "adjustedTotalShares": 20000,
  "name": {
    "en": "IRCISIN00001",
    "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
  },
  "mnemonic": {
    "en": "SPY0",
    "fa": "اس پی وای0"
  },
  "matchingType": "FIFO",
  "issuePrice": "200.000",
  "strikePrice": "200.000",
  "underlyingProductId": "IRCISIN00001",
  "tradingStartDate": "2021-01-01",
  "tradingEndDate": "2021-01-01",
  "maturityDate": "2021-01-01",
  "normalBlockSize": 1000000,
  "availableForSell": true,
  "isPricePercentage": false
}

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1009",
      "title": "No such element",
      "detail": "'CommandQueueItem' not found"
    }
  ]
}

########################################################################################################################

#
## Given: no resources exist
#

PUT /products/IRO1SPY00022/update-sell-availability
Content-Type: application/json

{
  "availableForSell": true
}

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Product' not resolvable by 'IRO1SPY00022'"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
#

PUT /products/IRO1SPY00006/update-sell-availability
Content-Type: application/json

{
  "availableForSell": true
}

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "productId": "IRO1SPY00006",
    "availableForSell": true
  },
  "status": "AWAITING_RESPONSE",
  "type": "UPDATE_PRODUCT_SELL_AVAILABILITY",
  "sender": "Test User"
}

########################################################################################################################
