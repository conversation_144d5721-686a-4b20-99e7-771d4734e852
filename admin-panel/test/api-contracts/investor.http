#
## Given: no resources exist
#
GET /investors/591542
Accept: application/json

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Investor' not resolvable by '591542'"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /investors/5915420
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "isBuyBlocked": false,
  "isSellBlocked": false,
  "nationality": "NATIONAL",
  "investorType": "INDIVIDUAL",
  "shareholderIds": [
    "1125915420"
  ],
  "deleted": false
}

########################################################################################################################

#
## Given: no resources exist
#
POST /investors/591542/block
Content-Type: application/json

{}

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Investor' not resolvable by '591542'"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
#
POST /investors/5915421/block
Content-Type: application/json

{
  "investorBlockedSides": [
    {
      "side": "BUY",
      "blocked": true
    }
  ]
}

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "investorId": "5915421",
    "investorBlockedSides": [
      {
        "blocked": true,
        "side": "BUY"
      }
    ]
  },
  "status": "AWAITING_RESPONSE",
  "type": "BLOCK_INVESTOR",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there are a lot of resources
#
POST /investors/5915420/block
Content-Type: application/json

{
  "investorBlockedSides": [
    {
      "side": "BUY",
      "blocked": true
    },
    {
      "side": "SELL",
      "blocked": true
    }
  ]
}

###
200
Content-Type: application/json

{
  "commandId": 8,
  "request": {
    "investorId": "5915420",
    "investorBlockedSides": [
      {
        "blocked": true,
        "side": "BUY"
      },
      {
        "blocked": true,
        "side": "SELL"
      }
    ]
  },
  "status": "AWAITING_RESPONSE",
  "type": "BLOCK_INVESTOR",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /investors/5915421/orders

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securities": [],
    "products": [],
    "brokers": [],
    "traders": [],
    "shareholders": [],
    "investors": [
      "5915421"
    ],
    "groups": [],
    "sides": []
  },
  "status": "AWAITING_RESPONSE",
  "type": "PURGE_ORDERS",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
DELETE /investors/5915421/orders?brokers=BRK0|BRK1&products=PRD0|PRD1|PRD2&traders=TRD0|TRD1&securities=SPY|NZQ&groups=GRP0|GRP1&sides=BUY|SELL

Nothing

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securities": [
      "SPY",
      "NZQ"
    ],
    "products": [
      "PRD0",
      "PRD1",
      "PRD2"
    ],
    "brokers": [
      "BRK0",
      "BRK1"
    ],
    "traders": [
      "TRD0",
      "TRD1"
    ],
    "shareholders": [],
    "investors": [
      "5915421"
    ],
    "groups": [
      "GRP0",
      "GRP1"
    ],
    "sides": [
      "BUY",
      "SELL"
    ]
  },
  "status": "AWAITING_RESPONSE",
  "type": "PURGE_ORDERS",
  "sender": "Test User"
}

########################################################################################################################
