#
## Given: no resources exist
#
GET /system/data/save/result
Accept: application/json

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1009",
      "title": "No such element",
      "detail": "'CommandRecord' not found"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /system/data/save/result
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "id": 2,
  "commandRecords": [
    {
      "commandId": 7,
      "status": "AWAITING_RESPONSE",
      "timestamp": "2021-02-21T15:51:20",
      "type": "SAVE_ALL"
    }
  ],
  "startTime": "2021-02-21T15:52:20"
}

########################################################################################################################

#
## Given: no resources exist
## With System State: PREPARE_TO_SHUTDOWN
#
POST /system/data/save
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "commandId": 1,
  "request": {
    "name": "Save All"
  },
  "status": "AWAITING_RESPONSE",
  "type": "SAVE_ALL",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: PREPARE_TO_SHUTDOWN
#
POST /system/data/save
Accept: application/json

Nothing

###
409
Content-Type: application/json

{
  "errors": [
    {
      "type": "1024",
      "title": "Saving command is already executing",
      "detail": "Saving command is already executing"
    }
  ]
}

########################################################################################################################
