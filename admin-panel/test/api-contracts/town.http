#
## Given: no resources exist
#
GET /towns
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 0
  },
  "element": {
    "count": 0,
    "offset": 0,
    "total": 0
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /towns
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "code": "90",
      "name": {
        "en": "Tehran",
        "fa": "تهران"
      }
    },
    {
      "code": "91",
      "name": {
        "en": "Shiraz",
        "fa": "شیراز"
      }
    },
    {
      "code": "92",
      "name": {
        "en": "Mashhad",
        "fa": "مشهد"
      }
    },
    {
      "code": "93",
      "name": {
        "en": "Kerman",
        "fa": "کرمان"
      }
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 4,
    "offset": 0,
    "total": 4
  }
}

########################################################################################################################
#
## Given: no resources exist
#
GET /towns/98
Accept: application/json

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Town' not resolvable by '98'"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /towns/90
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "code": "90",
  "name": {
    "en": "Tehran",
    "fa": "تهران"
  }
}

########################################################################################################################

#
## Given: no resources exist
## With System State: TRADING_SESSION
#
POST /towns
Accept: application/json

{
  "code": "10",
  "name": {
    "en": "Esfahan",
    "fa": "اصفهان"
  }
}

###
200
Content-Type: application/json

{
  "commandId": 1,
  "request": {
    "code": "10",
    "name": {
      "en": "Esfahan",
      "fa": "اصفهان"
    }
  },
  "status": "AWAITING_RESPONSE",
  "type": "CREATE_TOWN",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
PUT /towns/90
Accept: application/json

{
  "name": {
    "en": "tehran",
    "fa": "طهران"
  }
}

###
200
Content-Type: application/json

{
  "commandId": 8,
  "request": {
    "code": "90",
    "name": {
      "en": "tehran",
      "fa": "طهران"
    }
  },
  "status": "AWAITING_RESPONSE",
  "type": "UPDATE_TOWN",
  "sender": "Test User"
}

########################################################################################################################
