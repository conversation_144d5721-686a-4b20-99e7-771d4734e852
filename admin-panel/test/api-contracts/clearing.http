#
## Given: no resources exist
#
GET /clearing/reports
Accept: application/json

Nothing

###
200
Content-Type: application/json

[]

########################################################################################################################
#
## Given: there is a single resource
#
GET /clearing/reports
Accept: application/json

Nothing

###
200
Content-Type: application/json

[
  {
    "fileName": "sample_csd_file_name",
    "totalStatus": "PARTIAL_SUCCESS",
    "successfulZ2Count": 0,
    "successfulU2Count": 0,
    "successfulW2Count": 0,
    "successfulX2Count": 0,
    "failedZ2Count": 0,
    "failedU2Count": 0,
    "failedW2Count": 0,
    "failedX2Count": 0,
    "processingProgress": 0
  }
]


########################################################################################################################
#
## Given: there is a single resource
#
GET /clearing/reports/sample_csd_file_name
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "originalRecord": "W2",
      "message": "INVESTOR_DOES_NOT_EXIST"
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 1,
    "offset": 0,
    "total": 1
  }
}


########################################################################################################################
#
## Given: no resources exist
#
GET /clearing/should-be-uploaded
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "shouldBeUploaded": true
}

########################################################################################################################
#
## Given: there is a single resource
#
GET /clearing/should-be-uploaded
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "shouldBeUploaded": false
}
