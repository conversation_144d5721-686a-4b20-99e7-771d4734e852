#
## Given: no resources exist
#
GET /market-notification-templates?page=0
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 0
  },
  "element": {
    "count": 0,
    "offset": 0,
    "total": 0
  }
}

########################################################################################################################
#
## Given: there are a lot of resources
#
GET /market-notification-templates?page=0
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "title": "templateTitle0",
      "message": "templateMessage0",
      "messageNature": "MARKET",
      "addressType": "BROKERS"
    },
    {
      "title": "templateTitle1",
      "message": "templateMessage1",
      "messageNature": "MARKET",
      "addressType": "BROKERS"
    },
    {
      "title": "templateTitle2",
      "message": "templateMessage2",
      "messageNature": "MARKET",
      "addressType": "BROKERS"
    },
    {
      "title": "templateTitle3",
      "message": "templateMessage3",
      "messageNature": "MARKET",
      "addressType": "BROKERS"
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 4,
    "offset": 0,
    "total": 4
  }
}

########################################################################################################################
#
## Given: there is a single resource
#
GET /market-notification-templates
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "title": "templateTitle",
      "message": "templateMessage",
      "messageNature": "MARKET",
      "addressType": "BROKERS"
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 1,
    "offset": 0,
    "total": 1
  }
}

########################################################################################################################
#
## Given: there is a single resource
#
GET /market-notification-templates/templateTitle
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "title": "templateTitle",
  "message": "templateMessage",
  "messageNature": "MARKET",
  "addressType": "BROKERS"
}

#######################################################################################################################
#
## Given: there is a single resource
#
PUT /market-notification-templates/templateTitle
Accept: application/json

{
    "title": "templateTitle",
    "message": "templateMessageUpdated",
    "messageNature": "MARKET",
    "addressType": "BROKERS"
}

###
200
Content-Type: application/json

{
  "title": "templateTitle",
  "message": "templateMessageUpdated",
  "messageNature": "MARKET",
  "addressType": "BROKERS"
}

#########################################################################################################################
#
## Given: there is a single resource
#
POST /market-notification-templates
Accept: application/json

{
  "title": "templateTitle2",
  "message": "templateMessage",
  "messageNature": "MARKET",
  "addressType": "BROKERS"
}

###
200
Content-Type: application/json

{
  "title": "templateTitle2",
  "message": "templateMessage",
  "messageNature": "MARKET",
  "addressType": "BROKERS"
}

########################################################################################################################
#
## Given: there is a single resource
#
DELETE /market-notification-templates/templateTitle
Accept: application/json

Nothing

###
200
Content-Type: application/json

#########################################################################################################################
#
## Given: there is a single resource
#
POST /market-notification-templates
Accept: application/json

{}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'addressType' must not be null"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'message' field is required"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'messageNature' must not be null"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'title' field is required"
    }
  ]
}


#########################################################################################################################
#
## Given: there is a single resource
#
POST /market-notification-templates
Accept: application/json

{
  "title": "0123456789112345678921234567893123456789412345678951234567896123456789712345678981234567899123456789",
  "message": "templateMessage",
  "messageNature": "MARKET",
  "addressType": "BROKERS"
}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'title' size must be between 1 and 73"
    }
  ]
}

#########################################################################################################################
#
## Given: there is a single resource
#
POST /market-notification-templates
Accept: application/json

{
  "title": "ارزش به ﷼",
  "message": "templateMessage",
  "messageNature": "MARKET",
  "addressType": "BROKERS"
}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "Title is not Windows-1256 compatible."
    }
  ]
}
