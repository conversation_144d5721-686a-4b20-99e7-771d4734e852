#
## Given: no resources exist
#
POST /securities/IRB1RADIN00-/static-threshold/percentage
Accept: application/json

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Security' not resolvable by 'IRB1RADIN00-'"
    }
  ]
}

########################################################################################################################
#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
POST /securities/IRO1SPY00001/static-threshold/percentage
Accept: application/json

{
  "priceBandPercentage": {
    "lowerBoundPercentage": null,
    "upperBoundPercentage": null
  }
}

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "securityId": "IRO1SPY00001"
  },
  "status": "AWAITING_RESPONSE",
  "type": "CHANGE_SECURITY_STATIC_PRICE_BAND_PERCENTAGE",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there is a single resource
#
POST /securities/IRO1SPY00001/static-threshold/percentage
Content-Type: application/json

{
  "priceBandPercentage": {
    "lowerBoundPercentage": "150",
    "upperBoundPercentage": "0.05"
  }
}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'priceBandPercentage.lowerBoundPercentage' must be more than or equal to 0 and less than 100"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
POST /securities/IRO1SPY00001/static-threshold/percentage
Content-Type: application/json

{
  "priceBandPercentage": {
    "lowerBoundPercentage": "0.1",
    "upperBoundPercentage": "120"
  }
}

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "priceBandPercentage": {
      "lowerBoundPercentage": "0.1",
      "upperBoundPercentage": "120"
    },
    "securityId": "IRO1SPY00001"
  },
  "status": "AWAITING_RESPONSE",
  "type": "CHANGE_SECURITY_STATIC_PRICE_BAND_PERCENTAGE",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there are a lot of resources
#
POST /securities/batch-static-threshold/percentage
Accept: application/json

Nothing

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1034",
      "title": "Endpoint requires a File to be uploaded",
      "detail": "Invalid file: Current request is not a multipart request"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
#
POST /securities/IRO1SPY00001/static-threshold/price-band
Content-Type: application/json

{
  "lowerBound": "150",
  "upperBound": "100"
}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "Upper bound price must be greater than lower bound price."
    }
  ]
}

########################################################################################################################
#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
POST /securities/IRO1SPY00001/static-threshold/price-band
Accept: application/json

{}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type":"1020",
      "title":"Endpoint parameter not valid",
      "detail":"'lowerBound' must not be blank"
    },
    {
      "type":"1020",
      "title":"Endpoint parameter not valid",
      "detail":"'upperBound' must not be blank"
    }
  ]
}

########################################################################################################################
#
## Given: there is a single resource
## With System State: TRADING_SESSION
#
POST /securities/IRO1SPY00001/static-threshold/price-band
Content-Type: application/json

{
  "lowerBound": "100",
  "upperBound": "150"
}

###
200
Content-Type: application/json

{
  "commandId": 2,
  "request": {
    "staticPriceBand": {
      "lowerBound": "100",
      "upperBound": "150"
    },
    "securityId": "IRO1SPY00001"
  },
  "status": "AWAITING_RESPONSE",
  "type": "UPDATE_STATIC_PRICE_BAND",
  "sender": "Test User"
}

########################################################################################################################

#
## Given: there are a lot of resources
#
POST /securities/batch-static-threshold/price-band
Accept: application/json

Nothing

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1034",
      "title": "Endpoint requires a File to be uploaded",
      "detail": "Invalid file: Current request is not a multipart request"
    }
  ]
}

########################################################################################################################

#
## Given: no resources exist
#
POST /groups/G-/static-threshold
Accept: application/json

Nothing

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1006",
      "title": "Resource not resolved",
      "detail": "'Group' not resolvable by 'G-'"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
POST /groups/G1/static-threshold
Content-Type: application/json

{
  "lowerBoundPercentage": "0.1",
  "upperBoundPercentage": "0.05"
}

###
200
Content-Type: application/json

{
  "status":"IN_QUEUE",
  "adminCommandType":"CHANGE_GROUP_STATIC_PRICE_BAND",
  "minorPriority":0,
  "majorPriority":"GROUP_PRICE_BAND_UPDATE",
  "request":{
    "groupCode":"G1",
    "priceBandPercentage":{
      "lowerBoundPercentage":"0.1",
      "upperBoundPercentage":"0.05"
    }
  },
  "groupCode":"G1"
}

########################################################################################################################

#
## Given: there is a single resource
#
POST /groups/G0/static-threshold
Content-Type: application/json

{}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'lowerBoundPercentage' must not be blank"
    },
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'upperBoundPercentage' must not be blank"
    }
  ]
}

########################################################################################################################

#
## Given: there is a single resource
#
POST /groups/G0/static-threshold
Content-Type: application/json

{
  "lowerBoundPercentage": "150",
  "upperBoundPercentage": "0.05"
}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'lowerBoundPercentage' must be more than or equal to 0 and less than 100"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
#
POST /groups/batch-static-threshold
Accept: application/json

Nothing

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1034",
      "title": "Endpoint requires a File to be uploaded",
      "detail": "Invalid file: Current request is not a multipart request"
    }
  ]
}

########################################################################################################################
