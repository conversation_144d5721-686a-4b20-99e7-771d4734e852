
#
## Given: no resources exist
#
GET /schedules/session/groups
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 0
  },
  "element": {
    "count": 0,
    "offset": 0,
    "total": 0
  }
}

########################################################################################################################

#
## Given: there is a single resource
#
GET /schedules/session/groups?schedulableId=G0
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "date": "2021-02-21",
      "actions": [
        {
          "runAt": "15:45:20",
          "targetState": "PRE_OPENING",
          "shouldOpen": false
        },
        {
          "runAt": "15:47:20",
          "targetState": "CONTINUOUS_TRADING",
          "shouldOpen": true
        }
      ],
      "group": {
        "code": "G0"
      }
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 1,
    "offset": 0,
    "total": 1
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#

GET /schedules/session/groups?date=2025-01-02..2025-01-05&schedulableId=G0
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "date": "2025-01-02",
      "actions": [
        {
          "runAt": "08:00:00",
          "targetState": "PRE_OPENING",
          "shouldOpen": false
        },
        {
          "runAt": "09:00:00",
          "targetState": "CONTINUOUS_TRADING",
          "shouldOpen": true
        },
        {
          "runAt": "12:00:00",
          "targetState": "SURVEILLANCE",
          "shouldOpen": false
        }
      ],
      "group": {
        "code": "G0"
      }
    },
    {
      "date": "2025-01-03",
      "actions": [
        {
          "runAt": "08:00:00",
          "targetState": "PRE_OPENING",
          "shouldOpen": false
        },
        {
          "runAt": "09:00:00",
          "targetState": "CONTINUOUS_TRADING",
          "shouldOpen": true
        },
        {
          "runAt": "12:00:00",
          "targetState": "SURVEILLANCE",
          "shouldOpen": false
        }
      ],
      "group": {
        "code": "G0"
      }
    },
    {
      "date": "2025-01-04",
      "actions": [
        {
          "runAt": "08:00:00",
          "targetState": "PRE_OPENING",
          "shouldOpen": false
        },
        {
          "runAt": "09:00:00",
          "targetState": "CONTINUOUS_TRADING",
          "shouldOpen": true
        },
        {
          "runAt": "12:00:00",
          "targetState": "SURVEILLANCE",
          "shouldOpen": false
        }
      ],
      "group": {
        "code": "G0"
      }
    },
    {
      "date": "2025-01-05",
      "actions": [
        {
          "runAt": "08:00:00",
          "targetState": "PRE_OPENING",
          "shouldOpen": false
        },
        {
          "runAt": "09:00:00",
          "targetState": "CONTINUOUS_TRADING",
          "shouldOpen": true
        },
        {
          "runAt": "12:00:00",
          "targetState": "SURVEILLANCE",
          "shouldOpen": false
        }
      ],
      "group": {
        "code": "G0"
      }
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 4,
    "offset": 0,
    "total": 4
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /schedules/session/groups?schedulableId=G0&page=0&size=7
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "date": "2021-02-21",
      "actions": [
        {
          "runAt": "15:45:20",
          "targetState": "PRE_OPENING",
          "shouldOpen": false
        },
        {
          "runAt": "15:47:20",
          "targetState": "CONTINUOUS_TRADING",
          "shouldOpen": true
        }
      ],
      "group": {
        "code": "G0"
      }
    },
    {
      "date": "2021-02-20",
      "actions": [
        {
          "runAt": "15:45:20",
          "targetState": "PRE_OPENING",
          "shouldOpen": false
        },
        {
          "runAt": "15:47:20",
          "targetState": "CONTINUOUS_TRADING",
          "shouldOpen": true
        }
      ],
      "group": {
        "code": "G0"
      }
    }
  ],
  "page": {
    "size": 7,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 2,
    "offset": 0,
    "total": 2
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
PUT /schedules/session/groups
Content-Type: application/json

{
  "groupCodes": [
    "G0"
  ],
  "actions": [
    {
      "runAt": "09:05:00",
      "targetState": "CONTINUOUS_TRADING",
      "shouldOpen": true
    },
    {
      "runAt": "08:29:00",
      "targetState": "PRE_OPENING",
      "shouldOpen": false
    }
  ]
}

###
200
Content-Type: application/json

[
  {
    "date": "2021-02-21",
    "actions": [
      {
        "runAt": "08:29:00",
        "targetState": "PRE_OPENING",
        "shouldOpen": false
      },
      {
        "runAt": "09:05:00",
        "targetState": "CONTINUOUS_TRADING",
        "shouldOpen": true
      }
    ],
    "group": {
      "code": "G0"
    }
  }
]

########################################################################################################################

#
## Given: there are a lot of resources
#
PUT /schedules/session/groups
Content-Type: application/json

{}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1020",
      "title": "Endpoint parameter not valid",
      "detail": "'groupCodes' must not be empty"
    }
  ]
}

########################################################################################################################

#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
PUT /schedules/session/groups
Content-Type: application/json

{
  "groupCodes": [
    "G0"
  ]
}

###
200
Content-Type: application/json

[
  {
    "date": "2021-02-21",
    "group": {
      "code": "G0"
    }
  }
]

########################################################################################################################

#
## Given: there are a lot of resources
#
PUT /schedules/session/groups
Content-Type: application/json

{
  "groupCodes": [
    "G3"
  ],
  "actions": [
    {
      "runAt": "08:29:00",
      "targetState": "TRADING_SESSION"
    },
    {
      "runAt": "09:05:00",
      "targetState": "POST_SESSION"
    }
  ]
}

###
400
Content-Type: application/json

{
  "errors": [
    {
      "type": "1005",
      "title": "Unable to assign parameter value",
      "detail": "'actions[0].targetState' not assignable by 'TRADING_SESSION'"
    }
  ]
}

########################################################################################################################


#
## Given: there are a lot of resources
## With System State: TRADING_SESSION
#
PUT /schedules/session/groups
Content-Type: application/json

{
  "groupCodes": [
    "G3"
  ],
  "actions": [
    {
      "runAt": "08:29:00",
      "targetState": "PRE_OPENING",
      "shouldOpen": false
    },
    {
      "runAt": "09:05:00",
      "targetState": "CONTINUOUS_TRADING",
      "shouldOpen": true
    },
    {
      "runAt": "12:35:00",
      "targetState": "FORBIDDEN",
      "shouldOpen": false
    }
  ]
}

###
404
Content-Type: application/json

{
  "errors": [
    {
      "type": "1009",
      "title": "No such element",
      "detail": "'SessionSchedule' not found"
    }
  ]
}

########################################################################################################################
