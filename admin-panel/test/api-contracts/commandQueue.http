#
## Given: no resources exist
#
GET /queued-commands
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 0
  },
  "element": {
    "count": 0,
    "offset": 0,
    "total": 0
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /queued-commands
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "status": "IN_QUEUE",
      "adminCommandType": "CREATE_PRODUCT",
      "minorPriority": 0,
      "majorPriority": "PRODUCT_CREATION",
      "request": {
        "productId": "IRO1SPY00030",
        "spec": {
          "productId": "IRO1SPY00030",
          "securities": [
            {
              "priceBandPercentage": {
                "lowerBoundPercentage": "89.5",
                "upperBoundPercentage": "100"
              },
              "groupId": "G0",
              "market": "NO"
            }
          ],
          "boardCode": "0",
          "productTypeCode": "O",
          "productSubTypeCode": "110",
          "marketFlowCode": "00",
          "companyCode": "SPY0",
          "parValue": "1000.000",
          "referencePrice": "10000",
          "totalShares": 900000000000,
          "name": {
            "en": "SPDR S&P 500 Trust",
            "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
          },
          "mnemonic": {
            "en": "SPY3",
            "fa": "اس پی وای3"
          },
          "productCode": "SPY003",
          "matchingType": "FIFO",
          "issuePrice": "200.000",
          "strikePrice": "200.000",
          "underlyingProductId": "IRO1SPY00006",
          "tradingStartDate": "2000-01-01",
          "tradingEndDate": "2000-01-01",
          "maturityDate": "2000-01-01",
          "normalBlockSize": 1,
          "availableForSell": true,
          "isPricePercentage": false
        }
      },
      "productId": "IRO1SPY00030"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "ADD_SECURITY",
      "minorPriority": 0,
      "majorPriority": "SECURITY_CREATION",
      "request": {
        "securityId": "IRO1SPY00004",
        "productId": "IRO1SPY00006",
        "mnemonic": {
          "en": "SPY04",
          "fa": "اس پی وای04"
        },
        "securitySpec": {
          "groupId": "G0",
          "settlementDelay": 2,
          "market": "BK",
          "baseVolume": 1000000
        },
        "orderSpec": {
          "priceTick": "1000",
          "lotSize": 1000,
          "minQuantity": 100,
          "maxBuyQuantity": 100000,
          "maxSellQuantity": 4000
        },
        "isShortSellAllowed": false,
        "groupCode": "G0"
      },
      "securityId": "IRO1SPY00004"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "ADD_SECURITY",
      "minorPriority": 0,
      "majorPriority": "SECURITY_CREATION",
      "request": {
        "securityId": "IRO1SPY00031",
        "productId": "IRO1SPY00030",
        "mnemonic": {
          "en": "SPY31",
          "fa": "اس پی وای31"
        },
        "securitySpec": {
          "groupId": "G0",
          "settlementDelay": 2,
          "market": "NO",
          "baseVolume": 1000000
        },
        "orderSpec": {
          "priceTick": "1000",
          "lotSize": 1000,
          "minQuantity": 100,
          "maxBuyQuantity": 100000,
          "maxSellQuantity": 4000
        },
        "isShortSellAllowed": false,
        "groupCode": "G0"
      },
      "securityId": "IRO1SPY00031"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "CHANGE_GROUP_STATIC_PRICE_BAND",
      "minorPriority": 0,
      "majorPriority": "GROUP_PRICE_BAND_UPDATE",
      "request": {
        "groupCode": "G0",
        "priceBandPercentage": {
          "lowerBoundPercentage": "89.5",
          "upperBoundPercentage": "100"
        }
      },
      "groupCode": "G0"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "UPDATE_PRODUCT",
      "minorPriority": 1,
      "majorPriority": "PRODUCT_UPDATE",
      "request": {
        "productId": "IRO1SPY00022",
        "spec": {
          "boardCode": "0",
          "marketFlowCode": "00",
          "parValue": "1000.000",
          "name": {
            "en": "SPDR S&P 500 Trust",
            "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
          },
          "mnemonic": {
            "en": "SPY0",
            "fa": "اس پی وای0"
          },
          "matchingType": "FIFO",
          "issuePrice": "200.000",
          "strikePrice": "200.000",
          "underlyingProductId": "IRO1SPY00006",
          "tradingStartDate": "2000-01-01",
          "tradingEndDate": "2000-01-01",
          "maturityDate": "2000-01-01",
          "normalBlockSize": 1,
          "isPricePercentage": false
        }
      },
      "productId": "IRO1SPY00022"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "UPDATE_SECURITY_SPECIFICATION",
      "minorPriority": 0,
      "majorPriority": "SECURITY_UPDATE",
      "request": {
        "securityId": "IRO1SPY00003",
        "updateSpec": {
          "settlementDelay": 3,
          "baseVolume": 1000000,
          "orderSpec": {
            "priceTick": "1000",
            "lotSize": 1000,
            "minQuantity": 100,
            "maxBuyQuantity": 100000,
            "maxSellQuantity": 4000
          },
          "isShortSellAllowed": false
        }
      },
      "securityId": "IRO1SPY00003"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "DIVIDENDS_CORPORATE_ACTION",
      "minorPriority": 1,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "value": 1200.0,
          "isPercentage": false
        },
        "productId": "IRO1SPY00006"
      },
      "productId": "IRO1SPY00006"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "BONUS_CORPORATE_ACTION",
      "minorPriority": 2,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "offeredShares": 1200,
          "heldShares": 1200
        },
        "productId": "IRO1SPY00006"
      },
      "productId": "IRO1SPY00006"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "RIGHTS_CORPORATE_ACTION",
      "minorPriority": 3,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "offeredShares": 1200,
          "heldShares": 200,
          "discountedPrice": 3000.0
        },
        "productId": "IRO1SPY00006"
      },
      "productId": "IRO1SPY00006"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "BONUS_RIGHTS_CORPORATE_ACTION",
      "minorPriority": 4,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "rightsInputs": {
            "offeredShares": 1200,
            "heldShares": 200,
            "discountedPrice": 3000.0
          },
          "bonusInputs": {
            "offeredShares": 1200,
            "heldShares": 1200
          }
        },
        "productId": "IRO1SPY00006"
      },
      "productId": "IRO1SPY00006"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "CUSTOM_CORPORATE_ACTION",
      "minorPriority": 5,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "newTotalShareNumbers": 1200,
          "newClosingPrice": 3000.0
        },
        "productId": "IRO1SPY00006"
      },
      "productId": "IRO1SPY00006"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "DIVIDENDS_CORPORATE_ACTION",
      "minorPriority": 1,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "value": 1200.0,
          "isPercentage": false
        },
        "productId": "IRO1SPY00022"
      },
      "productId": "IRO1SPY00022"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "BONUS_CORPORATE_ACTION",
      "minorPriority": 2,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "offeredShares": 1200,
          "heldShares": 1200
        },
        "productId": "IRO1SPY00022"
      },
      "productId": "IRO1SPY00022"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "RIGHTS_CORPORATE_ACTION",
      "minorPriority": 3,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "offeredShares": 1200,
          "heldShares": 200,
          "discountedPrice": 3000.0
        },
        "productId": "IRO1SPY00022"
      },
      "productId": "IRO1SPY00022"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "BONUS_RIGHTS_CORPORATE_ACTION",
      "minorPriority": 4,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "rightsInputs": {
            "offeredShares": 1200,
            "heldShares": 200,
            "discountedPrice": 3000.0
          },
          "bonusInputs": {
            "offeredShares": 1200,
            "heldShares": 1200
          }
        },
        "productId": "IRO1SPY00022"
      },
      "productId": "IRO1SPY00022"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "CUSTOM_CORPORATE_ACTION",
      "minorPriority": 5,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "newTotalShareNumbers": 1200,
          "newClosingPrice": 3000.0
        },
        "productId": "IRO1SPY00022"
      },
      "productId": "IRO1SPY00022"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "PURGE_SECURITY",
      "minorPriority": 0,
      "majorPriority": "SECURITY_DELETION",
      "request": {
        "securityId": "IRO1SPY00011"
      },
      "securityId": "IRO1SPY00011"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "PURGE_SECURITY",
      "minorPriority": 0,
      "majorPriority": "SECURITY_DELETION",
      "request": {
        "securityId": "IRO1SPY00021"
      },
      "securityId": "IRO1SPY00021"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "DELETE_PRODUCT",
      "minorPriority": 0,
      "majorPriority": "PRODUCT_DELETION",
      "request": {
        "productId": "IRO1SPY00014"
      },
      "productId": "IRO1SPY00014"
    },
    {
      "status": "SENT",
      "adminCommandType": "UPDATE_PRODUCT",
      "minorPriority": 0,
      "majorPriority": "PRODUCT_UPDATE",
      "request": {
        "productId": "IRO1SPY00022",
        "spec": {
          "boardCode": "0",
          "marketFlowCode": "00",
          "parValue": "1000.000",
          "name": {
            "en": "SPDR S&P 500 Trust",
            "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
          },
          "mnemonic": {
            "en": "SPY0",
            "fa": "اس پی وای0"
          },
          "matchingType": "FIFO",
          "issuePrice": "200.000",
          "strikePrice": "200.000",
          "underlyingProductId": "IRO1SPY00006",
          "tradingStartDate": "2000-01-01",
          "tradingEndDate": "2000-01-01",
          "maturityDate": "2000-01-01",
          "normalBlockSize": 1,
          "isPricePercentage": false
        }
      },
      "productId": "IRO1SPY00022"
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 20,
    "offset": 0,
    "total": 20
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /queued-commands?status=IN_QUEUE
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "status": "IN_QUEUE",
      "adminCommandType": "CREATE_PRODUCT",
      "minorPriority": 0,
      "majorPriority": "PRODUCT_CREATION",
      "request": {
        "productId": "IRO1SPY00030",
        "spec": {
          "productId": "IRO1SPY00030",
          "securities": [
            {
              "priceBandPercentage": {
                "lowerBoundPercentage": "89.5",
                "upperBoundPercentage": "100"
              },
              "groupId": "G0",
              "market": "NO"
            }
          ],
          "boardCode": "0",
          "productTypeCode": "O",
          "productSubTypeCode": "110",
          "marketFlowCode": "00",
          "companyCode": "SPY0",
          "parValue": "1000.000",
          "referencePrice": "10000",
          "totalShares": 900000000000,
          "name": {
            "en": "SPDR S&P 500 Trust",
            "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
          },
          "mnemonic": {
            "en": "SPY3",
            "fa": "اس پی وای3"
          },
          "productCode": "SPY003",
          "matchingType": "FIFO",
          "issuePrice": "200.000",
          "strikePrice": "200.000",
          "underlyingProductId": "IRO1SPY00006",
          "tradingStartDate": "2000-01-01",
          "tradingEndDate": "2000-01-01",
          "maturityDate": "2000-01-01",
          "normalBlockSize": 1,
          "availableForSell": true,
          "isPricePercentage": false
        }
      },
      "productId": "IRO1SPY00030"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "ADD_SECURITY",
      "minorPriority": 0,
      "majorPriority": "SECURITY_CREATION",
      "request": {
        "securityId": "IRO1SPY00004",
        "productId": "IRO1SPY00006",
        "mnemonic": {
          "en": "SPY04",
          "fa": "اس پی وای04"
        },
        "securitySpec": {
          "groupId": "G0",
          "settlementDelay": 2,
          "market": "BK",
          "baseVolume": 1000000
        },
        "orderSpec": {
          "priceTick": "1000",
          "lotSize": 1000,
          "minQuantity": 100,
          "maxBuyQuantity": 100000,
          "maxSellQuantity": 4000
        },
        "isShortSellAllowed": false,
        "groupCode": "G0"
      },
      "securityId": "IRO1SPY00004"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "ADD_SECURITY",
      "minorPriority": 0,
      "majorPriority": "SECURITY_CREATION",
      "request": {
        "securityId": "IRO1SPY00031",
        "productId": "IRO1SPY00030",
        "mnemonic": {
          "en": "SPY31",
          "fa": "اس پی وای31"
        },
        "securitySpec": {
          "groupId": "G0",
          "settlementDelay": 2,
          "market": "NO",
          "baseVolume": 1000000
        },
        "orderSpec": {
          "priceTick": "1000",
          "lotSize": 1000,
          "minQuantity": 100,
          "maxBuyQuantity": 100000,
          "maxSellQuantity": 4000
        },
        "isShortSellAllowed": false,
        "groupCode": "G0"
      },
      "securityId": "IRO1SPY00031"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "CHANGE_GROUP_STATIC_PRICE_BAND",
      "minorPriority": 0,
      "majorPriority": "GROUP_PRICE_BAND_UPDATE",
      "request": {
        "groupCode": "G0",
        "priceBandPercentage": {
          "lowerBoundPercentage": "89.5",
          "upperBoundPercentage": "100"
        }
      },
      "groupCode": "G0"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "UPDATE_PRODUCT",
      "minorPriority": 1,
      "majorPriority": "PRODUCT_UPDATE",
      "request": {
        "productId": "IRO1SPY00022",
        "spec": {
          "boardCode": "0",
          "marketFlowCode": "00",
          "parValue": "1000.000",
          "name": {
            "en": "SPDR S&P 500 Trust",
            "fa": "اس.پی.دی.آر اس.اند.پی ۵۰۰تراست"
          },
          "mnemonic": {
            "en": "SPY0",
            "fa": "اس پی وای0"
          },
          "matchingType": "FIFO",
          "issuePrice": "200.000",
          "strikePrice": "200.000",
          "underlyingProductId": "IRO1SPY00006",
          "tradingStartDate": "2000-01-01",
          "tradingEndDate": "2000-01-01",
          "maturityDate": "2000-01-01",
          "normalBlockSize": 1,
          "isPricePercentage": false
        }
      },
      "productId": "IRO1SPY00022"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "UPDATE_SECURITY_SPECIFICATION",
      "minorPriority": 0,
      "majorPriority": "SECURITY_UPDATE",
      "request": {
        "securityId": "IRO1SPY00003",
        "updateSpec": {
          "settlementDelay": 3,
          "baseVolume": 1000000,
          "orderSpec": {
            "priceTick": "1000",
            "lotSize": 1000,
            "minQuantity": 100,
            "maxBuyQuantity": 100000,
            "maxSellQuantity": 4000
          },
          "isShortSellAllowed": false
        }
      },
      "securityId": "IRO1SPY00003"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "DIVIDENDS_CORPORATE_ACTION",
      "minorPriority": 1,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "value": 1200.0,
          "isPercentage": false
        },
        "productId": "IRO1SPY00006"
      },
      "productId": "IRO1SPY00006"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "BONUS_CORPORATE_ACTION",
      "minorPriority": 2,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "offeredShares": 1200,
          "heldShares": 1200
        },
        "productId": "IRO1SPY00006"
      },
      "productId": "IRO1SPY00006"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "RIGHTS_CORPORATE_ACTION",
      "minorPriority": 3,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "offeredShares": 1200,
          "heldShares": 200,
          "discountedPrice": 3000.0
        },
        "productId": "IRO1SPY00006"
      },
      "productId": "IRO1SPY00006"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "BONUS_RIGHTS_CORPORATE_ACTION",
      "minorPriority": 4,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "rightsInputs": {
            "offeredShares": 1200,
            "heldShares": 200,
            "discountedPrice": 3000.0
          },
          "bonusInputs": {
            "offeredShares": 1200,
            "heldShares": 1200
          }
        },
        "productId": "IRO1SPY00006"
      },
      "productId": "IRO1SPY00006"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "CUSTOM_CORPORATE_ACTION",
      "minorPriority": 5,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "newTotalShareNumbers": 1200,
          "newClosingPrice": 3000.0
        },
        "productId": "IRO1SPY00006"
      },
      "productId": "IRO1SPY00006"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "DIVIDENDS_CORPORATE_ACTION",
      "minorPriority": 1,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "value": 1200.0,
          "isPercentage": false
        },
        "productId": "IRO1SPY00022"
      },
      "productId": "IRO1SPY00022"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "BONUS_CORPORATE_ACTION",
      "minorPriority": 2,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "offeredShares": 1200,
          "heldShares": 1200
        },
        "productId": "IRO1SPY00022"
      },
      "productId": "IRO1SPY00022"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "RIGHTS_CORPORATE_ACTION",
      "minorPriority": 3,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "offeredShares": 1200,
          "heldShares": 200,
          "discountedPrice": 3000.0
        },
        "productId": "IRO1SPY00022"
      },
      "productId": "IRO1SPY00022"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "BONUS_RIGHTS_CORPORATE_ACTION",
      "minorPriority": 4,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "rightsInputs": {
            "offeredShares": 1200,
            "heldShares": 200,
            "discountedPrice": 3000.0
          },
          "bonusInputs": {
            "offeredShares": 1200,
            "heldShares": 1200
          }
        },
        "productId": "IRO1SPY00022"
      },
      "productId": "IRO1SPY00022"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "CUSTOM_CORPORATE_ACTION",
      "minorPriority": 5,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "newTotalShareNumbers": 1200,
          "newClosingPrice": 3000.0
        },
        "productId": "IRO1SPY00022"
      },
      "productId": "IRO1SPY00022"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "PURGE_SECURITY",
      "minorPriority": 0,
      "majorPriority": "SECURITY_DELETION",
      "request": {
        "securityId": "IRO1SPY00011"
      },
      "securityId": "IRO1SPY00011"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "PURGE_SECURITY",
      "minorPriority": 0,
      "majorPriority": "SECURITY_DELETION",
      "request": {
        "securityId": "IRO1SPY00021"
      },
      "securityId": "IRO1SPY00021"
    },
    {
      "status": "IN_QUEUE",
      "adminCommandType": "DELETE_PRODUCT",
      "minorPriority": 0,
      "majorPriority": "PRODUCT_DELETION",
      "request": {
        "productId": "IRO1SPY00014"
      },
      "productId": "IRO1SPY00014"
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 19,
    "offset": 0,
    "total": 19
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /queued-commands?entityId=IRO1SPY00004
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "status": "IN_QUEUE",
      "adminCommandType": "ADD_SECURITY",
      "minorPriority": 0,
      "majorPriority": "SECURITY_CREATION",
      "request": {
        "securityId": "IRO1SPY00004",
        "productId": "IRO1SPY00006",
        "mnemonic": {
          "en": "SPY04",
          "fa": "اس پی وای04"
        },
        "securitySpec": {
          "groupId": "G0",
          "settlementDelay": 2,
          "market": "BK",
          "baseVolume": 1000000
        },
        "orderSpec": {
          "priceTick": "1000",
          "lotSize": 1000,
          "minQuantity": 100,
          "maxBuyQuantity": 100000,
          "maxSellQuantity": 4000
        },
        "isShortSellAllowed": false,
        "groupCode": "G0"
      },
      "securityId": "IRO1SPY00004"
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 1,
    "offset": 0,
    "total": 1
  }
}

########################################################################################################################

#
## Given: there are a lot of resources
#
GET /queued-commands?adminCommandType=DIVIDENDS_CORPORATE_ACTION
Accept: application/json

Nothing

###
200
Content-Type: application/json

{
  "content": [
    {
      "productId": "IRO1SPY00006",
      "status": "IN_QUEUE",
      "adminCommandType": "DIVIDENDS_CORPORATE_ACTION",
      "minorPriority": 1,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "value": 1200.0,
          "isPercentage": false
        },
        "productId": "IRO1SPY00006"
      }
    },
    {
      "productId": "IRO1SPY00022",
      "status": "IN_QUEUE",
      "adminCommandType": "DIVIDENDS_CORPORATE_ACTION",
      "minorPriority": 1,
      "majorPriority": "CORPORATE_ACTION",
      "request": {
        "inputs": {
          "value": 1200.0,
          "isPercentage": false
        },
        "productId": "IRO1SPY00022"
      }
    }
  ],
  "page": {
    "size": 10000,
    "number": 0,
    "total": 1
  },
  "element": {
    "count": 2,
    "offset": 0,
    "total": 2
  }
}
