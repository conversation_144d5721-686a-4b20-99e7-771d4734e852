{"content": [{"timestamp": "2019-10-28T14:20:55.011624722", "internalEvent": {"securityId": "IRO1KVIR0001", "oldState": "FROZEN", "newState": "RESERVED"}, "internalEventType": "AUTOMATIC_SECURITY_STATE_CHANGE"}, {"timestamp": "2019-10-28T14:20:55.009307003", "internalEvent": {"securityId": "IRO1KVIR0001", "oldState": "OPENED", "newState": "FROZEN"}, "internalEventType": "AUTOMATIC_SECURITY_STATE_CHANGE"}, {"timestamp": "2019-10-28T14:16:00.116681203", "internalEvent": {"commandStatus": "SUCCESS", "scheduledActionDescription": "Scheduled action for System to transition to state: TRADING_SESSION"}, "internalEventType": "SCHEDULED_STATE_CHANGE"}, {"timestamp": "2019-10-28T14:15:35.122284149", "internalEvent": {"commandStatus": "SUCCESS", "scheduledActionDescription": "Scheduled action for Security with id: IRO2X57T8091 to transition to state: SURVEILLANCE"}, "internalEventType": "SCHEDULED_STATE_CHANGE"}, {"timestamp": "2019-10-28T14:15:00.121148379", "internalEvent": {"commandStatus": "SUCCESS", "scheduledActionDescription": "Scheduled action for System to transition to state: TRADING_SESSION"}, "internalEventType": "SCHEDULED_STATE_CHANGE"}, {"timestamp": "2019-10-28T14:07:00.121710713", "internalEvent": {"commandStatus": "SUCCESS", "scheduledActionDescription": "Scheduled action for Group with id: 31 to transition to state: SURVEILLANCE"}, "internalEventType": "SCHEDULED_STATE_CHANGE"}], "page": {"size": 10000, "number": 0, "total": 1}, "element": {"count": 6, "offset": 0, "total": 6}}