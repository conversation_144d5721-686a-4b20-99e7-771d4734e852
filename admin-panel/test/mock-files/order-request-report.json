{"content": [{"id": 907907414857089000, "internalSubscriberReference": "GATEWAY00000000=", "accountId": "UdssVmWpEe6", "state": "ALREADY_IN_QUEUE", "securityId": "IRO1KVIR0001", "type": "LIMIT", "side": "BUY", "sequenceId": 2019102700000000000, "priorityDateTime": "2019-10-27T08:00:00.00001", "price": "992000.000", "quantity": 1000, "validityQualifier": "GOOD_TILL_CANCEL", "brokerId": "189", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "", "brokerOrderEntryDateTime": "2019-10-27T11:47:21", "brokerBusinessIdentificationCode": {"bankCode": "138", "countryCode": "IR", "townCode": "23", "branchCode": "001"}, "freeText": "", "giveUpBrokerId": "126"}}, {"id": 907907414857154600, "internalSubscriberReference": "GATEWAY00000000=", "accountId": "UdssVmWpEe6", "state": "ALREADY_IN_QUEUE", "securityId": "IRO1KVIR0001", "type": "LIMIT", "side": "BUY", "sequenceId": 2019102700000000000, "priorityDateTime": "2019-10-27T08:00:00.000016", "price": "990000.000", "quantity": 10000, "validityQualifier": "GOOD_TILL_CANCEL", "brokerId": "189", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "", "brokerOrderEntryDateTime": "2019-10-27T13:51:03", "brokerBusinessIdentificationCode": {"bankCode": "152", "countryCode": "IR", "townCode": "23", "branchCode": "001"}, "freeText": "", "giveUpBrokerId": "126"}}, {"id": 907907414857220100, "internalSubscriberReference": "GATEWAY00000000=", "accountId": "UdssVmWpEe6", "state": "ALREADY_IN_QUEUE", "securityId": "IRO1KVIR0001", "type": "LIMIT", "side": "BUY", "sequenceId": 2019102700000000000, "priorityDateTime": "2019-10-27T08:00:00.000017", "price": "990000.000", "quantity": 5000, "validityQualifier": "GOOD_TILL_CANCEL", "brokerId": "189", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "", "brokerOrderEntryDateTime": "2019-10-27T13:51:06", "brokerBusinessIdentificationCode": {"bankCode": "152", "countryCode": "IR", "townCode": "23", "branchCode": "001"}, "freeText": "", "giveUpBrokerId": "126"}}, {"id": 907907414857285600, "internalSubscriberReference": "GATEWAY00000000=", "accountId": "UdssVmWpEe6", "state": "ALREADY_IN_QUEUE", "securityId": "IRO1KVIR0001", "type": "LIMIT", "side": "BUY", "sequenceId": 2019102700000000000, "priorityDateTime": "2019-10-27T08:00:00.000019", "price": "985000.000", "quantity": 10000, "validityQualifier": "GOOD_TILL_CANCEL", "brokerId": "189", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "", "brokerOrderEntryDateTime": "2019-10-27T12:31:22", "brokerBusinessIdentificationCode": {"bankCode": "776", "countryCode": "IR", "townCode": "23", "branchCode": "001"}, "freeText": "", "giveUpBrokerId": "126"}}, {"id": 907907414857351200, "internalSubscriberReference": "GATEWAY00000000=", "accountId": "UdssVmWpEe6", "state": "ALREADY_IN_QUEUE", "securityId": "IRO1KVIR0001", "type": "LIMIT", "side": "BUY", "sequenceId": 2019102700000000000, "priorityDateTime": "2019-10-27T08:00:00.000013", "price": "820001.000", "quantity": 1000, "validityQualifier": "GOOD_TILL_CANCEL", "brokerId": "189", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "", "brokerOrderEntryDateTime": "2019-10-27T11:22:40", "brokerBusinessIdentificationCode": {"bankCode": "172", "countryCode": "IR", "townCode": "23", "branchCode": "001"}, "freeText": "", "giveUpBrokerId": "126"}}, {"id": 907907414857416700, "internalSubscriberReference": "GATEWAY00000000=", "accountId": "UdssVmWpEe6", "state": "ALREADY_IN_QUEUE", "securityId": "IRO1KVIR0001", "type": "LIMIT", "side": "BUY", "sequenceId": 2019102700000000000, "priorityDateTime": "2019-10-27T08:00:00.000012", "price": "820000.000", "quantity": 5000, "validityQualifier": "GOOD_TILL_CANCEL", "brokerId": "189", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "", "brokerOrderEntryDateTime": "2019-10-27T11:22:40", "brokerBusinessIdentificationCode": {"bankCode": "442", "countryCode": "IR", "townCode": "23", "branchCode": "001"}, "freeText": "", "giveUpBrokerId": "126"}}, {"id": 907907414857482200, "internalSubscriberReference": "GATEWAY00000000=", "accountId": "UdssVmWpEe6", "state": "ALREADY_IN_QUEUE", "securityId": "IRO1KVIR0001", "type": "LIMIT", "side": "BUY", "sequenceId": 2019102700000000000, "priorityDateTime": "2019-10-27T08:00:00.000018", "price": "965392.000", "quantity": 7500, "validityQualifier": "GOOD_TILL_CANCEL", "brokerId": "189", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "", "brokerOrderEntryDateTime": "2019-10-27T09:26:49", "brokerBusinessIdentificationCode": {"bankCode": "121", "countryCode": "IR", "townCode": "23", "branchCode": "001"}, "freeText": "", "giveUpBrokerId": "126"}}, {"id": 907907414857547800, "internalSubscriberReference": "GATEWAY00000000=", "accountId": "UdssVmWpEe6", "state": "ALREADY_IN_QUEUE", "securityId": "IRO1KVIR0001", "type": "LIMIT", "side": "BUY", "sequenceId": 2019102700000000000, "priorityDateTime": "2019-10-27T08:00:00.000011", "price": "819019.000", "quantity": 500, "validityQualifier": "GOOD_TILL_CANCEL", "brokerId": "189", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "", "brokerOrderEntryDateTime": "2019-10-27T09:27:24", "brokerBusinessIdentificationCode": {"bankCode": "442", "countryCode": "IR", "townCode": "98", "branchCode": "001"}, "freeText": "", "giveUpBrokerId": "126"}}, {"id": 907907414857613300, "internalSubscriberReference": "GATEWAY00000000=", "accountId": "UdssVmWpEe6", "state": "ALREADY_IN_QUEUE", "securityId": "IRO1KVIR0001", "type": "LIMIT", "side": "SELL", "sequenceId": 2019102700000000000, "priorityDateTime": "2019-10-27T08:00:00.000001", "price": "1000000.000", "quantity": 800, "validityQualifier": "GOOD_TILL_CANCEL", "brokerId": "189", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "", "brokerOrderEntryDateTime": "2019-10-27T09:34:56", "brokerBusinessIdentificationCode": {"bankCode": "112", "countryCode": "IR", "townCode": "98", "branchCode": "001"}, "freeText": "", "giveUpBrokerId": "126"}}, {"id": 907907414857678800, "internalSubscriberReference": "GATEWAY00000000=", "accountId": "UdssVmWpEe6", "state": "ALREADY_IN_QUEUE", "securityId": "IRO1KVIR0001", "type": "LIMIT", "side": "SELL", "sequenceId": 2019102700000000000, "priorityDateTime": "2019-10-27T08:00:00.000002", "price": "1050000.000", "quantity": 6, "validityQualifier": "GOOD_TILL_CANCEL", "brokerId": "189", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "", "brokerOrderEntryDateTime": "2019-10-27T09:02:10", "brokerBusinessIdentificationCode": {"bankCode": "187", "countryCode": "IR", "townCode": "98", "branchCode": "001"}, "freeText": "", "giveUpBrokerId": "126"}}, {"id": 907907414857744400, "internalSubscriberReference": "GATEWAY00000000=", "accountId": "UdssVmWpEe6", "state": "ALREADY_IN_QUEUE", "securityId": "IRO1KVIR0001", "type": "LIMIT", "side": "SELL", "sequenceId": 2019102700000000000, "priorityDateTime": "2019-10-27T08:00:00.000007", "price": "1005000.000", "quantity": 5681, "validityQualifier": "GOOD_TILL_CANCEL", "brokerId": "189", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "", "brokerOrderEntryDateTime": "2019-10-27T11:11:22", "brokerBusinessIdentificationCode": {"bankCode": "150", "countryCode": "IR", "townCode": "23", "branchCode": "001"}, "freeText": "", "giveUpBrokerId": "126"}}, {"id": 907907414857809900, "internalSubscriberReference": "GATEWAY00000000=", "accountId": "UdssVmWpEe6", "state": "ALREADY_IN_QUEUE", "securityId": "IRO1KVIR0001", "type": "LIMIT", "side": "SELL", "sequenceId": 2019102700000000000, "priorityDateTime": "2019-10-27T08:00:00.000004", "price": "1050000.000", "quantity": 270, "validityQualifier": "GOOD_TILL_CANCEL", "brokerId": "189", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "", "brokerOrderEntryDateTime": "2019-10-27T11:37:56", "brokerBusinessIdentificationCode": {"bankCode": "162", "countryCode": "IR", "townCode": "98", "branchCode": "001"}, "freeText": "", "giveUpBrokerId": "126"}}, {"id": 907907414857875500, "internalSubscriberReference": "GATEWAY00000000=", "accountId": "UdssVmWpEe6", "state": "ALREADY_IN_QUEUE", "securityId": "IRO1KVIR0001", "type": "LIMIT", "side": "SELL", "sequenceId": 2019102700000000000, "priorityDateTime": "2019-10-27T08:00:00.000005", "price": "1100000.000", "quantity": 3000, "validityQualifier": "GOOD_TILL_CANCEL", "brokerId": "189", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "", "brokerOrderEntryDateTime": "2019-10-27T09:02:03", "brokerBusinessIdentificationCode": {"bankCode": "152", "countryCode": "IR", "townCode": "23", "branchCode": "001"}, "freeText": "", "giveUpBrokerId": "126"}}, {"id": 907907414857941000, "internalSubscriberReference": "GATEWAY00000000=", "accountId": "UdssVmWpEe6", "state": "ALREADY_IN_QUEUE", "securityId": "IRO1KVIR0001", "type": "LIMIT", "side": "SELL", "sequenceId": 2019102700000000000, "priorityDateTime": "2019-10-27T08:00:00.000003", "price": "1050000.000", "quantity": 150, "validityQualifier": "GOOD_TILL_CANCEL", "brokerId": "189", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "", "brokerOrderEntryDateTime": "2019-10-27T09:31:34", "brokerBusinessIdentificationCode": {"bankCode": "187", "countryCode": "IR", "townCode": "98", "branchCode": "001"}, "freeText": "", "giveUpBrokerId": "126"}}, {"id": 907907414858006500, "internalSubscriberReference": "GATEWAY00000000=", "accountId": "UdssVmWpEe6", "state": "ALREADY_IN_QUEUE", "securityId": "IRO1KVIR0001", "type": "LIMIT", "side": "SELL", "sequenceId": 2019102700000000000, "priorityDateTime": "2019-10-27T08:00:00.000006", "price": "1020000.000", "quantity": 1400, "validityQualifier": "GOOD_TILL_CANCEL", "brokerId": "189", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "", "brokerOrderEntryDateTime": "2019-10-27T13:52:51", "brokerBusinessIdentificationCode": {"bankCode": "442", "countryCode": "IR", "townCode": "23", "branchCode": "001"}, "freeText": "", "giveUpBrokerId": "126"}}, {"id": 907907414858072000, "internalSubscriberReference": "GATEWAY00000000=", "accountId": "UdssVmWpEe6", "state": "ALREADY_IN_QUEUE", "securityId": "IRO1KVIR0001", "type": "LIMIT", "side": "SELL", "sequenceId": 2019102700000000000, "priorityDateTime": "2019-10-27T08:00:00.000008", "price": "1484848.000", "quantity": 57, "validityQualifier": "GOOD_TILL_CANCEL", "brokerId": "189", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "", "brokerOrderEntryDateTime": "2019-10-27T11:38:52", "brokerBusinessIdentificationCode": {"bankCode": "112", "countryCode": "IR", "townCode": "98", "branchCode": "001"}, "freeText": "", "giveUpBrokerId": "126"}}, {"id": 907907414858137600, "internalSubscriberReference": "GATEWAY00000000=", "accountId": "UdssVmWpEe6", "state": "ALREADY_IN_QUEUE", "securityId": "IRO1KVIR0001", "type": "LIMIT", "side": "SELL", "sequenceId": 2019102700000000000, "priorityDateTime": "2019-10-27T08:00:00.000009", "price": "1121400.000", "quantity": 100, "validityQualifier": "GOOD_TILL_CANCEL", "brokerId": "189", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "", "brokerOrderEntryDateTime": "2019-10-27T08:50:48", "brokerBusinessIdentificationCode": {"bankCode": "405", "countryCode": "IR", "townCode": "98", "branchCode": "001"}, "freeText": "", "giveUpBrokerId": "126"}}, {"id": 907907414858203100, "internalSubscriberReference": "769RE80004821410", "accountId": "API-GATEWAY", "state": "ADDED_IN_QUEUE", "securityId": "IRO1PSHZ0001", "type": "LIMIT", "side": "SELL", "sequenceId": 2019102800000000000, "priorityDateTime": "2019-10-28T11:50:17.71", "price": "2.000", "quantity": 100, "validityQualifier": "DAY", "brokerId": "769", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "1", "brokerOrderEntryDateTime": "2019-10-28T08:30:23", "brokerBusinessIdentificationCode": {"bankCode": "769-", "countryCode": "IR", "townCode": "98", "branchCode": "001"}, "freeText": "                  ", "giveUpBrokerId": ""}}, {"id": 907907414858268700, "internalSubscriberReference": "112TX_3uGBBRRjaB", "accountId": "API-GATEWAY", "state": "PARTIAL_TRADED", "securityId": "IRO1PSHZ0001", "type": "LIMIT", "side": "BUY", "sequenceId": 2019102800000000000, "priorityDateTime": "2019-10-28T11:50:17.710001", "price": "2.000", "quantity": 50, "validityQualifier": "DAY", "brokerId": "112", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "1", "brokerOrderEntryDateTime": "2019-10-28T08:31:03", "brokerBusinessIdentificationCode": {"bankCode": "112-", "countryCode": "IR", "townCode": "98", "branchCode": "001"}, "freeText": "                  ", "giveUpBrokerId": ""}, "tradeSequenceId": 1}, {"id": 907907414858334200, "internalSubscriberReference": "769RE80004821410", "accountId": "API-GATEWAY", "state": "FULLY_TRADED", "securityId": "IRO1PSHZ0001", "type": "LIMIT", "side": "SELL", "sequenceId": 2019102800000000000, "priorityDateTime": "2019-10-28T11:50:17.71", "price": "2.000", "quantity": 0, "validityQualifier": "DAY", "brokerId": "769", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "1", "brokerOrderEntryDateTime": "2019-10-28T08:30:23", "brokerBusinessIdentificationCode": {"bankCode": "769-", "countryCode": "IR", "townCode": "98", "branchCode": "001"}, "freeText": "                  ", "giveUpBrokerId": ""}, "tradeSequenceId": 1}, {"id": 907907414858399700, "internalSubscriberReference": "112TX_3uGBBRRjaB", "accountId": "API-GATEWAY", "state": "ADDED_IN_QUEUE", "securityId": "IRO1PSHZ0001", "type": "LIMIT", "side": "BUY", "sequenceId": 2019102800000000000, "priorityDateTime": "2019-10-28T11:50:17.710001", "price": "2.000", "quantity": 50, "validityQualifier": "DAY", "brokerId": "112", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "1", "brokerOrderEntryDateTime": "2019-10-28T08:31:03", "brokerBusinessIdentificationCode": {"bankCode": "112-", "countryCode": "IR", "townCode": "98", "branchCode": "001"}, "freeText": "                  ", "giveUpBrokerId": ""}}, {"id": 907907414858465300, "internalSubscriberReference": "112TX_3uGBBRRjaB", "accountId": "API-GATEWAY", "state": "FULLY_TRADED", "securityId": "IRO1PSHZ0001", "type": "LIMIT", "side": "BUY", "sequenceId": 2019102800000000000, "priorityDateTime": "2019-10-28T11:50:17.710001", "price": "2.000", "quantity": 0, "validityQualifier": "DAY", "brokerId": "112", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "1", "brokerOrderEntryDateTime": "2019-10-28T08:31:03", "brokerBusinessIdentificationCode": {"bankCode": "112-", "countryCode": "IR", "townCode": "98", "branchCode": "001"}, "freeText": "                  ", "giveUpBrokerId": ""}, "tradeSequenceId": 2}, {"id": 907907414858530800, "internalSubscriberReference": "112TL_2TkBBRRjaB", "accountId": "API-GATEWAY", "state": "PARTIAL_TRADED", "securityId": "IRO1PSHZ0001", "type": "LIMIT", "side": "SELL", "sequenceId": 2019102800000000000, "priorityDateTime": "2019-10-28T11:50:17.710002", "price": "2.000", "quantity": 100, "validityQualifier": "DAY", "brokerId": "112", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "1", "brokerOrderEntryDateTime": "2019-10-28T08:38:34", "brokerBusinessIdentificationCode": {"bankCode": "112-", "countryCode": "IR", "townCode": "98", "branchCode": "001"}, "freeText": "                  ", "giveUpBrokerId": ""}, "tradeSequenceId": 2}, {"id": 907907414858596400, "internalSubscriberReference": "112TL_2TkBBRRjaB", "accountId": "API-GATEWAY", "state": "ADDED_IN_QUEUE", "securityId": "IRO1PSHZ0001", "type": "LIMIT", "side": "SELL", "sequenceId": 2019102800000000000, "priorityDateTime": "2019-10-28T11:50:17.710002", "price": "2.000", "quantity": 100, "validityQualifier": "DAY", "brokerId": "112", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "1", "brokerOrderEntryDateTime": "2019-10-28T08:38:34", "brokerBusinessIdentificationCode": {"bankCode": "112-", "countryCode": "IR", "townCode": "98", "branchCode": "001"}, "freeText": "                  ", "giveUpBrokerId": ""}}, {"id": 907907414858661900, "internalSubscriberReference": "3311+IMDAw+4tPBA", "accountId": "API-GATEWAY", "state": "PARTIAL_TRADED", "securityId": "IRO1PSHZ0001", "type": "LIMIT", "side": "BUY", "sequenceId": 2019102800000000000, "priorityDateTime": "2019-10-28T11:50:17.710003", "price": "2.000", "quantity": 50, "validityQualifier": "DAY", "brokerId": "331", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "1", "brokerOrderEntryDateTime": "2019-10-28T08:33:59", "brokerBusinessIdentificationCode": {"bankCode": "331-", "countryCode": "IR", "townCode": "98", "branchCode": "001"}, "freeText": "                  ", "giveUpBrokerId": ""}, "tradeSequenceId": 3}, {"id": 907907414858727400, "internalSubscriberReference": "112TL_2TkBBRRjaB", "accountId": "API-GATEWAY", "state": "FULLY_TRADED", "securityId": "IRO1PSHZ0001", "type": "LIMIT", "side": "SELL", "sequenceId": 2019102800000000000, "priorityDateTime": "2019-10-28T11:50:17.710002", "price": "2.000", "quantity": 0, "validityQualifier": "DAY", "brokerId": "112", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "1", "brokerOrderEntryDateTime": "2019-10-28T08:38:34", "brokerBusinessIdentificationCode": {"bankCode": "112-", "countryCode": "IR", "townCode": "98", "branchCode": "001"}, "freeText": "                  ", "giveUpBrokerId": ""}, "tradeSequenceId": 3}, {"id": 907907414858793000, "internalSubscriberReference": "3311+IMDAw+4tPBA", "accountId": "API-GATEWAY", "state": "ADDED_IN_QUEUE", "securityId": "IRO1PSHZ0001", "type": "LIMIT", "side": "BUY", "sequenceId": 2019102800000000000, "priorityDateTime": "2019-10-28T11:50:17.710003", "price": "2.000", "quantity": 50, "validityQualifier": "DAY", "brokerId": "331", "shareholderId": "**************", "origin": "1", "clearingData": {"traderId": "********", "traderOrderNumber": "1", "brokerOrderEntryDateTime": "2019-10-28T08:33:59", "brokerBusinessIdentificationCode": {"bankCode": "331-", "countryCode": "IR", "townCode": "98", "branchCode": "001"}, "freeText": "                  ", "giveUpBrokerId": ""}}], "page": {"size": 29, "number": 0, "total": 1}, "element": {"count": 27, "offset": 0, "total": 27}}