import {TestBed, TestModuleMetadata} from '@angular/core/testing';
import {Http} from '@http';
import {HttpClient, provideHttpClient, withInterceptorsFromDi} from '@angular/common/http';
import {HttpTestingController, provideHttpClientTesting} from '@angular/common/http/testing';
import {ProviderToken} from '@angular/core';
import {environment} from '../../src/environments/environment';

export class ServiceHarness<T> {
    private readonly _service: T;
    get service() { return this._service; }

    private readonly _httpTestingController: HttpTestingController;

    constructor(service: ProviderToken<any>, moduleDef?: TestModuleMetadata) {
        TestBed.configureTestingModule({
            ...moduleDef,
            imports: [],
            providers: [
                ...(moduleDef?.providers ?? []),
                provideHttpClient(withInterceptorsFromDi()),
                provideHttpClientTesting()
            ]
        });

        Http.httpClient = TestBed.inject(HttpClient);
        this._service = TestBed.inject(service);
        this._httpTestingController = TestBed.inject(HttpTestingController);
    }

    request(path: string, response: any, method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'): void {
        const req = this._httpTestingController.expectOne(environment.apiBaseUrl + path);
        expect(req.request.method).toEqual(method);
        req.flush(response);
        this._httpTestingController.verify();
    }

    post(path: string, response: any): void {
        this.request(path, response, 'POST');
    }

    get(path: string, response: any): void {
        this.request(path, response, 'GET');
    }

    put(path: string, response: any): void {
        this.request(path, response, 'PUT');
    }

    delete(path: string, response: any): void {
        this.request(path, response, 'DELETE');
    }

    patch(path: string, response: any): void {
        this.request(path, response, 'PATCH');
    }
}
