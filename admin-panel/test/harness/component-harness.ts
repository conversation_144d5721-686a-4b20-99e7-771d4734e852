import {ComponentFixture, TestBed, TestModuleMetadata} from '@angular/core/testing';
import {Type} from '@angular/core';
import {customMatchers} from '@test/harness/matchers/custom-matchers';
import {GetElement} from '@test/harness/get-element';
import {CreateElement} from '@test/harness/create-element';

export class ComponentHarness<T> {
    createElement = CreateElement;

    get component(): T { return this._fixture.componentInstance; }

    private _fixture: ComponentFixture<T>;
    get fixture(): ComponentFixture<T> { return this._fixture; }

    get get() { return new GetElement<T>(this._fixture).get; }

    constructor(component: Type<T>, moduleDef: TestModuleMetadata & { detectChanges?: boolean }) {
       this._beforeEach(component, moduleDef);
    }

    keyPress(option: string | object): void {
        if (typeof option === 'string') {
            option = {code: option};
        }

        document.dispatchEvent(new KeyboardEvent('keydown', option));
        document.dispatchEvent(new KeyboardEvent('keyup', option));

        this.fixture.detectChanges();
    }

    private _beforeEach(component: Type<T>, moduleDef: TestModuleMetadata & { detectChanges?: boolean }) {
        TestBed.configureTestingModule(moduleDef)
            .compileComponents();

        jasmine.addMatchers(customMatchers);

        this._fixture = TestBed.createComponent(component);

        if (moduleDef.detectChanges || moduleDef.detectChanges === undefined) {
            this._fixture.detectChanges();
        }
    }

    async detectChanges() {
        this._fixture.detectChanges();
        await this._fixture.whenStable();
    }
}
