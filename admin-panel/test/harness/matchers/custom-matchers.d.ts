declare namespace jasmine {
    export interface Matchers<T> {
        toBeExists(): boolean;
        toHaveAttribute(attributeName: string, value?: string): boolean;
        toHaveChild(selector: string): boolean;
        toHaveContained(value: string): boolean;
        toHaveClass(className: string): boolean;
        toHaveStyle(styleName: string, value?: string): boolean;
        toHaveListener(listener: string, value?: string): boolean;
    }
}
