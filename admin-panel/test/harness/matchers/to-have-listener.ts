import CustomMatcherResult = jasmine.CustomMatcherResult;
import CustomMatcher = jasmine.CustomMatcher;
import MatchersUtil = jasmine.MatchersUtil;
import {GetElement} from '@test/harness/get-element';

export function toHaveListener(matchersUtil: MatchersUtil): CustomMatcher {
    return {
        compare(actual: GetElement, listener: string, value: string): CustomMatcherResult {
            const result = {pass: null, message: null};
            const element: HTMLElement = actual instanceof GetElement ? actual.nativeElement : actual;

            result.pass = !!actual.query[0].listeners.find(item => item.name === listener)

            if (result.pass) {
                result.message = '';
            } else if (element.outerHTML.includes(`ng-reflect-${listener}="${value}"`)) {
                result.message = '';
                result.pass = true;
            } else {
                result.message = `Expected ${element.outerHTML} to have bounded listener ${listener}, but it doesn't.`
            }

            return result;
        }
    }
}
